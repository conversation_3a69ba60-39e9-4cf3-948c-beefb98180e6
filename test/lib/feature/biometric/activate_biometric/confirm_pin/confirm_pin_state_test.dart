import 'package:evoapp/feature/biometric/activate_biometric/confirm_pin/confirm_pin_cubit.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('subtype should be instance of [ConfirmPinState]', () {
    expect(ConfirmPinInitState(), isA<ConfirmPinState>());
    expect(ConfirmPinLoadingState(), isA<ConfirmPinState>());
    expect(ConfirmPinSuccess(biometricToken: ''), isA<ConfirmPinState>());
    expect(ConfirmPinBadRequest(userMessage: ''), isA<ConfirmPinState>());
    expect(ConfirmPinFailure(error: ErrorUIModel()), isA<ConfirmPinState>());
  });

  test('ConfirmPinSuccess should hold correct data', () {
    const String mockToken = 'mock_token';
    final ConfirmPinSuccess state = ConfirmPinSuccess(biometricToken: mockToken);
    expect(state.biometricToken, mockToken);
  });

  test('ConfirmPinBadRequest should hold correct data', () {
    const String mockUserMessage = 'mock_user_message';
    final ConfirmPinBadRequest state = ConfirmPinBadRequest(userMessage: mockUserMessage);
    expect(state.userMessage, mockUserMessage);
  });

  test('ConfirmPinFailure should hold correct data', () {
    final ErrorUIModel error = ErrorUIModel();
    final ConfirmPinFailure state = ConfirmPinFailure(error: error);
    expect(state.error, error);
  });
}
