import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/user_repo.dart';
import 'package:evoapp/data/response/biometric_token_entity.dart';
import 'package:evoapp/feature/biometric/activate_biometric/confirm_pin/confirm_pin_cubit.dart';
import 'package:evoapp/feature/biometric/mock/mock_biometric_token_use_case.dart';
import 'package:evoapp/feature/pin/mock/mock_pin_use_case.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../constant.dart';
import '../../../../../util/test_util.dart';

class MockUserRepo extends Mock implements UserRepo {}

void main() {
  late ConfirmPinCubit cubit;
  late UserRepo mockUserRepo;
  const String mockPin = 'mock_pin';

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
  });

  setUp(() {
    mockUserRepo = MockUserRepo();
    cubit = ConfirmPinCubit(userRepo: mockUserRepo);

    when(() => mockUserRepo.getBiometricTokenByPin(
        pin: any(named: 'pin'), mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async {
      return BiometricTokenEntity.fromBaseResponse(BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: await TestUtil.getResponseMock(getMockBiometricTokenFileNameByCase(
          MockTestBiometricTokenUseCase.getBiometricTokenSuccess,
        )),
      ));
    });
  });

  test('init state', () {
    expect(cubit.state, isA<ConfirmPinInitState>());
  });

  group('verify [getBiometricTokenByPin]', () {
    blocTest<ConfirmPinCubit, ConfirmPinState>(
        'should emit [ConfirmPinSuccess] when [getBiometricTokenByPin] return status_code = 200',
        build: () => cubit,
        act: (_) => cubit.getBiometricTokenByPin(mockPin),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <TypeMatcher<ConfirmPinState>>[
              isA<ConfirmPinLoadingState>(),
              isA<ConfirmPinSuccess>().having(
                (ConfirmPinSuccess state) => state.biometricToken,
                'verify biometric_token',
                'mock_biometric_token',
              ),
            ]);

    blocTest<ConfirmPinCubit, ConfirmPinState>(
        'should emit [ConfirmPinBadRequest] when [getBiometricTokenByPin] return status_code = 400',
        setUp: () {
          when(() => mockUserRepo.getBiometricTokenByPin(
              pin: any(named: 'pin'), mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async {
            return BiometricTokenEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.BAD_REQUEST,
              response: await TestUtil.getResponseMock(getMockPinFileNameByCase(
                MockPinUseCase.getVerifyPinInvalidCredential,
              )),
            ));
          });
        },
        build: () => cubit,
        act: (_) => cubit.getBiometricTokenByPin(mockPin),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <TypeMatcher<ConfirmPinState>>[
              isA<ConfirmPinLoadingState>(),
              isA<ConfirmPinBadRequest>().having(
                (ConfirmPinBadRequest state) => state.userMessage,
                'verify user_message',
                'verify_pin_invalid_credential',
              ),
            ]);

    blocTest<ConfirmPinCubit, ConfirmPinState>(
        'should emit [ConfirmPinFailure] when [getBiometricTokenByPin] return occurred unknown value',
        setUp: () {
          when(() => mockUserRepo.getBiometricTokenByPin(
              pin: any(named: 'pin'), mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async {
            return BiometricTokenEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.UNKNOWN_ERRORS,
              response: <String, dynamic>{},
            ));
          });
        },
        build: () => cubit,
        act: (_) => cubit.getBiometricTokenByPin(mockPin),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <TypeMatcher<ConfirmPinState>>[
              isA<ConfirmPinLoadingState>(),
              isA<ConfirmPinFailure>().having(
                (ConfirmPinFailure state) => state.error.statusCode,
                'verify status_code',
                CommonHttpClient.UNKNOWN_ERRORS,
              ),
            ]);
  });
}
