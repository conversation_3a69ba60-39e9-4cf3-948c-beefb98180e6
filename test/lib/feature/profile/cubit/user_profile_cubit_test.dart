import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/user_repo.dart';
import 'package:evoapp/data/response/user_entity.dart';
import 'package:evoapp/data/response/user_information_entity.dart';
import 'package:evoapp/feature/profile/cubit/user_profile_cubit.dart';
import 'package:evoapp/feature/profile/mock/mock_profile_use_case.dart';
import 'package:evoapp/model/user_info_notifier.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../constant.dart';
import '../../../../util/test_util.dart';

class MockUserRepo extends Mock implements UserRepo {}

class MockAppState extends Mock implements AppState {
  @override
  UserInfoNotifier userInfo = UserInfoNotifier(null);
}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

void main() {
  late UserProfileCubit cubit;
  late UserRepo mockUserRepo;
  late EvoLocalStorageHelper mockStorageHelper;
  late AppState mockAppState;
  late UserInformationEntity? mockUserProfile;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
  });

  setUp(() {
    mockUserRepo = MockUserRepo();
    mockStorageHelper = MockEvoLocalStorageHelper();
    mockAppState = MockAppState();

    cubit = UserProfileCubit(
      userRepo: mockUserRepo,
      appState: mockAppState,
      localStorageHelper: mockStorageHelper,
    );
  });

  test('verify init state', () {
    expect(cubit.state, isA<UserProfileInitial>());
  });

  group('Test fetchUserInfoFromServer', () {
    setUp(() async {
      final Map<String, dynamic> response =
          await TestUtil.getResponseMock(getMockProfileFileNameByCase(
        MockProfileUseCase.getUserInformationSuccess,
      ));

      when(() => mockUserRepo.getUserInfo(
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async {
        final UserEntity entity = UserEntity.fromBaseResponse(BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: response,
        ));
        mockUserProfile = entity.userInformation;
        return entity;
      });

      when(() => mockStorageHelper.setUserFullName(any())).thenAnswer((_) async {});
    });

    blocTest<UserProfileCubit, UserProfileState>(
      'should update cache data and emit UserProfileLoadedSuccess when status code = 200',
      wait: TestConstant.blocEmitStateDelayDuration,
      build: () => cubit,
      act: (_) => cubit.fetchUserInfoFromServer(),
      expect: () => <TypeMatcher<UserProfileState>>[
        isA<UserProfileLoading>(),
        isA<UserProfileLoadedSuccess>().having(
          (UserProfileLoadedSuccess state) => state.user,
          'verify user',
          mockUserProfile,
        ),
      ],
      verify: (_) {
        final String? capturedFullName =
            verify(() => mockStorageHelper.setUserFullName(captureAny())).captured.first as String?;
        expect(capturedFullName, 'user_full_name');
        expect(mockAppState.userInfo.value, mockUserProfile);
      },
    );

    blocTest<UserProfileCubit, UserProfileState>(
      'should emit UserProfileLoadedFail when status code not 200',
      wait: TestConstant.blocEmitStateDelayDuration,
      setUp: () {
        when(() => mockUserRepo.getUserInfo(
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return UserEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: <String, dynamic>{},
          ));
        });
      },
      build: () => cubit,
      act: (_) => cubit.fetchUserInfoFromServer(),
      expect: () => <TypeMatcher<UserProfileState>>[
        isA<UserProfileLoading>(),
        isA<UserProfileLoadedFail>().having(
          (UserProfileLoadedFail state) => state.error?.statusCode,
          'verify status_code = 400',
          CommonHttpClient.BAD_REQUEST,
        ),
      ],
      verify: (_) {
        verifyNever(() => mockStorageHelper.setUserFullName(any()));
        expect(mockAppState.userInfo.value, null);
      },
    );
  });

  group('Test getLocalUserInfo', () {
    const String mockFullName = 'mock_user_full_name';
    const String mockPhoneNumber = 'mock_phone_number';

    blocTest<UserProfileCubit, UserProfileState>(
        'should get data from localStorage and update AppState data',
        wait: TestConstant.blocEmitStateDelayDuration,
        setUp: () {
          when(() => mockStorageHelper.getUserFullName()).thenAnswer((_) async => mockFullName);
          when(() => mockStorageHelper.getUserPhoneNumber())
              .thenAnswer((_) async => mockPhoneNumber);
        },
        build: () => cubit,
        act: (_) => cubit.getLocalUserInfo(),
        expect: () => <TypeMatcher<UserProfileState>>[
              isA<UserProfileLoading>(),
              isA<UserProfileLoadedSuccess>()
                  .having(
                    (UserProfileLoadedSuccess state) => state.user?.fullName,
                    'verify full_name value',
                    mockFullName,
                  )
                  .having(
                    (UserProfileLoadedSuccess state) => state.user?.phoneNumber,
                    'verify phoneNumber value',
                    mockPhoneNumber,
                  ),
            ],
        verify: (_) {
          expect(
              mockAppState.userInfo.value,
              isA<UserInformationEntity>()
                  .having((UserInformationEntity user) => user.fullName, 'verify full_name value',
                      mockFullName)
                  .having((UserInformationEntity user) => user.phoneNumber,
                      'verify phone_number value', mockPhoneNumber));
        });

    blocTest<UserProfileCubit, UserProfileState>(
        'should be null fullName and phoneNumber if localStorage return null',
        wait: TestConstant.blocEmitStateDelayDuration,
        setUp: () {
          when(() => mockStorageHelper.getUserFullName()).thenAnswer((_) async => null);
          when(() => mockStorageHelper.getUserPhoneNumber()).thenAnswer((_) async => null);
        },
        build: () => cubit,
        act: (_) => cubit.getLocalUserInfo(),
        expect: () => <TypeMatcher<UserProfileState>>[
              isA<UserProfileLoading>(),
              isA<UserProfileLoadedSuccess>()
                  .having(
                    (UserProfileLoadedSuccess state) => state.user?.fullName,
                    'verify full_name is null',
                    null,
                  )
                  .having(
                    (UserProfileLoadedSuccess state) => state.user?.phoneNumber,
                    'verify phone_number is null',
                    null,
                  ),
            ],
        verify: (_) {
          expect(
              mockAppState.userInfo.value,
              isA<UserInformationEntity>()
                  .having((UserInformationEntity user) => user.fullName, 'verify full_name is null',
                      null)
                  .having((UserInformationEntity user) => user.phoneNumber,
                      'verify phone_number is null', null));
        });
  });
}
