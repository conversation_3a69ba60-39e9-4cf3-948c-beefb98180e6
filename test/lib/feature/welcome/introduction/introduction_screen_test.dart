import 'package:evoapp/feature/welcome/introduction/introduction_page.dart';
import 'package:evoapp/feature/welcome/introduction/introduction_screen.dart';
import 'package:evoapp/feature/welcome/introduction/widget/dot_indicator.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/util/share_preference_helper.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../base/evo_page_state_base_test_config.dart';

class MockCommonSharedPreferencesHelper extends Mock implements CommonSharedPreferencesHelper {}

void main() {
  late MockCommonSharedPreferencesHelper mockSharedPreferencesHelper;

  setUpAll(() {
    initConfigEvoPageStateBase();

    mockSharedPreferencesHelper = MockCommonSharedPreferencesHelper();
    getIt.registerSingleton<CommonSharedPreferencesHelper>(mockSharedPreferencesHelper);
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();
    when(() => mockSharedPreferencesHelper.setPassedTutorial(any())).thenAnswer((_) async {});
  });

  tearDownAll(() {
    getIt.reset();
  });

  buildWidget(WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: const IntroductionScreen(),
      ),
    );
  }

  IntroductionPage getPage(WidgetTester tester) {
    return tester.widget(find.byType(IntroductionPage));
  }

  Future<void> dragLeft(WidgetTester tester) async {
    await tester.drag(find.byType(PageView), const Offset(-500.0, 0.0));
    await tester.pumpAndSettle();
  }

  group('IntroductionScreen', () {
    testWidgets('should render initial widgets correctly', (WidgetTester tester) async {
      await buildWidget(tester);

      // Verify branding exists
      verify(
        () => evoImageProvider.asset(
          EvoImages.imgBrandName,
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
        ),
      ).called(1);

      // Verify dot indicator exists
      expect(find.byType(DotIndicator), findsOneWidget);

      // Verify PageView exists
      expect(find.byType(PageView), findsOneWidget);

      // Verify first introduction page is shown
      expect(find.byType(IntroductionPage), findsOneWidget);

      // Verify skip button exists
      expect(find.text(EvoStrings.ctaSkip), findsOneWidget);
    });

    test('goNamed should navigate to introduction screen', () {
      IntroductionScreen.goNamed();

      verify(() => mockNavigatorContext.goNamed(Screen.introductionScreen.name)).called(1);
    });

    testWidgets('should navigate to next page when swiping', (WidgetTester tester) async {
      await buildWidget(tester);
      expect(getPage(tester).image, EvoImages.imgIntroduction1);

      await dragLeft(tester);

      expect(getPage(tester).image, EvoImages.imgIntroduction2);
    });

    testWidgets('should call setPassedTutorial and navigate when skip button is tapped',
        (WidgetTester tester) async {
      await buildWidget(tester);

      // Tap skip button
      await tester.tap(find.text(EvoStrings.ctaSkip));
      await tester.pumpAndSettle();

      // Verify shared preferences was called with correct value
      verify(() => mockSharedPreferencesHelper.setPassedTutorial(true)).called(1);

      // Verify navigation to welcome screen
      verify(() => mockNavigatorContext.goNamed(Screen.welcomeScreen.name)).called(1);
    });

    testWidgets('should automatically advance pages after 10s', (WidgetTester tester) async {
      await buildWidget(tester);
      expect(getPage(tester).image, EvoImages.imgIntroduction1);

      // Advance time by 10 seconds + animation duration
      await tester.pump(const Duration(seconds: 10));
      await tester.pump(IntroductionScreen.animationDuration);

      // Verify page changed
      expect(getPage(tester).image, EvoImages.imgIntroduction2);

      // Advance time by 10 seconds + animation duration
      await tester.pump(const Duration(seconds: 10));
      await tester.pump(IntroductionScreen.animationDuration);

      // Verify page changed
      expect(getPage(tester).image, EvoImages.imgIntroduction3);
    });

    testWidgets('should use different colors for each page', (WidgetTester tester) async {
      await buildWidget(tester);

      BoxDecoration getDecoration() {
        final AnimatedContainer background = tester.firstWidget(find.byType(AnimatedContainer));
        return background.decoration as BoxDecoration;
      }

      // color of page 1
      expect(getDecoration().color, evoColors.primaryBase);

      // color of page 2
      await dragLeft(tester);
      expect(getDecoration().color, evoColors.secondaryBase);

      // color of page 3
      await dragLeft(tester);
      expect(getDecoration().color, evoColors.accentBase);
    });

    testWidgets('should call setPassedTutorial and navigate when last page button is tapped',
        (WidgetTester tester) async {
      await buildWidget(tester);

      await dragLeft(tester);
      await dragLeft(tester);

      final IntroductionPage lastPage = getPage(tester);
      expect(lastPage.image, EvoImages.imgIntroduction3);
      expect(lastPage.onSkip, isNotNull);

      // Tap skip button ( available on last page)
      await tester.tap(find.text(EvoStrings.ctaSkip));
      await tester.pumpAndSettle();

      // Verify shared preferences was called with correct value
      verify(() => mockSharedPreferencesHelper.setPassedTutorial(true)).called(1);

      // Verify navigation to welcome screen
      verify(() => mockNavigatorContext.goNamed(Screen.welcomeScreen.name)).called(1);
    });
  });
}
