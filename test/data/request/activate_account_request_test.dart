import 'package:evoapp/data/request/activate_account_request.dart';
import 'package:evoapp/feature/account_activation/handler/account_activation_ui_handler.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ActivateAccountRequest', () {
    test('base class toJson should include type', () {
      final TestActivateAccountRequest request = TestActivateAccountRequest();

      expect(
        request.toJson(),
        equals(<String, String>{
          'type': AccountActivationType.none.value,
        }),
      );
    });
  });

  group('ActivateAccountVerifyOTPRequest', () {
    const String mockOtp = '123456';
    const String mockSessionToken = 'mock_session_token';

    test('should have correct type', () {
      final ActivateAccountVerifyOTPRequest request = ActivateAccountVerifyOTPRequest(
        otp: mockOtp,
        sessionToken: mockSessionToken,
      );

      expect(request.type, equals(AccountActivationType.verifyOTP));
    });

    test('toJson should include type and otp', () {
      final ActivateAccountVerifyOTPRequest request = ActivateAccountVerifyOTPRequest(
        otp: mockOtp,
        sessionToken: mockSessionToken,
      );

      expect(
        request.toJson(),
        equals(<String, dynamic>{
          'type': AccountActivationType.verifyOTP.value,
          'otp': mockOtp,
        }),
      );
    });
  });

  group('ActivateAccountVerifyPhoneNumberRequest', () {
    const String phoneNumber = '123456';

    test('should have correct type', () {
      final ActivateAccountVerifyPhoneNumberRequest request =
          ActivateAccountVerifyPhoneNumberRequest(phoneNumber: phoneNumber);

      expect(request.type, equals(AccountActivationType.none));
    });

    test('toJson should include type and phone number', () {
      final ActivateAccountVerifyPhoneNumberRequest request =
          ActivateAccountVerifyPhoneNumberRequest(phoneNumber: phoneNumber);

      expect(
        request.toJson(),
        equals(<String, dynamic>{
          'type': AccountActivationType.none.value,
          'phone_number': phoneNumber,
        }),
      );
    });
  });

  group('ActivateAccountCreateUsernameRequest', () {
    const String mockUsername = 'test_username';
    const String mockSessionToken = 'mock_session_token';

    test('should have correct type', () {
      final ActivateAccountCreateUsernameRequest request = ActivateAccountCreateUsernameRequest(
        username: mockUsername,
        sessionToken: mockSessionToken,
      );

      expect(request.type, equals(AccountActivationType.createUsername));
    });

    test('toJson should include type and username', () {
      final ActivateAccountCreateUsernameRequest request = ActivateAccountCreateUsernameRequest(
        username: mockUsername,
        sessionToken: mockSessionToken,
      );

      expect(
        request.toJson(),
        equals(<String, dynamic>{
          'type': AccountActivationType.createUsername.value,
          'user_name': mockUsername,
        }),
      );
    });

    test('constructor should set required fields', () {
      final ActivateAccountCreateUsernameRequest request = ActivateAccountCreateUsernameRequest(
        username: mockUsername,
        sessionToken: mockSessionToken,
      );

      expect(request.username, equals(mockUsername));
      expect(request.sessionToken, equals(mockSessionToken));
    });
  });
}

// Helper class for testing the abstract base class
class TestActivateAccountRequest extends ActivateAccountRequest {
  TestActivateAccountRequest({super.sessionToken});

  @override
  AccountActivationType get type => AccountActivationType.none;
}
