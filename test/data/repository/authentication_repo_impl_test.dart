import 'package:collection/collection.dart';
import 'package:evoapp/data/constants.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/repository/authentication_repo_impl.dart';
import 'package:evoapp/data/request/activate_account_request.dart';
import 'package:evoapp/data/request/reset_pin_request.dart';
import 'package:evoapp/data/response/account_activation_entity.dart';
import 'package:evoapp/data/response/reset_pin_entity.dart';
import 'package:evoapp/data/response/sign_in_entity.dart';
import 'package:evoapp/feature/pin/mock/mock_pin_use_case.dart';
import 'package:evoapp/feature/profile/profile_screen/cubit/sign_out/mock_signout_file_name.dart';
import 'package:evoapp/feature/verify_otp/mock/mock_verify_otp_use_case.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/common_request_option.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';
import '../../util/test_util.dart';

class MockCommonHttpClient extends Mock implements CommonHttpClient {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockCommonUtilFunction extends Mock implements CommonUtilFunction {}

class MockActivateAccountRequest extends Mock implements ActivateAccountRequest {}

void main() {
  test('Test SignInRequestParam', () {
    expect(SignInRequestParam.type.value, 'type');
    expect(SignInRequestParam.phoneNumber.value, 'phone_number');
    expect(SignInRequestParam.otp.value, 'otp');
    expect(SignInRequestParam.pin.value, 'pin');
    expect(SignInRequestParam.refreshToken.value, 'refresh_token');
    expect(SignInRequestParam.biometricToken.value, 'biometric_token');
  });

  test('Test TypeLogin', () {
    expect(TypeLogin.otp.value, 'otp');
    expect(TypeLogin.verifyOTP.value, 'verify_otp');
    expect(TypeLogin.verifyPin.value, 'verify_pin');
    expect(TypeLogin.refreshToken.value, 'refresh_token');
    expect(TypeLogin.biometricToken.value, 'biometric_token');
  });

  test('Test ResetPinType', () {
    expect(ResetPinType.none.value, 'none');
    expect(ResetPinType.verifyOTP.value, 'verify_otp');
    expect(ResetPinType.changePin.value, 'change_pin');
    expect(ResetPinType.faceAuth.value, 'face_auth');
  });

  group('Test AuthenticationRepo', () {
    late AuthenticationRepoImpl authenticationRepo;
    final CommonHttpClient evoHttpClient = MockCommonHttpClient();
    late EvoLocalStorageHelper evoLocalStorageHelper;
    final CommonHttpClient nonAuthenticationEvoHttpClient = MockCommonHttpClient();
    final AppState appState = AppState();
    late ActivateAccountRequest mockRequest;

    const String fakeSessionToken = 'fakeSessionToken';
    const String fakeDeviceToken = 'fakeDeviceToken';
    const String fakeRefreshToken = 'fakeRefreshToken';

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
      mockLoggingRepo();
    });

    setUp(() {
      appState.userToken = null;

      evoLocalStorageHelper = MockEvoLocalStorageHelper();
      authenticationRepo = AuthenticationRepoImpl(
        evoHttpClient: evoHttpClient,
        evoLocalStorageHelper: evoLocalStorageHelper,
        nonAuthenticationEvoHttpClient: nonAuthenticationEvoHttpClient,
        appState: appState,
      );

      mockRequest = MockActivateAccountRequest();

      when(() => evoLocalStorageHelper.setDeviceToken(any())).thenAnswer((_) async {});
      when(() => evoLocalStorageHelper.setBiometricToken(any())).thenAnswer((_) async {});
      when(() => evoLocalStorageHelper.getDeviceToken()).thenAnswer((_) async => null);
      when(() => mockRequest.toJson()).thenAnswer((_) => <String, dynamic>{});
    });

    void mockCommonUtilFunction() {
      getIt.registerLazySingleton<CommonUtilFunction>(() => MockCommonUtilFunction());
      when(() => commonUtilFunction.handleSignInSucceedData(
            accessToken: any(named: 'accessToken'),
            userId: any(named: 'userId'),
            notificationAuthKey: any(named: 'notificationAuthKey'),
          )).thenAnswer((_) async {});
    }

    test('Test constants', () {
      expect(AuthenticationRepoImpl.signInUrl, 'user/signin');
    });

    group('Test signIn', () {
      const TypeLogin type = TypeLogin.otp;
      const String fakePhoneNumber = 'fakePhoneNumber';
      const String fakeOtp = 'fakeOtp';
      const String fakePin = 'fakePin';
      const String fakeBiometricToken = 'fakeBiometricToken';
      const String accessToken = 'access_token';
      const String refreshToken = 'refresh_token';
      const MockConfig fakeMockConfig = MockConfig(
        enable: true,
        fileName: 'signin_success.json',
      );

      setUpAll(() {
        mockCommonUtilFunction();
      });

      tearDownAll(() {
        getIt.unregister<CommonUtilFunction>();
      });

      Future<void> mockSignInSucceed() async {
        final Map<String, dynamic> responseData =
            await TestUtil.getResponseMock('signin_success.json');

        final BaseResponse mockResponse = BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: responseData,
        );

        when(
          () => evoHttpClient.post(
            AuthenticationRepoImpl.signInUrl,
            requestOption: any(named: 'requestOption'),
            data: any(named: 'data'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer((_) async {
          return mockResponse;
        });

        when(() => commonUtilFunction.serialize(
              any<SerializeFunction<SignInEntity>>(),
              originalData: any<dynamic>(named: 'originalData'),
            )).thenAnswer((_) {
          return SignInEntity.fromBaseResponse(mockResponse);
        });
      }

      Future<void> mockSignInFailed() async {
        final Map<String, dynamic> responseData =
            await TestUtil.getResponseMock('signin_invalid_parameters.json');

        final BaseResponse mockResponse = BaseResponse(
          statusCode: CommonHttpClient.BAD_REQUEST,
          response: responseData,
        );

        when(
          () => evoHttpClient.post(
            AuthenticationRepoImpl.signInUrl,
            requestOption: any(named: 'requestOption'),
            data: any(named: 'data'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer((_) async {
          return mockResponse;
        });

        when(() => commonUtilFunction.serialize(
              any<SerializeFunction<SignInEntity>>(),
              originalData: any<dynamic>(named: 'originalData'),
            )).thenAnswer((_) {
          return SignInEntity.fromBaseResponse(mockResponse);
        });
      }

      TypeMatcher<CommonRequestOption> verifyHeaders({
        String? deviceTokenMatcher,
        String? sessionTokenMatcher,
      }) {
        return isA<CommonRequestOption>().having(
          (CommonRequestOption p0) => p0.headers,
          'verify headers',
          isA<Map<String, dynamic>>()
              .having(
                (Map<String, dynamic> p0) => p0[HeaderKey.deviceToken],
                'verify deviceToken',
                deviceTokenMatcher,
              )
              .having(
                (Map<String, dynamic> p0) => p0[HeaderKey.sessionToken],
                'verify sessionToken',
                sessionTokenMatcher,
              ),
        );
      }

      TypeMatcher<Map<String, dynamic>> verifyData({
        required String type,
        String? phoneNumber,
        String? otp,
        String? pin,
        String? refreshToken,
        String? biometricToken,
      }) {
        return isA<Map<String, dynamic>>()
            .having(
              (Map<String, dynamic> p0) => p0[SignInRequestParam.type.value],
              'verify type',
              type,
            )
            .having(
              (Map<String, dynamic> p0) => p0[SignInRequestParam.phoneNumber.value],
              'verify phoneNumber',
              phoneNumber,
            )
            .having(
              (Map<String, dynamic> p0) => p0[SignInRequestParam.otp.value],
              'verify otp',
              otp,
            )
            .having(
              (Map<String, dynamic> p0) => p0[SignInRequestParam.pin.value],
              'verify pin',
              pin,
            )
            .having(
              (Map<String, dynamic> p0) => p0[SignInRequestParam.refreshToken.value],
              'verify refreshToken',
              refreshToken,
            )
            .having(
              (Map<String, dynamic> p0) => p0[SignInRequestParam.biometricToken.value],
              'verify biometricToken',
              biometricToken,
            );
      }

      void verifySignInDataHandling() {
        expect(appState.userToken.accessToken, accessToken);
        expect(appState.userToken.refreshToken, refreshToken);
        verify(() => evoLocalStorageHelper.setDeviceToken('device_token')).called(1);
        verify(() => evoLocalStorageHelper.setBiometricToken('biometric_token')).called(1);
        verify(() => commonUtilFunction.handleSignInSucceedData(
              accessToken: 'access_token',
              userId: 0,
              notificationAuthKey: 'auth_noti_token',
            )).called(1);
      }

      void verifyNotHandleSignInData() {
        expect(appState.userToken.accessToken, isNull);
        expect(appState.userToken.refreshToken, isNull);
        verifyNever(() => evoLocalStorageHelper.setDeviceToken(any()));
        verifyNever(() => evoLocalStorageHelper.setBiometricToken(any()));
        verifyNever(() => commonUtilFunction.handleSignInSucceedData(
              accessToken: any(named: 'accessToken'),
              userId: any(named: 'userId'),
              notificationAuthKey: any(named: 'notificationAuthKey'),
            ));
      }

      /// Test signIn with only required params
      test('Call signIn succeed with only required params', () async {
        mockSignInSucceed();
        final SignInEntity signInEntity = await authenticationRepo.signIn(type);
        expect(signInEntity.statusCode, CommonHttpClient.SUCCESS);
        expect(
          verify(() => evoHttpClient.post(
                AuthenticationRepoImpl.signInUrl,
                requestOption: captureAny(named: 'requestOption'),
                data: captureAny(named: 'data'),
                mockConfig: captureAny(named: 'mockConfig'),
              )).captured,
          <dynamic>[
            verifyHeaders(),
            verifyData(type: TypeLogin.otp.value),
            isNull,
          ],
        );

        verify(() => evoLocalStorageHelper.getDeviceToken()).called(1);
        verifySignInDataHandling();
      });

      test('Call signIn failed with only required params', () async {
        mockSignInFailed();
        final SignInEntity signInEntity = await authenticationRepo.signIn(type);
        expect(signInEntity.statusCode, CommonHttpClient.BAD_REQUEST);
        expect(
          verify(() => evoHttpClient.post(
                AuthenticationRepoImpl.signInUrl,
                requestOption: captureAny(named: 'requestOption'),
                data: captureAny(named: 'data'),
                mockConfig: captureAny(named: 'mockConfig'),
              )).captured,
          <dynamic>[
            verifyHeaders(),
            verifyData(type: TypeLogin.otp.value),
            isNull,
          ],
        );

        verify(() => evoLocalStorageHelper.getDeviceToken()).called(1);
        verifyNotHandleSignInData();
      });

      /// Test signIn with all params
      test('Call signIn succeed with all params', () async {
        mockSignInSucceed();
        final SignInEntity signInEntity = await authenticationRepo.signIn(
          type,
          phoneNumber: fakePhoneNumber,
          otp: fakeOtp,
          pin: fakePin,
          refreshToken: fakeRefreshToken,
          biometricToken: fakeBiometricToken,
          sessionToken: fakeSessionToken,
          mockConfig: fakeMockConfig,
        );
        expect(signInEntity.statusCode, CommonHttpClient.SUCCESS);
        expect(
          verify(() => evoHttpClient.post(
                AuthenticationRepoImpl.signInUrl,
                requestOption: captureAny(named: 'requestOption'),
                data: captureAny(named: 'data'),
                mockConfig: captureAny(named: 'mockConfig'),
              )).captured,
          <dynamic>[
            verifyHeaders(sessionTokenMatcher: fakeSessionToken),
            verifyData(
              type: TypeLogin.otp.value,
              phoneNumber: fakePhoneNumber,
              otp: fakeOtp,
              pin: fakePin,
              refreshToken: fakeRefreshToken,
              biometricToken: fakeBiometricToken,
            ),
            fakeMockConfig,
          ],
        );

        verify(() => evoLocalStorageHelper.getDeviceToken()).called(1);
        verifySignInDataHandling();
      });

      /// Test signIn with deviceToken
      test('Call signIn succeed with deviceToken', () async {
        mockSignInSucceed();
        when(() => evoLocalStorageHelper.getDeviceToken()).thenAnswer((_) async => fakeDeviceToken);
        final SignInEntity signInEntity = await authenticationRepo.signIn(type);
        expect(signInEntity.statusCode, CommonHttpClient.SUCCESS);
        expect(
          verify(() => evoHttpClient.post(
                AuthenticationRepoImpl.signInUrl,
                requestOption: captureAny(named: 'requestOption'),
                data: captureAny(named: 'data'),
                mockConfig: captureAny(named: 'mockConfig'),
              )).captured,
          <dynamic>[
            verifyHeaders(deviceTokenMatcher: fakeDeviceToken),
            verifyData(type: TypeLogin.otp.value),
            isNull,
          ],
        );

        verify(() => evoLocalStorageHelper.getDeviceToken()).called(1);
        verifySignInDataHandling();
      });

      test('should return SignInEntity.unserializable() when serialize() returns null', () async {
        when(() => evoHttpClient.post(
              AuthenticationRepoImpl.signInUrl,
              requestOption: any(named: 'requestOption'),
              data: any(named: 'data'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => BaseResponse(statusCode: null, response: null));
        when(() => commonUtilFunction.serialize(
              any<SerializeFunction<SignInEntity>>(),
              originalData: any<dynamic>(named: 'originalData'),
            )).thenReturn(null);

        final SignInEntity entity = await authenticationRepo.signIn(type);

        expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
      });

      test('should return SignInEntity with correct data', () async {
        mockSignInSucceed();
        final SignInEntity entity1 = await authenticationRepo.signIn(type);

        final List<dynamic> captured = verify(() => commonUtilFunction.serialize(
              captureAny<SerializeFunction<SignInEntity>>(),
              originalData: any<dynamic>(named: 'originalData'),
            )).captured;

        final SerializeFunction<SignInEntity> serializeFunction =
            captured.first as SerializeFunction<SignInEntity>;
        final SignInEntity entity2 = serializeFunction();

        expect(entity1.refreshToken, entity2.refreshToken);
        expect(entity1.accessToken, entity2.accessToken);
        expect(entity1.authNotiToken, entity2.authNotiToken);
        expect(entity1.biometricToken, entity2.biometricToken);
        expect(entity1.deviceToken, entity2.deviceToken);
        expect(entity1.statusCode, entity2.statusCode);
        expect(entity1.userId, entity2.userId);
        expect(entity1.otpResendSecs, entity2.otpResendSecs);
        expect(entity1.otpValiditySecs, entity2.otpValiditySecs);
        expect(entity1.sessionToken, entity2.sessionToken);
        expect(entity1.ekycClientSettings, entity2.ekycClientSettings);
      });
    });

    group('Test refreshToken', () {
      setUpAll(() {
        mockCommonUtilFunction();
      });

      tearDownAll(() {
        getIt.unregister<CommonUtilFunction>();
      });

      Future<void> stubRefreshTokenRequest(
          {required String jsonFileName, required int statusCode}) async {
        final Map<String, dynamic> responseData = await TestUtil.getResponseMock(jsonFileName);

        final BaseResponse mockResponse = BaseResponse(
          statusCode: statusCode,
          response: responseData,
        );

        when(
          () => nonAuthenticationEvoHttpClient.post(
            AuthenticationRepoImpl.signInUrl,
            requestOption: any(named: 'requestOption'),
            data: any(named: 'data'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer((_) async {
          return mockResponse;
        });

        when(() => commonUtilFunction.serialize(
              any<SerializeFunction<SignInEntity>>(),
              originalData: any<dynamic>(named: 'originalData'),
            )).thenAnswer((_) {
          return SignInEntity.fromBaseResponse(mockResponse);
        });
      }

      test('Call refreshToken succeed with given params', () async {
        /// Arrange
        await stubRefreshTokenRequest(
          jsonFileName: 'refresh_token_success.json',
          statusCode: CommonHttpClient.SUCCESS,
        );

        when(() => evoLocalStorageHelper.getDeviceToken()).thenAnswer((_) async => fakeDeviceToken);

        /// Action
        final SignInEntity signInEntity = await authenticationRepo.refreshToken(fakeRefreshToken);
        expect(signInEntity.statusCode, CommonHttpClient.SUCCESS);

        //// capture parameters of the call
        final List<dynamic> capturedData = verify(() => nonAuthenticationEvoHttpClient.post(
              AuthenticationRepoImpl.signInUrl,
              requestOption: captureAny(named: 'requestOption'),
              data: captureAny(named: 'data'),
              mockConfig: captureAny(named: 'mockConfig'),
            )).captured;

        /// Assert the requestOption
        final CommonRequestOption requestOption = capturedData[0] as CommonRequestOption;
        expect(requestOption.headers?[HeaderKey.deviceToken], fakeDeviceToken);

        /// Assert the request Data
        final Map<String, dynamic>? data = capturedData[1] as Map<String, dynamic>?;
        expect(data?['type'], TypeLogin.refreshToken.value);
        expect(data?['refresh_token'], fakeRefreshToken);

        /// Assert the mockConfig
        final MockConfig? mockConfig = capturedData[2] as MockConfig?;
        expect(mockConfig, isNull);

        /// Assert the mockConfig
        verify(() => evoLocalStorageHelper.getDeviceToken()).called(1);

        /// Assert AccessToken, RefreshToken is stored in memory
        expect(appState.userToken.accessToken, signInEntity.accessToken);
        expect(appState.userToken.refreshToken, signInEntity.refreshToken);

        /// Assert DeviceToken is stored in secure storage
        verify(() => evoLocalStorageHelper.setDeviceToken('device_token')).called(1);

        /// Assert handleSignInSucceedData is called with correct parameters
        verify(() => commonUtilFunction.handleSignInSucceedData(
              accessToken: signInEntity.accessToken,
              userId: signInEntity.userId,
              notificationAuthKey: signInEntity.authNotiToken,
            )).called(1);
      });

      test('Call refreshToken failed', () async {
        /// Arrange
        await stubRefreshTokenRequest(
          jsonFileName: 'refresh_token_failure.json',
          statusCode: CommonHttpClient.UNKNOWN_ERRORS,
        );

        /// Action
        final SignInEntity signInEntity = await authenticationRepo.refreshToken(fakeRefreshToken);
        expect(signInEntity.statusCode, CommonHttpClient.UNKNOWN_ERRORS);

        /// Assert AccessToken, RefreshToken is not stored in memory
        expect(appState.userToken.accessToken, isNull);
        expect(appState.userToken.refreshToken, isNull);

        /// Assert DeviceToken is not stored in secure storage
        verifyNever(() => evoLocalStorageHelper.setDeviceToken('device_token'));

        /// Assert handleSignInSucceedData is not called
        verifyNever(
          () => commonUtilFunction.handleSignInSucceedData(
            accessToken: any(named: 'accessToken'),
            userId: any(named: 'userId'),
            notificationAuthKey: any(named: 'notificationAuthKey'),
          ),
        );
      });

      test('Call refreshToken with device_token = NULL', () async {
        /// Arrange
        await stubRefreshTokenRequest(
          jsonFileName: 'refresh_token_success.json',
          statusCode: CommonHttpClient.SUCCESS,
        );

        /// Action
        final SignInEntity signInEntity = await authenticationRepo.refreshToken(fakeRefreshToken);
        expect(signInEntity.statusCode, CommonHttpClient.SUCCESS);

        //// capture parameters of the call
        final List<dynamic> capturedData = verify(() => nonAuthenticationEvoHttpClient.post(
              AuthenticationRepoImpl.signInUrl,
              requestOption: captureAny(named: 'requestOption'),
              data: any(named: 'data'),
              mockConfig: any(named: 'mockConfig'),
            )).captured;

        /// Assert the requestOption
        final CommonRequestOption requestOption = capturedData[0] as CommonRequestOption;
        expect(requestOption.headers?[HeaderKey.deviceToken], isNull);
      });

      test('should return SignInEntity.unserializable() when serialize() return null', () async {
        when(() => nonAuthenticationEvoHttpClient.post(
              AuthenticationRepoImpl.signInUrl,
              requestOption: any(named: 'requestOption'),
              data: any(named: 'data'),
            )).thenAnswer((_) async => BaseResponse(statusCode: null, response: null));
        when(() => commonUtilFunction.serialize(
              any<SerializeFunction<SignInEntity>>(),
              originalData: any<dynamic>(named: 'originalData'),
            )).thenReturn(null);

        final SignInEntity entity = await authenticationRepo.refreshToken('token');

        expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
      });

      test('should return SignInEntity with correct data', () async {
        await stubRefreshTokenRequest(
          jsonFileName: 'refresh_token_success.json',
          statusCode: CommonHttpClient.SUCCESS,
        );
        final SignInEntity entity1 = await authenticationRepo.refreshToken('token');

        final List<dynamic> captured = verify(() => commonUtilFunction.serialize(
              captureAny<SerializeFunction<SignInEntity>>(),
              originalData: any<dynamic>(named: 'originalData'),
            )).captured;

        final SerializeFunction<SignInEntity> serializeFunction =
            captured.first as SerializeFunction<SignInEntity>;
        final SignInEntity entity2 = serializeFunction();

        expect(entity1.refreshToken, entity2.refreshToken);
        expect(entity1.accessToken, entity2.accessToken);
        expect(entity1.authNotiToken, entity2.authNotiToken);
        expect(entity1.biometricToken, entity2.biometricToken);
        expect(entity1.deviceToken, entity2.deviceToken);
        expect(entity1.statusCode, entity2.statusCode);
        expect(entity1.userId, entity2.userId);
        expect(entity1.otpResendSecs, entity2.otpResendSecs);
        expect(entity1.otpValiditySecs, entity2.otpValiditySecs);
        expect(entity1.sessionToken, entity2.sessionToken);
        expect(entity1.ekycClientSettings, entity2.ekycClientSettings);
      });
    });

    group('Test createSignInHeaders', () {
      test('Give sessionToken null, deviceToken null', () {
        final Future<Map<String, dynamic>> headers = authenticationRepo.createSignInHeaders(null);
        expect(
          headers,
          completion(
            isA<Map<String, dynamic>>()
                .having(
                  (Map<String, dynamic> p0) => p0[HeaderKey.sessionToken],
                  'verify sessionToken',
                  isNull,
                )
                .having(
                  (Map<String, dynamic> p0) => p0[HeaderKey.deviceToken],
                  'verify deviceToken',
                  isNull,
                ),
          ),
        );
      });

      test('Give sessionToken NOT null, deviceToken null', () async {
        final Map<String, dynamic> headers =
            await authenticationRepo.createSignInHeaders(fakeSessionToken);
        expect(
          headers,
          isA<Map<String, dynamic>>()
              .having(
                (Map<String, dynamic> p0) => p0[HeaderKey.sessionToken],
                'verify sessionToken',
                fakeSessionToken,
              )
              .having(
                (Map<String, dynamic> p0) => p0[HeaderKey.deviceToken],
                'verify deviceToken',
                isNull,
              ),
        );
      });

      test('Give sessionToken null, deviceToken NOT null', () async {
        when(() => evoLocalStorageHelper.getDeviceToken()).thenAnswer((_) async => fakeDeviceToken);
        final Map<String, dynamic> headers = await authenticationRepo.createSignInHeaders(null);
        expect(
          headers,
          isA<Map<String, dynamic>>()
              .having(
                (Map<String, dynamic> p0) => p0[HeaderKey.sessionToken],
                'verify sessionToken',
                isNull,
              )
              .having(
                (Map<String, dynamic> p0) => p0[HeaderKey.deviceToken],
                'verify deviceToken',
                fakeDeviceToken,
              ),
        );
      });

      test('Give sessionToken NOT null, deviceToken NOT null', () async {
        when(() => evoLocalStorageHelper.getDeviceToken()).thenAnswer((_) async => fakeDeviceToken);
        final Map<String, dynamic> headers =
            await authenticationRepo.createSignInHeaders(fakeSessionToken);
        expect(
          headers,
          isA<Map<String, dynamic>>()
              .having(
                (Map<String, dynamic> p0) => p0[HeaderKey.sessionToken],
                'verify sessionToken',
                fakeSessionToken,
              )
              .having(
                (Map<String, dynamic> p0) => p0[HeaderKey.deviceToken],
                'verify deviceToken',
                fakeDeviceToken,
              ),
        );
      });
    });

    group('verify resetPin', () {
      const String sessionToken = 'mock-session-token';
      final ResetPinRequest request = InitializeResetPinRequest(phoneNumber: 'mock-phone-number');

      setUpAll(() {
        getIt.registerSingleton<CommonUtilFunction>(CommonUtilFunction());
      });

      tearDownAll(() {
        getIt.unregister<CommonUtilFunction>();
      });

      setUp(() {
        when(() => evoHttpClient.patch(
              AuthenticationRepoImpl.resetPinUrl,
              data: any(named: 'data'),
              mockConfig: any(named: 'mockConfig'),
              requestOption: any(named: 'requestOption'),
            )).thenAnswer((_) async {
          final Map<String, dynamic> responseData =
              await TestUtil.getResponseMock(getMockPinFileNameByCase(
            MockPinUseCase.getResetPinInitializeSuccess,
          ));

          return BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: responseData,
          );
        });
      });

      void verifyHttpCalled({required ResetPinRequest request}) {
        final List<dynamic> captures = verify(() => evoHttpClient.patch(
              AuthenticationRepoImpl.resetPinUrl,
              data: captureAny(named: 'data'),
              requestOption: captureAny(named: 'requestOption'),
              mockConfig: any(named: 'mockConfig'),
            )).captured;

        expect(captures[0], equals(request.toJson()));

        if (request.sessionToken != null) {
          expect(
              captures[1],
              isA<CommonRequestOption>().having(
                  (CommonRequestOption reqOptions) => reqOptions.headers, 'verify headers', {
                HeaderKey.sessionToken: sessionToken,
              }));
        }
      }

      group('verify if request is InitializeResetPinRequest', () {
        test('mock is true and get response success', () async {
          final ResetPinEntity entity = await authenticationRepo.resetPin(
            request: request,
            mockConfig: MockConfig(
                enable: true,
                fileName: getMockPinFileNameByCase(
                  MockPinUseCase.getResetPinInitializeSuccess,
                )),
          );

          expect(entity.statusCode, CommonHttpClient.SUCCESS);
          verifyHttpCalled(request: request);
        });

        test('mock is false and get response success', () async {
          final ResetPinEntity entity = await authenticationRepo.resetPin(
            request: request,
          );

          expect(entity.statusCode, CommonHttpClient.SUCCESS);
          verifyHttpCalled(request: request);
        });

        test('mock is false and get response failure', () async {
          when(() => evoHttpClient.patch(
                AuthenticationRepoImpl.resetPinUrl,
                data: any(named: 'data'),
                mockConfig: any(named: 'mockConfig'),
                requestOption: any(named: 'requestOption'),
              )).thenAnswer((_) async {
            return BaseResponse(
              statusCode: CommonHttpClient.BAD_REQUEST,
              response: <String, dynamic>{},
            );
          });

          final ResetPinEntity entity = await authenticationRepo.resetPin(
            request: request,
          );

          expect(entity.statusCode, CommonHttpClient.BAD_REQUEST);
          verifyHttpCalled(request: request);
        });
      });

      group('verify if request is ResetPinVerifyOTPRequest', () {
        test('should have session token when request is ResetPinVerifyOTPRequest', () async {
          final ResetPinRequest request =
              ResetPinVerifyOTPRequest(otp: 'otp', sessionToken: sessionToken);
          await authenticationRepo.resetPin(
              request: request,
              mockConfig: MockConfig(
                  enable: true,
                  fileName: getMockPinFileNameByCase(
                    MockPinUseCase.getResetPinInitializeSuccess,
                  )));
          verifyHttpCalled(request: request);
        });
      });

      test('should return ResetPinEntity.unSerializable() when data format is invalid', () async {
        when(() => evoHttpClient.patch(
              AuthenticationRepoImpl.resetPinUrl,
              requestOption: any(named: 'requestOption'),
              data: any(named: 'data'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => BaseResponse(
              statusCode: null,
              response: <String, dynamic>{
                'data': <String, dynamic>{
                  'session_token': 0,
                },
              },
            ));

        final ResetPinEntity entity = await authenticationRepo.resetPin(request: request);

        expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
      });
    });

    group('verify logOut()', () {
      setUpAll(() {
        getIt.registerSingleton<CommonUtilFunction>(CommonUtilFunction());
      });

      tearDownAll(() {
        getIt.unregister<CommonUtilFunction>();
      });

      Future<BaseResponse> httpRequest() => evoHttpClient.post(
            AuthenticationRepoImpl.logoutUrl,
            mockConfig: any(named: 'mockConfig'),
          );

      test('should make an HTTP request', () async {
        when(httpRequest).thenAnswer((_) async => BaseResponse(statusCode: null, response: null));

        await authenticationRepo.logout();

        verify(httpRequest).called(1);
      });

      test('should make an HTTP request with passed mockConfig', () async {
        when(httpRequest).thenAnswer((_) async => BaseResponse(statusCode: null, response: null));

        final MockConfig config = MockConfig(enable: true);
        await authenticationRepo.logout(mockConfig: config);

        final List<dynamic> captured = verify(() => evoHttpClient.post(
              AuthenticationRepoImpl.logoutUrl,
              mockConfig: captureAny(named: 'mockConfig'),
            )).captured;
        final MockConfig passedConfig = captured.first as MockConfig;
        expect(passedConfig, equals(config));
      });

      test('should return BaseEntity with INVALID_FORMAT when serialize() fails', () async {
        when(httpRequest).thenAnswer((_) async => BaseResponse(
              statusCode: null,
              response: <String, dynamic>{'data': 0},
            ));

        final BaseEntity? entity = await authenticationRepo.logout();

        expect(entity?.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
      });

      test('should return entity with correct data', () async {
        final String mockFile = getSignOutMockFileName(MockSignOutUseCase.success);
        final Map<String, dynamic> response = await TestUtil.getResponseMock(mockFile);

        when(httpRequest).thenAnswer((_) async => BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: response,
            ));

        final BaseEntity? entity = await authenticationRepo.logout();
        expect(entity?.statusCode, CommonHttpClient.SUCCESS);
        expect(entity?.message, response['message']);
        expect(entity?.verdict, response['verdict']);
        expect(DeepCollectionEquality().equals(entity?.data, response['data']), isTrue);
      });
    });

    group('verify activateAccount', () {
      verifyHttpCalled() {
        verify(() => evoHttpClient.post(
              AuthenticationRepoImpl.activateAccountUrl,
              data: any(named: 'data'),
              mockConfig: any(named: 'mockConfig'),
              requestOption: captureAny(named: 'requestOption'),
            )).called(1);
      }

      setUpAll(() {
        getIt.registerSingleton<CommonUtilFunction>(CommonUtilFunction());
      });

      tearDownAll(() {
        getIt.unregister<CommonUtilFunction>();
      });

      setUp(() {
        when(() => evoHttpClient.post(
              AuthenticationRepoImpl.activateAccountUrl,
              data: any(named: 'data'),
              mockConfig: any(named: 'mockConfig'),
              requestOption: any(named: 'requestOption'),
            )).thenAnswer((_) async {
          final Map<String, dynamic> responseData = await TestUtil.getResponseMock(
              getMockVerifyOtpFileNameByCase(MockVerifyOtpUseCase.getSignInOtpSuccess));

          return BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData);
        });
      });

      test('mock is true and get response success', () async {
        final AccountActivationEntity entity = await authenticationRepo.activateAccount(
          request: mockRequest,
          mockConfig: MockConfig(
              enable: true,
              fileName: getMockVerifyOtpFileNameByCase(MockVerifyOtpUseCase.getSignInOtpSuccess)),
        );

        expect(entity.statusCode, CommonHttpClient.SUCCESS);
        verifyHttpCalled();
      });

      test('mock is false and get response success', () async {
        final AccountActivationEntity entity = await authenticationRepo.activateAccount(
          request: mockRequest,
        );
        expect(entity.statusCode, CommonHttpClient.SUCCESS);
        verifyHttpCalled();
      });

      test('mock is false and get response failure', () async {
        when(() => evoHttpClient.post(
              AuthenticationRepoImpl.activateAccountUrl,
              data: any(named: 'data'),
              mockConfig: any(named: 'mockConfig'),
              requestOption: any(named: 'requestOption'),
            )).thenAnswer((_) async {
          return BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: <String, dynamic>{},
          );
        });

        final AccountActivationEntity entity = await authenticationRepo.activateAccount(
          request: mockRequest,
        );

        expect(entity.statusCode, CommonHttpClient.BAD_REQUEST);
        verifyHttpCalled();
      });

      test('should verify requestOption and data parameters', () async {
        const String fakeSessionToken = 'test-session-token';
        final Map<String, dynamic> fakeRequestData = {'key': 'value'};

        when(() => mockRequest.sessionToken).thenReturn(fakeSessionToken);
        when(() => mockRequest.toJson()).thenReturn(fakeRequestData);

        await authenticationRepo.activateAccount(request: mockRequest);

        final List<dynamic> captured = verify(() => evoHttpClient.post(
              AuthenticationRepoImpl.activateAccountUrl,
              data: captureAny(named: 'data'),
              mockConfig: any(named: 'mockConfig'),
              requestOption: captureAny(named: 'requestOption'),
            )).captured;

        // Verify data parameter
        expect(captured[0], equals(fakeRequestData));

        // Verify requestOption parameter
        final CommonRequestOption requestOption = captured[1] as CommonRequestOption;
        expect(requestOption.headers, isNotNull);
        expect(requestOption.headers?[HeaderKey.sessionToken], equals(fakeSessionToken));
      });
    });
  });
}
