import 'package:evoapp/data/response/account_activation_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('AccountActivationEntity', () {
    test('should create an instance with given parameters', () {
      final AccountActivationEntity entity = AccountActivationEntity(
        challengeType: 'challenge-type',
        sessionToken: 'abc123',
        otpResendSecs: 30,
        otpValiditySecs: 120,
      );

      expect(entity.challengeType, 'challenge-type');
      expect(entity.sessionToken, 'abc123');
      expect(entity.otpResendSecs, 30);
      expect(entity.otpValiditySecs, 120);
    });

    test('should create an unserializable instance', () {
      final AccountActivationEntity entity = AccountActivationEntity.unserializable();

      expect(entity.challengeType, null);
      expect(entity.sessionToken, null);
      expect(entity.otpResendSecs, null);
      expect(entity.otpValiditySecs, null);
    });

    test('should create an instance from BaseResponse', () {
      final Map<String, Object> mockData = {
        'challenge_type': 'email',
        'session_token': 'xyz789',
        'otp_resend_secs': 60,
        'otp_validity_secs': 300,
        'retry_remaining_count': 5,
      };
      final BaseResponse baseResponse = BaseResponse(
          statusCode: CommonHttpClient.SUCCESS, response: <String, dynamic>{'data': mockData});

      final AccountActivationEntity entity = AccountActivationEntity.fromBaseResponse(baseResponse);

      expect(entity.challengeType, 'email');
      expect(entity.sessionToken, 'xyz789');
      expect(entity.otpResendSecs, 60);
      expect(entity.otpValiditySecs, 300);
    });

    test('should convert to JSON correctly', () {
      final AccountActivationEntity entity = AccountActivationEntity(
        challengeType: 'challenge-type',
        sessionToken: 'abc123',
        otpResendSecs: 30,
        otpValiditySecs: 120,
      );

      final Map<String, dynamic> json = entity.toJson();

      expect(json['challenge_type'], 'challenge-type');
      expect(json['session_token'], 'abc123');
      expect(json['otp_resend_secs'], 30);
      expect(json['otp_validity_secs'], 120);
    });
  });
}
