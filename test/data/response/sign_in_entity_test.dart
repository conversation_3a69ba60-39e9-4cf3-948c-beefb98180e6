import 'package:evoapp/data/response/auth_challenge_type.dart';
import 'package:evoapp/data/response/sign_in_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('verify constant', () {
    expect(SignInEntity.verdictInvalidParameter, 'invalid_parameters');
    expect(SignInEntity.verdictInvalidCredential, 'invalid_credential');
    expect(SignInEntity.verdictIncorrectOTP, 'incorrect_otp');
    expect(SignInEntity.verdictExpiredData, 'expired_data');
    expect(SignInEntity.verdictOneLastTry, 'one_last_try');
    expect(SignInEntity.verdictInvalidToken, 'invalid_token');
    expect(SignInEntity.verdictInvalidDeviceToken, 'invalid_device_token');
    expect(SignInEntity.verdictUserNotExisted, 'record_not_found');
    expect(SignInEntity.verdictLimitExceeded, 'limit_exceeded');
    expect(SignInEntity.defaultResendOtpIntervalTimeInSecs, 60);
  });

  group('verify SignInEntity', () {
    const String fakeAccessToken = 'fakeAccessToken';
    const String fakeAuthNotiToken = 'fakeAuthNotiToken';
    const String fakeBiometricToken = 'fakeBiometricToken';
    const AuthChallengeType fakeChallengeType = AuthChallengeType.none;
    const String fakeDeviceToken = 'fakeDeviceToken';
    const int fakeOtpResendSecs = 50;
    const int fakeOtpValiditySecs = 300;
    const String fakeRefreshToken = 'fakeRefreshToken';
    const String fakeSessionToken = 'fakeSessionToken';
    const int fakeUserId = 0;
    final Map<String, dynamic> ekycClientSettings = <String, dynamic>{'key': 'value'};

    test('Test fromBaseResponse/toJson without otp_resend_secs', () {
      final BaseResponse baseResponse = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'access_token': fakeAccessToken,
            'auth_noti_token': fakeAuthNotiToken,
            'biometric_token': fakeBiometricToken,
            'challenge_type': fakeChallengeType.value,
            'device_token': fakeDeviceToken,
            'otp_validity_secs': fakeOtpValiditySecs,
            'refresh_token': fakeRefreshToken,
            'session_token': fakeSessionToken,
            'user_id': fakeUserId,
          },
        },
      );

      final SignInEntity result = SignInEntity.fromBaseResponse(baseResponse);

      expect(result.accessToken, fakeAccessToken);
      expect(result.authNotiToken, fakeAuthNotiToken);
      expect(result.biometricToken, fakeBiometricToken);
      expect(result.challengeType, fakeChallengeType.value);
      expect(result.deviceToken, fakeDeviceToken);
      expect(result.otpResendSecs, SignInEntity.defaultResendOtpIntervalTimeInSecs);
      expect(result.otpValiditySecs, fakeOtpValiditySecs);
      expect(result.refreshToken, fakeRefreshToken);
      expect(result.sessionToken, fakeSessionToken);
      expect(result.userId, fakeUserId);
    });

    test('Test fromBaseResponse/toJson with all fields', () {
      final BaseResponse baseResponse = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'access_token': fakeAccessToken,
            'auth_noti_token': fakeAuthNotiToken,
            'biometric_token': fakeBiometricToken,
            'challenge_type': fakeChallengeType.value,
            'device_token': fakeDeviceToken,
            'otp_resend_secs': fakeOtpResendSecs,
            'otp_validity_secs': fakeOtpValiditySecs,
            'refresh_token': fakeRefreshToken,
            'session_token': fakeSessionToken,
            'user_id': fakeUserId,
            'ekyc_client_settings': ekycClientSettings,
          },
        },
      );

      final SignInEntity result = SignInEntity.fromBaseResponse(baseResponse);

      expect(result.accessToken, fakeAccessToken);
      expect(result.authNotiToken, fakeAuthNotiToken);
      expect(result.biometricToken, fakeBiometricToken);
      expect(result.challengeType, fakeChallengeType.value);
      expect(result.deviceToken, fakeDeviceToken);
      expect(result.otpResendSecs, fakeOtpResendSecs);
      expect(result.otpValiditySecs, fakeOtpValiditySecs);
      expect(result.refreshToken, fakeRefreshToken);
      expect(result.sessionToken, fakeSessionToken);
      expect(result.userId, fakeUserId);
      expect(result.ekycClientSettings, ekycClientSettings);
    });

    test('Test unserializable', () {
      final SignInEntity result = SignInEntity.unserializable();

      expect(result.accessToken, isNull);
      expect(result.authNotiToken, isNull);
      expect(result.biometricToken, isNull);
      expect(result.challengeType, isNull);
      expect(result.deviceToken, isNull);
      expect(result.otpResendSecs, 0);
      expect(result.otpValiditySecs, isNull);
      expect(result.refreshToken, isNull);
      expect(result.sessionToken, isNull);
      expect(result.userId, isNull);
      expect(result.ekycClientSettings, isNull);
    });

    test('toJson() should return correct json', () {
      final BaseResponse baseResponse = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'access_token': fakeAccessToken,
            'auth_noti_token': fakeAuthNotiToken,
            'biometric_token': fakeBiometricToken,
            'challenge_type': fakeChallengeType.value,
            'device_token': fakeDeviceToken,
            'otp_resend_secs': fakeOtpResendSecs,
            'otp_validity_secs': fakeOtpValiditySecs,
            'refresh_token': fakeRefreshToken,
            'session_token': fakeSessionToken,
            'user_id': fakeUserId,
            'ekyc_client_settings': ekycClientSettings,
          },
        },
      );
      final SignInEntity result = SignInEntity.fromBaseResponse(baseResponse);
      final Map<String, dynamic> json = result.toJson();

      expect(json['access_token'], fakeAccessToken);
      expect(json['auth_noti_token'], fakeAuthNotiToken);
      expect(json['biometric_token'], fakeBiometricToken);
      expect(json['challenge_type'], fakeChallengeType.value);
      expect(json['device_token'], fakeDeviceToken);
      expect(json['otp_resend_secs'], fakeOtpResendSecs);
      expect(json['otp_validity_secs'], fakeOtpValiditySecs);
      expect(json['refresh_token'], fakeRefreshToken);
      expect(json['session_token'], fakeSessionToken);
      expect(json['user_id'], fakeUserId);
      expect(json['ekyc_client_settings'], ekycClientSettings);
    });
  });
}
