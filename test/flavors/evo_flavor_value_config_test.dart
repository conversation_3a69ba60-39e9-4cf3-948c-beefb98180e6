import 'package:evoapp/flavors/evo_flavor_value_config.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('Correct constants', () {
    /// Base url config
    expect(EvoFlavorValueConfig.baseUrlStag, 'https://mobile-kyko-staging.tsengineering.io/');
    expect(EvoFlavorValueConfig.baseUrlUat, 'https://mobile-api-evo-vn-uat.trustingsocial.com/');
    expect(EvoFlavorValueConfig.baseUrlProd, 'https://mobile-api.goevo.vn/');

    /// OneSignal App Id
    expect(EvoFlavorValueConfig.oneSignalAppIdStag, '************************************');
    expect(EvoFlavorValueConfig.oneSignalAppIdUat, '************************************');
    expect(EvoFlavorValueConfig.oneSignalAppIdProd, '************************************');
  });
}
