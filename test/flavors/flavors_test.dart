import 'package:evoapp/flavors/evo_flavor_value_config.dart';
import 'package:evoapp/flavors/factory/evo_flavor.dart';
import 'package:evoapp/flavors/factory/evo_flavor_factory.dart';
import 'package:evoapp/flavors/factory/evo_flavor_prod.dart';
import 'package:evoapp/flavors/factory/evo_flavor_stag.dart';
import 'package:evoapp/flavors/factory/evo_flavor_uat.dart';
import 'package:evoapp/flavors/flavors_type.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('test flavor value', () {
    test('test stag flavor', () {
      final EvoFlavor stagFlavor = EvoFlavorStag();
      final CommonFlavorValues stagFlavorValue = stagFlavor.getFlavorValue();

      expect(stagFlavorValue.baseUrl, EvoFlavorValueConfig.baseUrlStag);
      expect(stagFlavorValue.oneSignalAppId, EvoFlavorValueConfig.oneSignalAppIdStag);
      expect(stagFlavorValue.initializeFirebaseSdk, true);
      expect(stagFlavorValue.firebaseOptions, isNull);
    });

    test('test uat flavor', () {
      final EvoFlavor uatFlavor = EvoFlavorUat();
      final CommonFlavorValues uatFlavorValue = uatFlavor.getFlavorValue();

      expect(uatFlavorValue.baseUrl, EvoFlavorValueConfig.baseUrlUat);
      expect(uatFlavorValue.oneSignalAppId, EvoFlavorValueConfig.oneSignalAppIdUat);
      expect(uatFlavorValue.initializeFirebaseSdk, true);
      expect(uatFlavorValue.firebaseOptions, isNull);
    });

    test('test prod flavor', () {
      final EvoFlavor prodFlavor = EvoFlavorProd();
      final CommonFlavorValues prodFlavorValue = prodFlavor.getFlavorValue();

      expect(prodFlavorValue.baseUrl, EvoFlavorValueConfig.baseUrlProd);
      expect(prodFlavorValue.oneSignalAppId, EvoFlavorValueConfig.oneSignalAppIdProd);
      expect(prodFlavorValue.initializeFirebaseSdk, true);
      expect(prodFlavorValue.firebaseOptions, isNull);
    });
  });

  group('test flavor factory', () {
    test('test  flavor factory', () {
      final EvoFlavorFactory flavorFactory = EvoFlavorFactory();

      expect(flavorFactory.getFlavor(FlavorType.stag), isA<EvoFlavorStag>());
      expect(flavorFactory.getFlavor(FlavorType.uat), isA<EvoFlavorUat>());
      expect(flavorFactory.getFlavor(FlavorType.prod), isA<EvoFlavorProd>());
    });
  });
}
