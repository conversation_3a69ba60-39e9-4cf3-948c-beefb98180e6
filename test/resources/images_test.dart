import 'package:evoapp/resources/resources.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String assetPath = EvoImages.assetPath;
  const String png = EvoImages.png;
  const String svg = EvoImages.svg;

  test('verify extensions and paths', () {
    expect(EvoImages.png, '.png');
    expect(EvoImages.svg, '.svg');
    expect(EvoImages.assetPath, 'assets/images/');
  });

  test('verify PNG constant image', () {
    expect(EvoImages.icDefaultAvatar, '${assetPath}ic_default_avatar$svg');
    expect(EvoImages.bgAppUpdate, '${assetPath}bg_app_update$png');
    expect(EvoImages.imgSplashBackgroundImage, '${assetPath}img_splash_background_image$png');
  });

  test('verify SVG constant image', () {
    expect(EvoImages.icBottomBarHome, '${assetPath}ic_bottom_bar_home$svg');
    expect(EvoImages.icBottomBarCards, '${assetPath}ic_bottom_bar_cards$svg');
    expect(EvoImages.icBanknotes, '${assetPath}ic_banknotes$svg');
    expect(EvoImages.icArrowsRightLeft, '${assetPath}ic_arrows_right_left$svg');
    expect(EvoImages.icBottomBarProfile, '${assetPath}ic_bottom_bar_profile$svg');
    expect(EvoImages.icArrowRight, '${assetPath}ic_arrow_right$svg');
    expect(EvoImages.icMpin, '${assetPath}ic_mpin$svg');
    expect(EvoImages.icArrowBack, '${assetPath}ic_arrow_back$svg');
    expect(EvoImages.icArrowForward, '${assetPath}ic_arrow_forward$svg');
    expect(EvoImages.icEyeVisibilityOff, '${assetPath}ic_eye_visibility_off$svg');
    expect(EvoImages.icEyeVisibilityOn, '${assetPath}ic_eye_visibility_on$svg');
    expect(EvoImages.icChevronDoubleUp, '${assetPath}ic_chevron_double_up$svg');
    expect(EvoImages.icChevronDoubleDown, '${assetPath}ic_chevron_double_down$svg');
    expect(EvoImages.icCalendarDays, '${assetPath}ic_calendar_days$svg');

    /// Snackbar icon
    expect(EvoImages.icSnackBarSuccess, '${assetPath}ic_snack_bar_success$svg');
    expect(EvoImages.icSnackBarError, '${assetPath}ic_snack_bar_error$svg');
    expect(EvoImages.icSnackBarWarning, '${assetPath}ic_snack_bar_warning$svg');
    expect(EvoImages.icSnackBarNeutral, '${assetPath}ic_snack_bar_neutral$svg');

    expect(EvoImages.icClear, '${assetPath}ic_clear$svg');
    expect(EvoImages.icErrorWebView, '${assetPath}ic_error_web_view$svg');
    expect(EvoImages.icFaceId, '${assetPath}ic_face_id$svg');
    expect(EvoImages.icFingerId, '${assetPath}ic_finger_id$svg');
    expect(EvoImages.icFaceFingerId, '${assetPath}ic_face_finger_id$svg');
    expect(EvoImages.icSettingFaceFingerId, '${assetPath}ic_setting_face_finger$svg');
    expect(EvoImages.icSettingFaceId, '${assetPath}ic_setting_face$svg');
    expect(EvoImages.icSettingFingerId, '${assetPath}ic_setting_finger$svg');
    expect(EvoImages.icNotify, '${assetPath}ic_notify$svg');
    expect(EvoImages.icError, '${assetPath}ic_error$svg');
    expect(EvoImages.icSwitchAccount, '${assetPath}ic_switch_account$svg');
    expect(EvoImages.icBiometrics, '${assetPath}ic_biometrics$svg');
    expect(EvoImages.icErrorTextField, '${assetPath}ic_error_text_field$svg');
    expect(EvoImages.icSuccessBanner, '${assetPath}ic_success_banner$svg');
    expect(EvoImages.icErrorBanner, '${assetPath}ic_error_banner$svg');
    expect(EvoImages.icWarningBanner, '${assetPath}ic_warning_banner$svg');
    expect(EvoImages.icInfoBanner, '${assetPath}ic_info_banner$svg');
    expect(EvoImages.icMail, '${assetPath}ic_mail$svg');
    expect(EvoImages.icCloseGrey, '${assetPath}ic_close_grey$svg');
    expect(EvoImages.imgEnableBiometric, '${assetPath}img_enable_biometric$svg');
    expect(EvoImages.imgBrandName, '${assetPath}img_brand_name$svg');
    expect(EvoImages.icAlertError, '${assetPath}ic_alert_error$svg');
    expect(EvoImages.icAlertUnsuccessful, '${assetPath}ic_alert_unsuccessful$svg');
    expect(EvoImages.icAlertWarning, '${assetPath}ic_alert_warning$svg');

    /// Login on new device
    expect(EvoImages.imgLoginOnNewDevice, '${assetPath}img_login_on_new_device$svg');

    /// Welcome screen
    expect(EvoImages.imgWelcome, '${assetPath}img_welcome$png');
    expect(EvoImages.imgIntroduction1, '${assetPath}img_introduction_1$png');
    expect(EvoImages.imgIntroduction2, '${assetPath}img_introduction_2$png');
    expect(EvoImages.imgIntroduction3, '${assetPath}img_introduction_3$png');

    /// Change MPIN
    expect(EvoImages.icNewPinSuccess, '${assetPath}new_pin_success$svg');

    /// Profile
    expect(EvoImages.icProfileBiometric, '${assetPath}ic_profile_biometric$svg');
    expect(EvoImages.icCard, '${assetPath}ic_card$svg');
    expect(EvoImages.icQuestionMarkCircle, '${assetPath}ic_question_mark_circle$svg');
    expect(EvoImages.icProfileMail, '${assetPath}ic_profile_mail$svg');
    expect(EvoImages.icEvoSm, '${assetPath}ic_evo_sm$svg');
    expect(EvoImages.icDocumentText, '${assetPath}ic_document_text$svg');
    expect(EvoImages.icPolicy, '${assetPath}ic_policy$svg');

    /// Card
    expect(EvoImages.frameVirtualCard, '${assetPath}frame_virtual_card$svg');
    expect(EvoImages.frameVirtualCardDetails, '${assetPath}frame_virtual_card_details$svg');
    expect(EvoImages.framePhysicalCard, '${assetPath}frame_physical_card$svg');
    expect(EvoImages.framePhysicalCardDetails, '${assetPath}frame_physical_card_details$svg');
    expect(EvoImages.framePhysicalCardInactive, '${assetPath}physical_card_inactive$svg');
    expect(EvoImages.frameVirtualCardInactive, '${assetPath}virtual_card_inactive$svg');
    expect(EvoImages.icInbox, '${assetPath}ic_inbox$svg');
    expect(EvoImages.icCopy, '${assetPath}ic_copy$svg');
    expect(EvoImages.icChatBubble, '${assetPath}ic_chat_bubble$svg');

    /// Credit limit
    expect(EvoImages.icInfoFilled, '${assetPath}ic_info_filled$svg');

    /// Activate Virtual Card
    expect(EvoImages.frameVirtualCardNameHolder, '${assetPath}frame_card_holder_name$svg');
    expect(EvoImages.icActiveVirtualCardGuideline1,
        '${assetPath}ic_active_virtual_card_guideline_1$svg');
    expect(EvoImages.icActiveVirtualCardGuideline2,
        '${assetPath}ic_active_virtual_card_guideline_2$svg');
    expect(EvoImages.icActiveVirtualCardGuideline3,
        '${assetPath}ic_active_virtual_card_guideline_3$svg');
    expect(EvoImages.virtualCardActivated, '${assetPath}frame_card_activated$svg');

    // Activate Card Success
    expect(EvoImages.imgActivateVirtualCardSuccess,
        '${assetPath}img_activate_virtual_card_success$svg');
    expect(EvoImages.imgActivatePhysicalCardSuccess,
        '${assetPath}img_activate_physical_card_success$svg');
   
    expect(EvoImages.imgFaceCaptureCheck, '${assetPath}img_face_capture_check$png');

    /// Activation Status Icons
    expect(EvoImages.imgActivationStatusExisting, '${assetPath}activation_status_existing$svg');
    expect(EvoImages.imgActivationStatusNone, '${assetPath}activation_status_none$svg');
    expect(EvoImages.imgActivationStatusReject, '${assetPath}activation_status_reject$svg');
    expect(EvoImages.imgActivationStatusProcessing, '${assetPath}activation_status_processing$svg');
  });
}
