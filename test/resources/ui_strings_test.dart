import 'package:evoapp/resources/ui_strings.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('verify EvoStrings', () {
    test('should have correct values', () {
      // Common
      expect(EvoStrings.existWarning, '<PERSON>hấn lần n<PERSON>a để thoát');
      expect(EvoStrings.retry, 'Retry');
      expect(EvoStrings.ctaActive, 'Activate');
      expect(EvoStrings.enterMPIN, 'Enter your 4-digit MPIN');
      expect(EvoStrings.login, 'Log in');
      expect(EvoStrings.needHelp, 'Need Help?');
      expect(EvoStrings.backToHomePage, 'Back to home');
      expect(EvoStrings.back, 'Back');
      expect(EvoStrings.logout, 'Log out');
      expect(EvoStrings.ctaCancel, 'Cancel');
      expect(EvoStrings.ctaProceed, 'Proceed');
      expect(EvoStrings.ok, 'OK');
      expect(EvoStrings.hi, 'Hi');
      expect(EvoStrings.unknownError, 'An error occurred.');
      expect(EvoStrings.gotIt, 'Got It');
      expect(EvoStrings.payNow, 'Pay Now');
      expect(EvoStrings.copiedToClipboard, 'Copied to clipboard.');
      expect(EvoStrings.reset, 'Reset');
      expect(EvoStrings.textFieldLabelRequiredMark, '*');

      // Bottom navigation bar
      expect(EvoStrings.bottomBarHomeLabel, 'Home');
      expect(EvoStrings.bottomBarCardLabel, 'Cards');
      expect(EvoStrings.bottomBarPayCardLabel, 'Pay Card');
      expect(EvoStrings.bottomBarUsageLabel, 'Usage');
      expect(EvoStrings.bottomBarProfileLabel, 'Profile');

      expect(EvoStrings.otpInvalidMsg, 'OTP invalid or expired.');

      // Input Phone Number Screen
      expect(EvoStrings.loginDesc, 'Enter registered number to log in.');
      expect(EvoStrings.mobileNumberLabel, 'Mobile Number');
      expect(EvoStrings.countryPhoneCode, '+63');
      expect(EvoStrings.doNotHaveAccount, 'Don’t have an account? ');
      expect(EvoStrings.getStarted, 'Get started');

      //OTP
      expect(EvoStrings.otpExpiredDesc, 'Your OTP expires in {0}.');
      expect(EvoStrings.otpResendCodeDesc, 'Didn\'t receive a code?');
      expect(EvoStrings.otpResendCode, 'Resend code');
      expect(EvoStrings.verifyOtpScreenTitle, 'OTP Verification');
      expect(EvoStrings.verifyOtpScreenDesc,
          'Please enter the 6-digit code sent to your mobile number ending in {0}.');
      expect(
          EvoStrings.verifyEmailOtpScreenDesc, 'Please enter the 6-digit code sent to your email.');

      // Profile Page
      expect(EvoStrings.unKnowPhone, 'XXX XXX XXXX');
      expect(EvoStrings.privacySecuritySection, 'Privacy and Security');
      expect(EvoStrings.helpSection, 'Help');
      expect(EvoStrings.aboutSection, 'About EVO');
      expect(EvoStrings.creditCardItemTitle, 'Credit Card Basics');
      expect(EvoStrings.faqItemTitle, 'FAQs');
      expect(EvoStrings.contactItemTitle, 'Contact Us');
      expect(EvoStrings.aboutItemTitle, 'Who is EVO?');
      expect(EvoStrings.tncItemTitle, 'Terms & Conditions');
      expect(EvoStrings.policyItemTitle, 'Privacy Policy');

      // User Info
      expect(EvoStrings.feedbackAndContact, 'For any inquiries, contact EVO Customer Support:');

      // Button
      expect(EvoStrings.ctaYes, 'Yes');
      expect(EvoStrings.ctaSkip, 'Skip');
      expect(EvoStrings.ctaCancel, 'Cancel');
      expect(EvoStrings.close, 'Close');
      expect(EvoStrings.viewMore, 'View more');
      expect(EvoStrings.ctaApplyNow, 'Apply Now');
      expect(EvoStrings.ctaSubmit, 'Submit');
      expect(EvoStrings.ctaGoToHome, 'Go to Home');
      expect(EvoStrings.ctaLetsGo, 'Let\'s go');

      // Login
      expect(EvoStrings.inputPinTitle, 'Enter your MPIN');
      expect(EvoStrings.inputPinDesc, 'For your security, we need your 4-digit MPIN to proceed.');
      expect(EvoStrings.loginScreenLoginWithBiometric, 'Log in with Biometrics');
      expect(EvoStrings.useMPIN, 'Use MPIN');

      /// Login on old device
      expect(EvoStrings.loginOnOldDeviceSwitchAccountCTA, 'Switch Account');
      expect(EvoStrings.loginOnOldDeviceTitle, 'Welcome back');
      expect(EvoStrings.loginLimitedExceededTitle, 'Maximum failed login\nattempts detected');

      // Session timeout
      expect(EvoStrings.authorizationSessionTimeoutTitle, 'The session has expired');
      expect(EvoStrings.localizedReasonForUsingBiometrics, 'EVO');
      expect(EvoStrings.settingTitle, 'Go to Settings');
      expect(EvoStrings.ignoreTitle, 'Cancel');

      //bio metrics error;
      expect(EvoStrings.lockedOutError, 'biometrics authentication is locked out');
      expect(
          EvoStrings.permanentlyLockedOutError, 'biometrics authentication permanently locked out');
      expect(EvoStrings.maxTriesReached, 'Maximum tries reached');
      expect(EvoStrings.limitResendOtp, 'Maximum resend reached');

      expect(EvoStrings.openDeviceSecuritySettingDesc,
          'Biometric authentication is not set up on your device. Go to ‘Settings’ to add biometric authentication');
      expect(EvoStrings.openDeviceSecuritySettingToUnlockDesc,
          'Please lock and unlock phone screen or go to ‘Settings’ to unlock biometric.');
      expect(EvoStrings.biometricRequiredTitle, 'Biometric required');
      expect(EvoStrings.biometricLockedTitle, 'Biometric has been locked');

      //Text for No Setup FaceId/FingerId Popup
      expect(EvoStrings.signOutFail, 'Bạn vừa đăng xuất không thành công');
      expect(EvoStrings.signOutSuccess, 'Bạn đã đăng xuất thành công');
      expect(EvoStrings.faceText, 'khuôn mặt');
      expect(EvoStrings.fingerText, 'vân tay');
      expect(EvoStrings.faceFingerText, 'khuôn mặt/ vân tay');
      expect(EvoStrings.authenticateText, 'Xác thực');
      expect(EvoStrings.enableText, 'Kích hoạt');
      expect(EvoStrings.biometricTokenUnUsableMessage,
          'Đăng nhập bằng {0} đã hết hạn. Vui lòng kích hoạt lại trong Cài đặt của ứng dụng');

      //Web view
      expect(EvoStrings.webViewErrorTitle, 'Không tìm thấy trang');
      expect(EvoStrings.webViewErrorDescription,
          'Trang của bạn không tồn tại hoặc\nđang có lỗi xảy ra');

      expect(EvoStrings.biometricDeviceChangeWarring,
          '{0} đã bị thay đổi. Vui lòng kiểm tra lại thiết lập {1} trong máy');

      // Transaction history
      expect(EvoStrings.allTransactionTitle, 'All Transactions');
      expect(EvoStrings.totalMonthSpend, 'Total Month Spend: ');

      // Intro
      expect(EvoStrings.introductionDescription1, 'Activate and use your Kyko card instantly');
      expect(EvoStrings.introductionDescription2,
          'Secure online shopping with your Kyko virtual card');
      expect(EvoStrings.introductionDescription3, 'Manage your card anytime, anywhere');
      expect(EvoStrings.introductionNext, 'Let’s go');

      //force update
      expect(EvoStrings.forceUpdateDescription,
          'Cập nhật ngay phiên bản mới để có trải nghiệm mượt mà hơn & sử dụng các tính năng mới nhất.');
      expect(EvoStrings.forceUpdateSubDesc, 'Đã có phiên bản mới');
      expect(EvoStrings.forceUpdateAgree, 'Cập nhật');
      expect(EvoStrings.forceUpdateSkip, 'Bỏ qua');

      //Reset Pin
      expect(EvoStrings.titleSessionTokenExpired, 'Session expired');
      expect(EvoStrings.contentSessionTokenExpiredSignIn,
          'Please log in again to\ncontinue using the EVO app');
      expect(EvoStrings.textSubmitSessionTokenExpiredSignIn, 'Log in again');
      expect(EvoStrings.contentSessionTokenExpiredResetPin,
          'To retry, please create a new\nMPIN again');

      // Detect Root/Jailbreak
      expect(EvoStrings.titleBlockInsecureDeviceDialog, 'EVO chưa hỗ trợ thiết bị này');
      expect(EvoStrings.descriptionBlockInsecureDeviceDialog,
          'EVO chưa hỗ trợ các thiết bị đã bẻ khoá để bảo đảm an toàn cho tài khoản của bạn');

      // Download File
      expect(EvoStrings.downloadLinkFileSuccess, 'Tải file thành công');
      expect(EvoStrings.downloadLinkFileFail, 'Tải file không thành công');
      expect(EvoStrings.startDownloadLinkFile, 'Đang tải file, bạn đợi chút nhé');

      /// MPIN widget
      expect(EvoStrings.mpinObscureText, '•');
      expect(EvoStrings.forgotMPINQuestion, 'Forgot MPIN?');

      /// Enable biometric
      expect(EvoStrings.activeBiometricTitle, 'Do you want to enable Biometrics?');
      expect(EvoStrings.activeBiometricDesc,
          'Activating this feature will allow you to login and secure your transactions via Fingerprint or Face ID (in supported devices)');
      expect(EvoStrings.activateBiometricsSuccessDesc,
          'You have successfully updated\nyour preferred settings');

      /// Login on new device popup
      expect(EvoStrings.loginOnNewDeviceTitle, 'You’re logging in from a\nnew device');
      expect(EvoStrings.loginOnNewDeviceDesc,
          'You will be automatically logged out\nfrom the other device.\nDo you want to proceed?');
      expect(EvoStrings.loginOnNewDeviceConfirmCTA, 'Proceed');

      /// Logout dialog
      expect(EvoStrings.logoutTitle, 'Are you sure you\nwant to log out?');

      /// Switch account dialog
      expect(EvoStrings.switchAccountTitle, 'You’re about to switch accounts');

      /// Idle / Inactive detector
      expect(EvoStrings.idleAWhileTitle, 'Are you still there?');
      expect(EvoStrings.idleAWhileContent,
          'To keep your account safe from unauthorized access, we’ll log you out in ');
      expect(EvoStrings.idleAWhileDurationInSec, 'seconds');
      expect(EvoStrings.idleAWhileKeepMeLoggedInCTA, 'Keep me logged in');
      expect(EvoStrings.idleAWhileLogMeOutCTA, 'Log me out');

      expect(EvoStrings.inActiveTitle, 'See you again soon!');
      expect(EvoStrings.inActiveDescription,
          'To protect your account from unauthorized access, we automatically log you out after you’ve been inactive for a while');
      expect(EvoStrings.ctaLogInAgain, 'Log in again');

      /// Change MPIN
      expect(EvoStrings.changeMPINLockedResourceTitle,
          'Maximum failed attempts to\nchange MPIN reached');
      expect(EvoStrings.backToProfile, 'Back to Profile');
      expect(EvoStrings.changeMPIN, 'Change MPIN');
      expect(EvoStrings.createNewMPINTitle, 'Create your new MPIN');
      expect(EvoStrings.createNewMPINDesc,
          'You will use this to secure your transactions on the EVO app.');
      expect(EvoStrings.enableBiometrics, 'Enable Biometrics');

      /// Reset PIN
      expect(
          EvoStrings.resetMPINLimitExceededTitle, 'Maximum failed attempts to\nreset MPIN reached');

      /// MPIN validate message
      expect(EvoStrings.noSameAllDigitsMPIN, 'No same all digits MPIN number');
      expect(EvoStrings.noDecreasingMPIN, 'No decreasing MPIN number');
      expect(EvoStrings.noIncreasingMPIN, 'No increasing MPIN number');
      expect(EvoStrings.mustMatchMPINLength, 'MPIN number must be 4 digits');

      /// MPIN info banner
      expect(EvoStrings.infoBannerNotSamePrevious, 'Must not be similar to previous MPIN');
      expect(EvoStrings.infoBannerNotIncreasing, 'Not increasing (ex. 1,2,3,4)');
      expect(EvoStrings.infoBannerNotDecreasing, 'Not decreasing (ex. 4,3,2,1)');
      expect(EvoStrings.infoBannerNotSameDigits, 'Not same digits (ex. 4,4,4,4)');
      expect(EvoStrings.infoBannerMustBe4Digits, 'Must be 4 digits');

      /// Welcome screen
      expect(EvoStrings.welcomeEvoTitle, 'Welcome to EVO');
      expect(EvoStrings.welcomeMessage, 'We’re excited to welcome you in!');
      expect(EvoStrings.activateAccountMessage, 'Activate Account');
      expect(EvoStrings.alreadyHaveAccountMessage, 'Already have an account? ');
      expect(EvoStrings.welcomeNewUserTitle, 'Welcome onboard, {0}!');

      // MPIN confirm reset
      expect(EvoStrings.confirmResetPinTitle, 'Confirm your MPIN');
      expect(EvoStrings.confirmResetPinDesc,
          'You will use this to secure your transactions on the Kyko app.');
      expect(EvoStrings.mpinNotMatch, 'MPIN does not match');
      expect(EvoStrings.forceLogoutTitle, 'You have been logged out of your account');

      /// card pages
      expect(EvoStrings.cardPageTitle, 'My Cards');
      expect(EvoStrings.virtualCard, 'Virtual Card');
      expect(EvoStrings.physicalCard, 'Physical Card');
      expect(EvoStrings.recentTransaction, 'Recent Transactions');
      expect(EvoStrings.noTransactionYet, 'No Transactions Yet');
      expect(EvoStrings.ctaFreezeCard, 'Freeze Card');
      expect(EvoStrings.ctaFreezeCard, 'Freeze Card');
      expect(EvoStrings.ctaShowDetails, 'Show Details');
      expect(EvoStrings.prefixViaText, 'via');
      expect(EvoStrings.maskTransactionCardNumber, '···· {0}');
      expect(EvoStrings.cardStateFrozenTitle, 'Frozen');
      expect(EvoStrings.cardStateBlockedTitle, 'Blocked');
      expect(EvoStrings.cardStateInactiveTitle, '· · · · · · · ·');
      expect(EvoStrings.cardNumber, 'Card Number');
      expect(EvoStrings.expiryDate, 'Expiry Date');
      expect(EvoStrings.cvv, 'CVV');
      expect(EvoStrings.nameOnCard, 'Name on Card');
      expect(EvoStrings.activateCard, 'Activate Card');

      /// Inactive Card Panel
      expect(EvoStrings.activateCardDesc, 'Activate yours virtual card to start');
      expect(EvoStrings.activeCTAText, 'Activate now');

      /// Payment Summary Panel
      expect(EvoStrings.overDue, 'Overdue');
      expect(EvoStrings.paid, 'Paid');
      expect(EvoStrings.payToday, 'Pay Today');
      expect(EvoStrings.payTomorrow, 'Pay Tomorrow');
      expect(EvoStrings.dueDate, 'Due Date');
      expect(EvoStrings.amountToPayTitle, 'Amount To Pay');
      expect(EvoStrings.amountToPayDesc, 'Kindly disregard if paid');
      expect(EvoStrings.foreClosedTitle, 'Please Pay Your Balance');
      expect(EvoStrings.totalAmountTitle, 'Total Amount Due');
      expect(EvoStrings.paidAmountTitle, 'Minimum Amount Due');
      expect(EvoStrings.lastAmountPaidTitle, 'Last Amount Paid');

      /// Credit Limit Panel
      expect(EvoStrings.availableCredit, 'Available Credit');
      expect(EvoStrings.cutOffDate, 'Cut-off Date');
      expect(EvoStrings.creditLimitProgressPrefix, 'out of');
      expect(EvoStrings.creditLimitProgressSuffix, 'total credit limit');

      /// Account Activation
      expect(EvoStrings.haveNotApplyYetBtn, 'Haven’t Applied Yet?');
      expect(EvoStrings.alreadyHaveAnAccountText, 'Already have an account? ');
      expect(EvoStrings.activeAccountText, 'Activate Account');
      expect(EvoStrings.activationAccountChunk1,
          'By clicking "Activate Account", I confirm that I understand and agree to the ');
      expect(EvoStrings.activationAccountChunk2, ' and ');
      expect(EvoStrings.activationAccountChunk3, ' of Kyko.');
      expect(EvoStrings.activationAccountTermsAndConditions, 'Terms and Conditions');
      expect(EvoStrings.activationAccountPrivacyPolicy, 'Privacy Policy');
      expect(EvoStrings.mobileNumberCheckTitle, 'Let’s activate your account!');
      expect(EvoStrings.mobileNumberCheckDesc,
          'Enter the mobile number you used in the credit card application form.');
      expect(EvoStrings.activeAccountErrorApplicationNotFoundTitle,
          'Sorry, we can’t seem to find your record.');
      expect(EvoStrings.activeAccountErrorApplicationNotFoundDescription, 'Haven’t applied yet?');
      expect(EvoStrings.activeAccountErrorApplicationRejectedTitle,
          'Sorry, please try applying again another time.');
      expect(EvoStrings.activeAccountErrorApplicationRejectedDescription,
          'Thanks for your interest in applying for an EVO credit card. Unfortunately, we can’t proceed with your application at this time.');
      expect(EvoStrings.activeAccountErrorApplicationPendingTitle,
          'Your application is still being processed.');
      expect(EvoStrings.activeAccountErrorApplicationPendingDescription,
          'We’ll send the result to you as soon as we’re done processing your application.');
      expect(EvoStrings.activeAccountCreateUsernameTitle, 'Let’s make a username.');
      expect(EvoStrings.activeAccountCreateUsernameDesc,
          'Please create a username. You will be using this to log into the app.');
      expect(EvoStrings.activateAccountCreatePinTitle, 'Create your MPIN');
      expect(EvoStrings.activateAccountCreatePinDesc,
          'You will use this to secure your transactions on the Kyko app.');

      expect(EvoStrings.usernameLabel, 'Username');
      expect(EvoStrings.usernameGuide1, 'Your handle can\'t exceed 30 characters');
      expect(EvoStrings.usernameGuide2, 'It can only contain letters, numbers, and periods');
      expect(EvoStrings.usernameGuide3, 'It can\'t contain symbols or punctuation marks');
      expect(EvoStrings.usernameGuide4, 'It needs to be unique');
      expect(EvoStrings.errorInvalidUsername, 'invalid username');
      expect(EvoStrings.errorUsernameEmpty, 'A username is required to proceed');
      expect(EvoStrings.errorUsernameMaxLength, 'Username exceeds 30 characters');
      expect(EvoStrings.errorUsernameContainsSymbols, 'Username can’t contain symbols');
      expect(EvoStrings.activateAccountSuccessDesc, 'Your account was successfully created!');

      /// Activate Virtual account
      expect(
          EvoStrings.activeVirtualCardTitle, 'Let’s activate your virtual credit card already! 👀');
      expect(EvoStrings.activeVirtualCardDesc,
          'Activate your virtual card to start using it for transactions.');
      expect(EvoStrings.activeVirtualCardGuideLine1,
          'Use your card instantly for online transactions');
      expect(EvoStrings.activeVirtualCardGuideLine2, 'Easily set a spend cap for your card');
      expect(EvoStrings.activeVirtualCardGuideLine3, 'Fully control your card on the app');
      expect(EvoStrings.ctaActiveVirtualCard, 'Activate Virtual Card');
      expect(EvoStrings.ctaActiveLater, 'Activate Later');
      expect(EvoStrings.bannerCardActivateFailedTitle, 'Card activation failed');
      expect(EvoStrings.bannerCardNewCardTitle, 'Your new card is ready to be activated');
      expect(EvoStrings.bannerCardNewCardDesc, 'Activate your card now to start using it.');

      /// Virtual card activated
      expect(EvoStrings.virtualCardActivatedTitle, 'Card Activated ⚡️\nWelcome, {0}! 🎉');
      expect(EvoStrings.virtualCardActivatedIntroTitle,
          'Fully Enjoy Your Credit Card Experience With These Reminders:');
      expect(EvoStrings.virtualCardActivatedIntroItem1,
          'Remember your due date! Pay on-time to avoid paying penalty fees');
      expect(EvoStrings.virtualCardActivatedIntroItem2,
          'Protect your card from unwanted access and keep your card details to yourself');
      expect(EvoStrings.virtualCardActivatedIntroItem3,
          'Spend responsibly! Only spend what you can repay.');

      expect(EvoStrings.activateVirtualCardSuccess, 'Your Virtual Card Is Ready For Use!');
      expect(EvoStrings.activatePhysicalCardSuccess, 'Your Physical Card Is Ready For Use!');
      expect(EvoStrings.goToCardsButton, 'Go to Cards');

      // Card Blocked
      expect(EvoStrings.blockedCardBannerTitle, 'This card has been blocked.');
      expect(EvoStrings.blockedCardBannerByUserDesc,
          'If you want to order a new credit card, please contact customer support.');
      expect(EvoStrings.blockedCardBannerByBankDesc,
          'We noticed that you’re missing a few payments on the card. Please settle so you can use the card again.\n\nIf you have already paid, but still receive this screen, please contact customer support.');

      /// Face Capture Check (Intro screen)
      expect(EvoStrings.faceCaptureCheckTitle, 'Glad to have you here,\n{0}.');
      expect(EvoStrings.faceCaptureCheckSubtitle, 'Get ready for selfie verification.');
      expect(EvoStrings.faceCaptureCheckDesc,
          'We just want to confirm if this is really you. Shoot your photos in a well-lit room and do not wear anything that covers your face.');

      /// Selfie Verification
      expect(EvoStrings.selfieAppBarTitle, 'Selfie Verification');
      expect(EvoStrings.selfieInitializingDesc,
          'Initializing selfie verification,\nplease wait for a moment');
      expect(EvoStrings.selfieProcessingDesc,
          'System is checking your photo,\nplease wait for a moment');
      expect(EvoStrings.selfieSafeInfoDesc, 'Your information is encrypted and secured');
      expect(EvoStrings.selfieErrorTitle, 'Face authentication unsuccessful!');
      expect(EvoStrings.selfieErrorSubtitle, 'Please try again!');
      expect(EvoStrings.selfieSuccessTitle, 'Selfie verification successful!');

      // Last 4 digits check
      expect(EvoStrings.last4DigitsCheckTitle, 'Enter The Last 4 Digits Of Your Card Number');
      expect(EvoStrings.last4DigitsCheckDesc,
          'To keep your account safe, make sure no one’s looking before proceeding');

      /// freecard Error
      expect(EvoStrings.freezeCarErrorTitle, 'Cannot process freezing your card');
      expect(EvoStrings.freezeCarErrorDesc1,
          'We can’t seem to connect to our system properly. If your purpose is urgent, please ');
      expect(EvoStrings.freezeCarErrorDesc2, 'contact customer support.');

      // Camera permission
      expect(EvoStrings.cameraPermissionTitle, 'Permission to use camera');
      expect(EvoStrings.cameraPermissionDesc,
          'Access to the camera has been prohibited, please enable it in the “Settings” to continue');

      // Transaction details
      expect(EvoStrings.transactionDetailsTitle, 'Transaction details');
      expect(EvoStrings.paymentTitle, 'Payment');
      expect(EvoStrings.amountTitle, 'Amount');
      expect(EvoStrings.transactionIdTitle, 'Transaction ID');
      expect(EvoStrings.paidToTitle, 'Paid to');
      expect(EvoStrings.paidByTitle, 'Paid by');
      expect(EvoStrings.dateAndTimeTitle, 'Date and time');
      expect(EvoStrings.postingDateTitle, 'Posting Date');

      // Verify Username
      expect(EvoStrings.verifyUsernameTitle, 'Hello, can you input your username?');
      expect(EvoStrings.forgotUsernameTitle, 'Forgot Username?');

      // Activation Status
      expect(EvoStrings.activationRejectedTitle, 'Sorry, please try applying again another time.');
      expect(EvoStrings.activationRejectedDesc,
          'Thanks for your interest in applying for a Kyko credit card. Unfortunately, we can\'t proceed with your application at this time.');
      expect(EvoStrings.activationProcessingTitle, 'Your application is still being processed.');
      expect(EvoStrings.activationProcessingDesc,
          'We\'ll send the result to you via SMS and email as soon as we\'re done processing your application.');
      expect(EvoStrings.activationNotFoundTitle, 'Sorry, we can\'t seem to find your record.');
      expect(EvoStrings.activationNotFoundDesc,
          'It seems you haven\'t applied for a Kyko credit card using that number.');
      expect(EvoStrings.activationExistTitle, 'You already have an activated account with Kyko.');
      expect(EvoStrings.activationExistDesc, 'You can try logging in instead.');

      // Account activation - Verify email
      expect(EvoStrings.verifyEmailTitle, 'Verify your email address');
      expect(EvoStrings.verifyEmailDesc,
          'To begin using Kyko, you will need to verify your email address.');
      expect(EvoStrings.verifyEmailSubtitle, 'Is this your email address?');
      expect(EvoStrings.sendEmailCodeBtn, 'Yes, send me a verification code');
      expect(EvoStrings.changeEmailBtn, 'No, change my email address');

      // Account activation - Input email
      expect(EvoStrings.inputEmailTitle, 'Input your correct email address');
      expect(EvoStrings.sendEmailCodeBtn2, 'Send me a verification code');
      expect(EvoStrings.errorInvalidEmailAddress, 'Invalid email address');

      // Account activation - Verify OTP
      expect(EvoStrings.ctaBackToActivationAccount, 'Back to account activation');
      expect(EvoStrings.contentActivationAccountSessionTokenExpired,
          'You can redo the account activation process by clicking the button below.');
    });
  });
}
