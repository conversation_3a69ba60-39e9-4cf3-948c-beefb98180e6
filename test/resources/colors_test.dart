import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  final EvoColors evoColors = EvoColors();

  // Page Colors
  group('test Common Colors', () {
    test('primary color should be primaryBase', () {
      expect(evoColors.primary, evoColors.primaryBase);
    });

    test('foreground color should be greyScale100', () {
      expect(evoColors.foreground, evoColors.greyScale100);
    });

    test('background color should be 0xFFFAFAFA', () {
      expect(evoColors.background, evoColors.greyScale50);
    });

    test('error color should be errorBase', () {
      expect(evoColors.error, evoColors.errorBase);
    });

    test('highlighted color should be primary100', () {
      expect(evoColors.highlighted, evoColors.primary100);
    });

    test('appBarShadow color should be 0x14000000', () {
      expect(evoColors.appBarShadow, const Color(0x14000000));
    });

    // Bottom Sheet Colors
    test('bottomSheetBackground color should be defaultWhite', () {
      expect(evoColors.bottomSheetBackground, evoColors.defaultWhite);
    });

    test('bottomSheetSelectedItem color should be primary100', () {
      expect(evoColors.bottomSheetSelectedItem, evoColors.primary100);
    });

    test('bottomSheetUnselectedItem color should be 0xFF999999', () {
      expect(evoColors.bottomSheetUnselectedItem, evoColors.greyScale80);
    });

    // Text Colors
    test('test Text Colors', () {
      expect(evoColors.textActive, evoColors.grayText);
      expect(evoColors.textPassive, evoColors.greyScale90);
      expect(evoColors.textPassive2, const Color(0xFF999999));
      expect(evoColors.textHint, evoColors.greyScale70);
      expect(evoColors.textNormal, const Color(0xFF626A84));
      expect(evoColors.icon, evoColors.accent90);
    });

    // TextField Colors
    test('test TextField Colors', () {
      expect(evoColors.focusedTextFieldBorder, evoColors.primary90);
      expect(evoColors.textFieldBorder, evoColors.greyScale70);
      expect(evoColors.disableTextFieldBorder, evoColors.greyScale60);
      expect(evoColors.disableTextFieldBg, evoColors.greyATrans5);
      expect(evoColors.textFieldBg, evoColors.defaultWhite);
      expect(evoColors.textSelectedBg, evoColors.defaultWhite);
    });

    // Button Colors
    test('test Button Colors', () {
      // primary
      expect(evoColors.primaryButtonForeground, Colors.white);
      expect(evoColors.primaryButtonBg, evoColors.greyScale100);
      expect(evoColors.primaryButtonForegroundDisable, Colors.white);
      expect(evoColors.primaryButtonBgDisable, evoColors.greyScale70);

      //secondary
      expect(evoColors.secondaryButtonForeground, evoColors.greyScale100);
      expect(evoColors.secondaryButtonBg, evoColors.greyScale50);
      expect(evoColors.secondaryButtonForegroundDisable, evoColors.greyScale70);
      expect(evoColors.secondaryButtonBgDisable, evoColors.greyScale50);

      //accent
      expect(evoColors.accentButtonForeground, Colors.white);
      expect(evoColors.accentButtonBg, evoColors.primary100);
      expect(evoColors.accentButtonForegroundDisable, Colors.white);
      expect(evoColors.accentButtonBgDisable, evoColors.primary80);

      //tertiary
      expect(evoColors.tertiaryButtonForeground, evoColors.greyScale100);
      expect(evoColors.tertiaryButtonBg, evoColors.defaultTransparent);
      expect(evoColors.tertiaryButtonForegroundDisable, evoColors.greyScale70);
      expect(evoColors.tertiaryButtonBgDisable, evoColors.defaultTransparent);
    });

    // OTP Text Input Field Colors
    test('test OTP Text Input Field Colors', () {
      expect(evoColors.inputFocusedColor, evoColors.primary100);
      expect(evoColors.inputUnfocusedColor, const Color(0xFFA1A7BA));
    });

    test('test Radio Button', () {
      expect(evoColors.selectedRadioButton, evoColors.primary100);
    });

    // WebView Loading Progress Colors
    test('test WebView Loading Progress Colors', () {
      expect(evoColors.webViewProgressBg, evoColors.accent70);
      expect(evoColors.webViewProgressValue, evoColors.accent90);
    });

    test('iconColor should be greyScale100', () {
      expect(evoColors.iconColor, evoColors.greyScale100);
    });

    test('loadingViewColor should be accent90', () {
      expect(evoColors.loadingViewColor, evoColors.accent90);
    });
  });

  // Evo's Color System
  group('test EVO System Colors', () {
    test('test default system colors', () {
      expect(evoColors.defaultWhite, Colors.white);
      expect(evoColors.defaultBlack, Colors.black);
      expect(evoColors.defaultTransparent, Colors.transparent);
      expect(evoColors.negativeButtonForeground, evoColors.defaultTransparent);
      expect(evoColors.negativeButtonBg, evoColors.defaultTransparent);
      expect(evoColors.negativeButtonForegroundDisable, evoColors.defaultTransparent);
      expect(evoColors.negativeButtonBgDisable, evoColors.defaultTransparent);
    });

    // Grey Scale Colors
    test('test grey scale colors', () {
      expect(evoColors.greyScale100, const Color(0xFF1D1D1D));
      expect(evoColors.greyScale90, const Color(0xFF5E5E5E));
      expect(evoColors.greyScale80, const Color(0xFF999999));
      expect(evoColors.greyScale75, const Color(0xFFC2C2C2));
      expect(evoColors.greyScale70, const Color(0xFFD1D1D1));
      expect(evoColors.greyScale60, const Color(0xFFE9E9E9));
      expect(evoColors.greyScale50, const Color(0xFFFAFAFA));
    });

    // Grey Scale Alpha Colors
    test('test grey scale alpha colors', () {
      expect(evoColors.greyATrans10, const Color(0x141d1d1d));
      expect(evoColors.greyATrans5, const Color(0x0a1d1d1d));
    });

    // Primary Colors
    test('test primary colors', () {
      expect(evoColors.primary100, const Color(0xFF09B364));
      expect(evoColors.primary90, const Color(0xFF1DC978));
      expect(evoColors.primary80, const Color(0xFFD5F6E7));
      expect(evoColors.primary70, const Color(0xFFECF9F3));
      expect(evoColors.primary00, const Color(0xFF0D6D40));
    });

    // Accent Colors
    test('test accent colors', () {
      expect(evoColors.accent100, const Color(0xFF37366F));
      expect(evoColors.accent90, const Color(0xFF6867A8));
      expect(evoColors.accent70, const Color(0xFFEBEBFF));
    });

    test('test info colors', () {
      expect(evoColors.info100, const Color(0xFF1F71F4));
      expect(evoColors.info80, const Color(0xFFBFDBFE));
      expect(evoColors.info70, const Color(0xFFEBF3FF));
    });

    test('test positive colors', () {
      expect(evoColors.positive100, const Color(0xFF3CBE69));
      expect(evoColors.positive70, const Color(0xFFEAFAF0));
    });

    test('test warning colors', () {
      expect(evoColors.warning100, const Color(0xFFFFB224));
      expect(evoColors.warning70, const Color(0xFFFFF6E5));
    });

    test('test error colors', () {
      expect(evoColors.error100, const Color(0xFFE54D2E));
      expect(evoColors.error70, const Color(0xFFFFE5DC));
    });
  });

  // Additional Colors
  group('test Additional Colors', () {
    test('activeIndicator should be 0xFF222222', () {
      expect(evoColors.activeIndicator, const Color(0xFF222222));
    });

    // Profile Settings Colors
    test('settingsCardShadow should be 0x0A000000', () {
      expect(evoColors.shadowColor, const Color(0x0A000000));
    });

    test('Test Utility button colors', () {
      expect(evoColors.utilityButtonForeground, evoColors.primary100);
      expect(evoColors.utilityButtonBg, evoColors.defaultTransparent);
      expect(evoColors.utilityButtonForegroundDisable, evoColors.primary70);
      expect(evoColors.utilityButtonBgDisable, evoColors.defaultTransparent);
      expect(evoColors.utilityButtonBorder, const Color(0xFFA1A7BA));
    });

    test('Test common colors', () {
      expect(evoColors.screenTitle, const Color(0xFF16181D));
    });

    test('Test Popup button colors', () {
      expect(evoColors.popupButtonForeground, evoColors.primary100);
      expect(evoColors.popupButtonBg, evoColors.defaultTransparent);
      expect(evoColors.popupButtonForegroundDisable, evoColors.primary70);
      expect(evoColors.popupButtonBgDisable, evoColors.defaultTransparent);
      expect(evoColors.popupButtonBorder, const Color(0x1A000000));
    });

    test('Test Activate Card Success', () {
      expect(evoColors.activateCardSuccessText, const Color(0xFFFCFDFD));
    });
  });

  group('Kyko design system', () {
    test('should have correct colors', () {
      expect(evoColors.primaryBase, Color(0xFFB83A8D));
      expect(evoColors.primaryBackground, Color(0xFFFEF7FB));
      expect(evoColors.primaryInteractive, Color(0xFFFADDED));
      expect(evoColors.primaryBorders, Color(0xFFE5ADCD));
      expect(evoColors.primaryDark, Color(0xFFA82A7F));
      expect(evoColors.primaryText, Color(0xFF611948));

      expect(evoColors.secondaryBase, Color(0xFF65BA74));
      expect(evoColors.secondaryBackground, Color(0xFFF5FBF5));
      expect(evoColors.secondaryInteractive, Color(0xFFDAF1DB));
      expect(evoColors.secondaryBorders, Color(0xFFB2DDB5));
      expect(evoColors.secondaryDark, Color(0xFF2A7E3B));
      expect(evoColors.secondaryText, Color(0xFF203C25));

      expect(evoColors.accentBase, Color(0xFF8CA8DC));
      expect(evoColors.accentBackground, Color(0xFFF7F9FD));
      expect(evoColors.accentInteractive, Color(0xFFE1EAFB));
      expect(evoColors.accentBorders, Color(0xFFC0D3F5));
      expect(evoColors.accentDark, Color(0xFF4C6695));
      expect(evoColors.accentText, Color(0xFF203050));

      expect(evoColors.successBase, Color(0xFF30A46C));
      expect(evoColors.successBackground, Color(0xFFF4FBF6));
      expect(evoColors.successInteractive, Color(0xFFD6F1DF));
      expect(evoColors.successBorders, Color(0xFF8ECEAA));
      expect(evoColors.successText, Color(0xFF193B2D));

      expect(evoColors.warningBase, Color(0xFFFFBA18));
      expect(evoColors.warningBackground, Color(0xFFFEFBE9));
      expect(evoColors.warningInteractive, Color(0xFFFFEE9C));
      expect(evoColors.warningBorders, Color(0xFFE9C162));
      expect(evoColors.warningText, Color(0xFF4F3422));

      expect(evoColors.errorBase, Color(0xFFE5484D));
      expect(evoColors.errorBackground, Color(0xFFFFF7F7));
      expect(evoColors.errorInteractive, Color(0xFFFFDBDC));
      expect(evoColors.errorBorders, Color(0xFFF4A9AA));
      expect(evoColors.errorText, Color(0xFF641723));

      expect(evoColors.informationBase, Color(0xFF0090FF));
      expect(evoColors.informationBackground, Color(0xFFF4FAFF));
      expect(evoColors.informationInteractive, Color(0xFFD5EFFF));
      expect(evoColors.informationBorders, Color(0xFF8EC8F6));
      expect(evoColors.informationText, Color(0xFF113264));

      expect(evoColors.grayBase, Color(0xFF8D8D86));
      expect(evoColors.grayBackground, Color(0xFFFDFDFC));
      expect(evoColors.grayInteractive, Color(0xFFE9E8E6));
      expect(evoColors.grayBorders, Color(0xFFCFCECA));
      expect(evoColors.grayText, Color(0xFF21201C));

      expect(evoColors.black, Color(0xFF000000));
      expect(evoColors.white, Color(0xFFFFFFFF));
      expect(evoColors.transparent, Color(0x00000000));
    });
  });
}
