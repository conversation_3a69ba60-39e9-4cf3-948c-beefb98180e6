import 'dart:async';

import 'package:evoapp/base/evo_page_state_base.dart';
import 'package:evoapp/feature/authorization_session_expired/authorization_session_expired_handler.dart';
import 'package:evoapp/feature/authorization_session_expired/authorization_session_expired_popup.dart';
import 'package:evoapp/feature/authorization_session_expired/force_logout_popup.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/feature/server_logging/common_navigator_observer.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/network_manager.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import 'evo_page_state_base_test_config.dart';

class MockStreamSubscription<T> extends Mock implements StreamSubscription<T> {}

class MockStream<T> extends Mock implements Stream<T> {}

class MockEvoSnackBar extends Mock implements EvoSnackBar {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class TestPageBase extends PageBase {
  final EventTrackingScreenId fakeEventTrackingScreenId = EventTrackingScreenId.undefined;
  final RouteSettings fakeRouteSettings = const RouteSettings(name: 'fake_route_name');

  const TestPageBase({super.key});

  @override
  TestEvoPageStateBase createState() => TestEvoPageStateBase();

  @override
  EventTrackingScreenId get eventTrackingScreenId => fakeEventTrackingScreenId;

  @override
  RouteSettings get routeSettings => fakeRouteSettings;
}

class TestEvoPageStateBase extends EvoPageStateBase<PageBase> {
  bool mountedValue = true;

  @override
  bool get mounted => mountedValue;

  bool isTopVisibleValue = true;

  @override
  bool isTopVisible() => isTopVisibleValue;

  bool hasCallDidPopNext = false;

  @override
  void didPopNext() {
    super.didPopNext();
    hasCallDidPopNext = true;
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return Container();
  }

  bool hasListenAuthorizationSessionExpiredValue = true;

  @override
  bool hasListenAuthorizationSessionExpired() => hasListenAuthorizationSessionExpiredValue;

  bool hasCallHandleBiometricChangedIfNeed = false;

  @override
  Future<void> handleBiometricChangedIfNeed() async {
    hasCallHandleBiometricChangedIfNeed = true;
  }

  bool hasCallHandleBiometricTokenUnUsable = false;

  @override
  Future<void> handleBiometricTokenUnUsable() async {
    hasCallHandleBiometricTokenUnUsable = true;
  }

  void resetDefaultValue() {
    mountedValue = true;
    isTopVisibleValue = true;
    hasListenAuthorizationSessionExpiredValue = true;
    hasCallHandleBiometricChangedIfNeed = false;
    hasCallHandleBiometricTokenUnUsable = false;
    hasCallDidPopNext = false;
  }
}

/// Class is created to test hasListenAuthorizationSessionExpired() method
class FakeEvoPageStateBase extends EvoPageStateBase<PageBase> {
  @override
  Widget getContentWidget(BuildContext context) {
    return Container();
  }
}

void main() {
  late AuthorizationSessionExpiredHandler mockAuthorizationSessionExpiredHandler;
  late AuthorizationSessionExpiredPopup mockAuthorizationSessionExpiredPopup;
  late NetworkManager mockNetworkManager;
  late EvoUtilFunction mockEvoUtilFunction;
  late EvoSnackBar mockEvoSnackBar;
  late EvoLocalStorageHelper mockEvoLocalStorageHelper;
  late CommonNavigatorObserver mockCommonNavigatorObserver;
  late AppState appState;
  late ForceLogoutPopup mockForceLogoutPopup;

  late TestPageBase testEvoPageBase;
  late TestEvoPageStateBase testEvoPageBaseState;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    registerFallbackValue(SnackBarType.success);

    initConfigEvoPageStateBase();

    appState = getIt.get<AppState>();

    mockAuthorizationSessionExpiredHandler = getIt.get<AuthorizationSessionExpiredHandler>();

    mockAuthorizationSessionExpiredPopup = getIt.get<AuthorizationSessionExpiredPopup>();

    mockForceLogoutPopup = getIt.get<ForceLogoutPopup>();

    mockNetworkManager = getIt.get<NetworkManager>();

    mockCommonNavigatorObserver = getIt.get<CommonNavigatorObserver>();

    mockEvoUtilFunction = getIt.get<EvoUtilFunction>();

    getIt.registerSingleton<EvoSnackBar>(MockEvoSnackBar());
    mockEvoSnackBar = getIt.get<EvoSnackBar>();

    getIt.registerSingleton<EvoLocalStorageHelper>(MockEvoLocalStorageHelper());
    mockEvoLocalStorageHelper = getIt.get<EvoLocalStorageHelper>();

    when(() => mockEvoSnackBar.show(
          any(),
          typeSnackBar: any(named: 'typeSnackBar'),
          durationInMilliSec: any(named: 'durationInMilliSec'),
          description: any(named: 'description'),
          marginBottomRatio: any(named: 'marginBottomRatio'),
        )).thenAnswer((_) async {
      return Future<bool?>.value();
    });
  });

  setUp(() {
    testEvoPageBase = const TestPageBase();
    testEvoPageBaseState = TestEvoPageStateBase();

    when(() => mockNetworkManager.myStreamNetwork).thenAnswer(
      (_) => Stream<bool>.fromIterable(<bool>[true]),
    );

    when(() => mockAuthorizationSessionExpiredHandler.getStreamSubscription()).thenAnswer((_) =>
        Stream<UnauthorizedSessionState>.fromIterable(
            <UnauthorizedSessionState>[UnauthorizedSessionState.invalidToken]));

    when(() => mockAuthorizationSessionExpiredPopup.checkCanShowPopup()).thenReturn(false);
    when(() => mockAuthorizationSessionExpiredPopup.show())
        .thenAnswer((_) async => Future<void>.value());

    when(() => mockCommonNavigatorObserver.topStackIsAPageRoute()).thenReturn(false);

    when(() => mockEvoUtilFunction.clearDataOnTokenInvalid()).thenAnswer((_) async {
      return Future<void>.value();
    });
  });

  tearDown(() {
    reset(mockAuthorizationSessionExpiredHandler);
    reset(mockAuthorizationSessionExpiredPopup);
    reset(mockEvoUtilFunction);
    reset(mockForceLogoutPopup);
  });

  tearDownAll(() {
    getIt.reset();
  });

  Future<TestEvoPageStateBase> initPageBaseWidget(WidgetTester tester) async {
    await tester.runAsync(() async {
      await tester.pumpWidget(testEvoPageBase);
    });

    final State state = tester.state(find.byType(TestPageBase));
    expect(state is TestEvoPageStateBase, true);
    return state as TestEvoPageStateBase;
  }

  Future<void> delayPumpWidget(WidgetTester tester) async {
    await tester.pump(const Duration(milliseconds: 500));
  }

  testWidgets('verify Lifecycle of EvoPageStateBase', (WidgetTester tester) async {
    when(() => mockAuthorizationSessionExpiredHandler.getStreamSubscription()).thenAnswer(
        (_) => Stream<UnauthorizedSessionState>.fromIterable(<UnauthorizedSessionState>[]));

    final TestEvoPageStateBase testEvoPageBaseState = await initPageBaseWidget(tester);

    // verify initState()
    final String screenId = testEvoPageBase.eventTrackingScreenId.name;
    expect(screenId, testEvoPageBase.fakeEventTrackingScreenId.name);
    expect(appState.eventTrackingSharedData.currentScreenId?.name, screenId);

    expect(testEvoPageBaseState.hasListenNetwork(), true);
    verify(() => mockNetworkManager.myStreamNetwork).called(1);

    expect(testEvoPageBaseState.hasListenAuthorizationSessionExpired(), true);

    verifyNever(() => mockEvoUtilFunction.clearDataOnTokenInvalid());
    verifyNever(() => mockEvoUtilFunction.clearUserInfoAppState());

    verifyNever(() => mockAuthorizationSessionExpiredPopup.checkCanShowPopup());
    verifyNever(() => mockForceLogoutPopup.checkCanShowPopup());

    // verify when call onResumed() with isTopVisible = true
    testEvoPageBaseState.isTopVisibleValue = true;
    await testEvoPageBaseState.onResumed();

    await delayPumpWidget(tester);

    expect(testEvoPageBaseState.hasCallHandleBiometricChangedIfNeed, true);
    expect(testEvoPageBaseState.hasCallHandleBiometricTokenUnUsable, true);

    // Reset value after calling onResumed()
    testEvoPageBaseState.resetDefaultValue();

    // verify when call onResumed() with isTopVisible = false
    testEvoPageBaseState.isTopVisibleValue = false;

    await testEvoPageBaseState.onResumed();

    await delayPumpWidget(tester);

    expect(testEvoPageBaseState.hasCallHandleBiometricChangedIfNeed, false);
    expect(testEvoPageBaseState.hasCallHandleBiometricTokenUnUsable, false);

    // Reset value after calling onResumed()
    testEvoPageBaseState.resetDefaultValue();

    // verify didPopNext()
    testEvoPageBaseState.didPopNext();
    expect(testEvoPageBaseState.hasCallDidPopNext, true);

    // Reset value after calling didPopNext()
    testEvoPageBaseState.resetDefaultValue();
  });

  group('verify handleBiometric()', () {
    test('verify method handleBiometric()', () async {
      expect(testEvoPageBaseState.hasCallHandleBiometricChangedIfNeed, false);
      expect(testEvoPageBaseState.hasCallHandleBiometricTokenUnUsable, false);

      await testEvoPageBaseState.handleBiometric();

      expect(testEvoPageBaseState.hasCallHandleBiometricChangedIfNeed, true);
      expect(testEvoPageBaseState.hasCallHandleBiometricTokenUnUsable, true);
    });
  });

  group('verify handleEvoApiError()', () {
    const int statusCode = 404;
    const String fakeVerdict = 'fake_verdict';
    const String errorMessage = 'Not Found';
    final ErrorUIModel errorUIModel = ErrorUIModel(
      statusCode: CommonHttpClient.INVALID_TOKEN,
      verdict: fakeVerdict,
      userMessage: errorMessage,
    );

    test('verify method with statusCode = 404', () async {
      final ErrorUIModel errorUIModel = ErrorUIModel(
        statusCode: statusCode,
        verdict: fakeVerdict,
        userMessage: errorMessage,
      );

      await testEvoPageBaseState.handleEvoApiError(errorUIModel);

      verifyNever(() => mockEvoUtilFunction.clearDataOnTokenInvalid());
      verify(() => mockEvoSnackBar.show(
            errorMessage,
            typeSnackBar: any(named: 'typeSnackBar'),
            durationInMilliSec: any(named: 'durationInMilliSec'),
            description: any(named: 'description'),
            marginBottomRatio: any(named: 'marginBottomRatio'),
          )).called(1);
    });

    test(
        'verify method with statusCode = CommonHttpClient.INVALID_TOKEN and has Listen Authorization Session Expired',
        () async {
      await testEvoPageBaseState.handleEvoApiError(errorUIModel);

      verify(() => mockEvoUtilFunction.clearDataOnTokenInvalid()).called(1);
      verifyNever(() => mockEvoSnackBar.show(
            any(),
            typeSnackBar: any(named: 'typeSnackBar'),
            durationInMilliSec: any(named: 'durationInMilliSec'),
            description: any(named: 'description'),
            marginBottomRatio: any(named: 'marginBottomRatio'),
          ));
    });

    test(
        'verify method with statusCode = CommonHttpClient.INVALID_TOKEN and has not Listen Authorization Session Expired',
        () async {
      testEvoPageBaseState.hasListenAuthorizationSessionExpiredValue = false;

      await testEvoPageBaseState.handleEvoApiError(errorUIModel);

      verify(() => mockEvoUtilFunction.clearDataOnTokenInvalid()).called(1);
      verify(() => mockEvoSnackBar.show(
            errorMessage,
            typeSnackBar: any(named: 'typeSnackBar'),
            durationInMilliSec: any(named: 'durationInMilliSec'),
            description: any(named: 'description'),
            marginBottomRatio: any(named: 'marginBottomRatio'),
          )).called(1);
    });
  });

  group('verify SnackBar', () {
    test('verify call showSnackBar() with default value', () {
      const String fakeMessage = 'fake_message';

      testEvoPageBaseState.showSnackBar(fakeMessage);

      final List<dynamic> capturedData = verify(() => mockEvoSnackBar.show(
            fakeMessage,
            typeSnackBar: captureAny(named: 'typeSnackBar'),
            durationInMilliSec: captureAny(named: 'durationInMilliSec'),
            description: captureAny(named: 'description'),
            marginBottomRatio: captureAny(named: 'marginBottomRatio'),
          )).captured;

      expect(capturedData[0], SnackBarType.success);
      expect(capturedData[1], SnackBarDuration.short.value);
      expect(capturedData[2], null);
      expect(capturedData[3], null);
    });

    test('verify showSnackBarError()', () {
      const String fakeMessage = 'fake_message';

      testEvoPageBaseState.showSnackBarError(fakeMessage);

      verify(() => mockEvoSnackBar.show(
            fakeMessage,
            typeSnackBar: SnackBarType.error,
            durationInMilliSec: SnackBarDuration.short.value,
            description: any(named: 'description'),
            marginBottomRatio: any(named: 'marginBottomRatio'),
          )).called(1);
    });

    test('verify showSnackBarWarning()', () {
      const String fakeMessage = 'fake_message';

      testEvoPageBaseState.showSnackBarWarning(fakeMessage);

      verify(() => mockEvoSnackBar.show(
            fakeMessage,
            typeSnackBar: SnackBarType.warning,
            durationInMilliSec: SnackBarDuration.short.value,
            description: any(named: 'description'),
            marginBottomRatio: any(named: 'marginBottomRatio'),
          )).called(1);
    });

    test('verify showSnackBarNeutral()', () {
      const String fakeMessage = 'fake_message';

      testEvoPageBaseState.showSnackBarNeutral(fakeMessage);

      verify(() => mockEvoSnackBar.show(
            fakeMessage,
            typeSnackBar: SnackBarType.neutral,
            durationInMilliSec: SnackBarDuration.short.value,
            description: any(named: 'description'),
            marginBottomRatio: any(named: 'marginBottomRatio'),
          )).called(1);
    });
  });

  group('verify updateUserLoginStatus()', () {
    late AppState appState;

    setUp(() {
      appState = getIt.get<AppState>();
    });

    test('updateUserLoginStatus() with isLogin = false', () {
      testEvoPageBaseState.updateUserLoginStatus(false);

      expect(appState.isUserLogIn, false);
    });

    test('updateUserLoginStatus() with isLogin = true', () {
      testEvoPageBaseState.updateUserLoginStatus(true);

      expect(appState.isUserLogIn, true);
    });
  });

  group('verify isNewDevice()', () {
    late TestEvoPageStateBase testPageState;

    setUp(() {
      testPageState = TestEvoPageStateBase();
    });

    test('isNewDevice() return true', () async {
      when(() => mockEvoLocalStorageHelper.isNewDevice()).thenAnswer((_) async => true);

      final bool result = await testPageState.isNewDevice();

      expect(result, true);
    });

    test('isNewDevice() return false', () async {
      when(() => mockEvoLocalStorageHelper.isNewDevice()).thenAnswer((_) async => false);

      final bool result = await testPageState.isNewDevice();

      expect(result, false);
    });
  });

  group('verify initAuthorizationSessionExpiredSubscriptionNeed()', () {
    setUp(() {
      /// stub mockAuthorizationSessionExpiredPopup
      when(() => mockAuthorizationSessionExpiredPopup.checkCanShowPopup()).thenReturn(true);
      when(() => mockAuthorizationSessionExpiredPopup.show())
          .thenAnswer((_) async => Future<void>.value());

      /// stub mockForceLogoutPopup
      when(() => mockForceLogoutPopup.checkCanShowPopup()).thenReturn(true);
      when(() => mockForceLogoutPopup.show()).thenAnswer((_) async => Future<void>.value());
    });

    tearDown(() {
      reset(mockAuthorizationSessionExpiredHandler);
      reset(mockForceLogoutPopup);
    });

    test('verify hasListenAuthorizationSessionExpired = false', () {
      testEvoPageBaseState.hasListenAuthorizationSessionExpiredValue = false;

      testEvoPageBaseState.initAuthorizationSessionExpiredSubscriptionNeed();

      verifyNever(() => mockAuthorizationSessionExpiredHandler.getStreamSubscription());
      verifyNever(() => mockAuthorizationSessionExpiredPopup.checkCanShowPopup());
      verifyNever(() => mockAuthorizationSessionExpiredPopup.show());
      verifyNever(() => mockForceLogoutPopup.checkCanShowPopup());
      verifyNever(() => mockForceLogoutPopup.show());
    });

    test('verify hasListenAuthorizationSessionExpired = true and mounted = false', () async {
      // Change mounted value
      testEvoPageBaseState.mountedValue = false;

      testEvoPageBaseState.initAuthorizationSessionExpiredSubscriptionNeed();

      // Delay for emitting stream
      await Future<void>.delayed(const Duration(milliseconds: 500));

      verify(() => mockAuthorizationSessionExpiredHandler.getStreamSubscription()).called(1);
      verifyNever(() => mockAuthorizationSessionExpiredPopup.checkCanShowPopup());
      verifyNever(() => mockAuthorizationSessionExpiredPopup.show());
      verifyNever(() => mockForceLogoutPopup.checkCanShowPopup());
      verifyNever(() => mockForceLogoutPopup.show());
    });

    test(
        'verify when hasListenAuthorizationSessionExpired = true & mounted = true & UnauthorizedSessionState = invalid_token,'
        ' then show AuthorizationSessionExpired popup', () async {
      /// setup
      when(() => mockAuthorizationSessionExpiredHandler.getStreamSubscription()).thenAnswer(
        (_) => Stream<UnauthorizedSessionState>.fromIterable(
          <UnauthorizedSessionState>[UnauthorizedSessionState.invalidToken],
        ),
      );

      when(() => mockAuthorizationSessionExpiredPopup.checkCanShowPopup()).thenReturn(true);

      // Change mounted value
      testEvoPageBaseState.mountedValue = true;

      testEvoPageBaseState.initAuthorizationSessionExpiredSubscriptionNeed();

      // Delay for emitting stream
      await Future<void>.delayed(const Duration(milliseconds: 500));

      verify(() => mockAuthorizationSessionExpiredPopup.checkCanShowPopup()).called(1);
      verify(() => mockAuthorizationSessionExpiredPopup.show()).called(1);
    });

    test(
        'verify hasListenAuthorizationSessionExpired = true & mounted = true & UnauthorizedSessionState = forced_logout'
        ' then show forceLogout popup', () async {
      ///setup
      when(() => mockAuthorizationSessionExpiredHandler.getStreamSubscription()).thenAnswer(
        (_) => Stream<UnauthorizedSessionState>.fromIterable(
          <UnauthorizedSessionState>[UnauthorizedSessionState.forcedLogout],
        ),
      );

      // Change mounted value
      testEvoPageBaseState.mountedValue = true;

      testEvoPageBaseState.initAuthorizationSessionExpiredSubscriptionNeed();

      // Delay for emitting stream
      await Future<void>.delayed(const Duration(milliseconds: 500));

      verify(() => mockForceLogoutPopup.checkCanShowPopup()).called(1);
      verify(() => mockForceLogoutPopup.show()).called(1);
    });

    test(
        'verify hasListenAuthorizationSessionExpired = true & mounted = true & state = UnauthorizedSessionState.unknown'
        ' then show AuthorizationSessionExpired popup', () async {
      when(() => mockAuthorizationSessionExpiredHandler.getStreamSubscription()).thenAnswer(
        (_) => Stream<UnauthorizedSessionState>.fromIterable(
          <UnauthorizedSessionState>[UnauthorizedSessionState.unknown],
        ),
      );

      // Change mounted value
      testEvoPageBaseState.mountedValue = true;

      testEvoPageBaseState.initAuthorizationSessionExpiredSubscriptionNeed();

      // Delay for emitting stream
      await Future<void>.delayed(const Duration(milliseconds: 500));

      verify(() => mockAuthorizationSessionExpiredPopup.checkCanShowPopup()).called(1);
      verify(() => mockAuthorizationSessionExpiredPopup.show()).called(1);
    });
  });

  group('verify cancelAuthorizationSessionExpiredSubscriptionIfNeed()', () {
    setUp(() {
      testEvoPageBaseState.authorizationSessionExpiredSubscription =
          MockStreamSubscription<UnauthorizedSessionState>();

      when(() => testEvoPageBaseState.authorizationSessionExpiredSubscription?.cancel())
          .thenAnswer((_) async => Future<void>.value());
    });

    test('verify method with hasListenAuthorizationSessionExpired = true', () {
      testEvoPageBaseState.cancelAuthorizationSessionExpiredSubscriptionIfNeed();

      verify(() => testEvoPageBaseState.authorizationSessionExpiredSubscription?.cancel())
          .called(1);
    });

    test('verify method with hasListenAuthorizationSessionExpired = false', () {
      testEvoPageBaseState.hasListenAuthorizationSessionExpiredValue = false;

      testEvoPageBaseState.cancelAuthorizationSessionExpiredSubscriptionIfNeed();

      verifyNever(() => testEvoPageBaseState.authorizationSessionExpiredSubscription?.cancel());
    });
  });

  group('verify showAuthorizationSessionTimeoutPopupIfNeed()', () {
    setUp(() {
      when(() => mockAuthorizationSessionExpiredPopup.checkCanShowPopup()).thenReturn(true);
    });

    tearDown(() {
      reset(mockAuthorizationSessionExpiredPopup);
    });

    test('verify method with checkCanShowPopup = true', () {
      testEvoPageBaseState.showAuthorizationSessionTimeoutPopupIfNeed();

      verify(() => mockAuthorizationSessionExpiredPopup.checkCanShowPopup()).called(1);
      verify(() => mockAuthorizationSessionExpiredPopup.show()).called(1);
    });

    test('verify method with checkCanShowPopup = false', () {
      when(() => mockAuthorizationSessionExpiredPopup.checkCanShowPopup()).thenReturn(false);

      testEvoPageBaseState.showAuthorizationSessionTimeoutPopupIfNeed();

      verify(() => mockAuthorizationSessionExpiredPopup.checkCanShowPopup()).called(1);
      verifyNever(() => mockAuthorizationSessionExpiredPopup.show());
    });
  });

  group('verify showForceLogoutPopIfNeed()', () {
    setUp(() {
      when(() => mockForceLogoutPopup.checkCanShowPopup()).thenReturn(true);
      when(() => mockForceLogoutPopup.show()).thenAnswer((_) async => Future<void>.value());
    });

    test('verify method with checkCanShowPopup = true', () {
      testEvoPageBaseState.showForceLogoutPopIfNeed();

      verify(() => mockForceLogoutPopup.checkCanShowPopup()).called(1);
      verify(() => mockForceLogoutPopup.show()).called(1);
    });

    test('verify method with checkCanShowPopup = false', () {
      when(() => mockForceLogoutPopup.checkCanShowPopup()).thenReturn(false);

      testEvoPageBaseState.showForceLogoutPopIfNeed();

      verify(() => mockForceLogoutPopup.checkCanShowPopup()).called(1);
      verifyNever(() => mockForceLogoutPopup.show());
    });
  });

  test('verify hasListenAuthorizationSessionExpired()', () {
    final FakeEvoPageStateBase testPageState = FakeEvoPageStateBase();
    expect(testPageState.hasListenAuthorizationSessionExpired(), true);
  });

  group('verify appLifecycleState in appState', () {
    testWidgets('verify appLifecycleState in appState', (WidgetTester tester) async {
      expect(appState.appLifecycleState, null);

      final TestEvoPageStateBase testEvoPageBaseState = await initPageBaseWidget(tester);

      testEvoPageBaseState.didChangeAppLifecycleState(AppLifecycleState.detached);
      expect(appState.appLifecycleState, AppLifecycleState.detached);

      testEvoPageBaseState.didChangeAppLifecycleState(AppLifecycleState.resumed);
      expect(appState.appLifecycleState, AppLifecycleState.resumed);

      testEvoPageBaseState.didChangeAppLifecycleState(AppLifecycleState.inactive);
      expect(appState.appLifecycleState, AppLifecycleState.inactive);

      testEvoPageBaseState.didChangeAppLifecycleState(AppLifecycleState.hidden);
      expect(appState.appLifecycleState, AppLifecycleState.hidden);

      testEvoPageBaseState.didChangeAppLifecycleState(AppLifecycleState.paused);
      expect(appState.appLifecycleState, AppLifecycleState.paused);
    });
  });
}
