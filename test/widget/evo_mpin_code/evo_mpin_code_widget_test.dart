import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/default_obscure_widget.dart';
import 'package:evoapp/widget/evo_mpin_code/evo_mpin_code_config.dart';
import 'package:evoapp/widget/evo_mpin_code/evo_mpin_code_widget.dart';
import 'package:evoapp/widget/evo_pin_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_common_package/widget/pin_code/pin_code_widget.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';

class MockCallback extends Mock {
  void stringCallback(String value);

  void voidCallback();
}

void main() {
  const String fakeTitle = 'fakeTitle';
  const String fakeErrorText = 'fakeErrorText';

  late TextEditingController textEditingController;
  late MockCallback onChange;
  late MockCallback onSubmit;
  late MockCallback onResetPin;
  late CommonImageProvider mockCommonImageProvider;

  setUpAll(() {
    getItRegisterColor();
    getItRegisterTextStyle();
    getItRegisterButtonStyle();
    getItRegisterMockCommonUtilFunctionAndImageProvider();

    mockCommonImageProvider = getIt.get<CommonImageProvider>();
    registerFallbackValue(BoxFit.none);
  });

  setUp(() {
    textEditingController = TextEditingController();
    onChange = MockCallback();
    onSubmit = MockCallback();
    onResetPin = MockCallback();

    when(() => mockCommonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          fit: any(named: 'fit'),
          color: any(named: 'color'),
          cornerRadius: any(named: 'cornerRadius'),
        )).thenAnswer((_) => Container());
  });

  void verifyTitle(String fakeTitle, WidgetTester tester) {
    final Finder titleFinder = find.text(fakeTitle);
    expect(titleFinder, findsOneWidget);
    final Text title = tester.widget<Text>(titleFinder);
    expect(title.style, evoTextStyles.regular(TextSize.base));
  }

  void verifyPinCodeWidget(
    WidgetTester tester, {
    required TextEditingController textEditingController,
    required Color inactiveColor,
    required Color activeColor,
    required Color selectedFillColor,
    required Color inactiveFillColor,
    required Color activeFillColor,
    bool autoFocus = true,
    bool autoUnFocus = false,
    FocusNode? focusNode,
    MockCallback? onChange,
    MockCallback? onSubmit,
  }) {
    final Finder pinCodeWidgetFinder = find.byType(CommonPinCode);
    expect(pinCodeWidgetFinder, findsOneWidget);
    final CommonPinCode pinCodeWidget = tester.widget<CommonPinCode>(pinCodeWidgetFinder);
    expect(pinCodeWidget.focusNode, focusNode);
    expect(pinCodeWidget.textController, textEditingController);
    expect(pinCodeWidget.autoFocus, autoFocus);
    expect(pinCodeWidget.autoUnFocus, autoUnFocus);

    /// Verify pinTheme
    expect(pinCodeWidget.pinTheme, isNotNull);
    expect(pinCodeWidget.pinTheme?.inactiveColor, inactiveColor);
    expect(pinCodeWidget.pinTheme?.activeColor, activeColor);
    expect(pinCodeWidget.pinTheme?.selectedFillColor, selectedFillColor);
    expect(pinCodeWidget.pinTheme?.inactiveFillColor, inactiveFillColor);
    expect(pinCodeWidget.pinTheme?.activeFillColor, activeFillColor);

    expect(pinCodeWidget.animationDuration, Duration.zero);
    expect(pinCodeWidget.obscuringWidget, isA<DefaultObscureWidget>());
    expect(pinCodeWidget.mainAxisAlignment, MainAxisAlignment.center);
    expect(pinCodeWidget.spaceBetweenItems, 0);
    expect(pinCodeWidget.pinLength, EvoMPINCodeConfig.defaultMPINCodeLength);
    expect(pinCodeWidget.textStyle, evoTextStyles.regular(TextSize.base));
    expect(pinCodeWidget.showCursor, isTrue);
    expect(pinCodeWidget.cursorHeight, EvoPinTextField.defaultPinCursorHeight);
    expect(pinCodeWidget.enableActiveFill, false);

    expect(pinCodeWidget.onChange, onChange == null ? isNull : isNotNull);
    expect(pinCodeWidget.onSubmit, onSubmit == null ? isNull : isNotNull);

    if (onChange != null) {
      const String onChangeStr = 'onChangeStr';
      pinCodeWidget.onChange?.call(onChangeStr);
      expect(verify(() => onChange.stringCallback(captureAny())).captured.single, onChangeStr);
    }

    if (onSubmit != null) {
      const String onSubmitStr = 'onSubmitStr';
      pinCodeWidget.onSubmit?.call(onSubmitStr);
      expect(verify(() => onSubmit.stringCallback(captureAny())).captured.single, onSubmitStr);
    }
  }

  Future<void> verifyResetMPIN(WidgetTester tester, {MockCallback? onResetPin}) async {
    expect(find.text(EvoStrings.forgotMPINQuestion), findsOneWidget);

    final Finder ctaFinder = find.text(EvoStrings.reset);
    expect(ctaFinder, findsOneWidget);
    if (onResetPin != null) {
      await tester.tap(ctaFinder);
      verify(() => onResetPin.voidCallback()).called(1);
    }
  }

  void verifyError(Matcher matcher) {
    expect(find.textContaining(fakeErrorText, findRichText: true), matcher);
  }

  testWidgets('Show EvoMPINCodeWidget with only required params', (WidgetTester tester) async {
    await tester.pumpWidget(MaterialApp(
      home: Scaffold(
        body: EvoMPINCodeWidget(
          textEditingController: textEditingController,
          title: fakeTitle,
        ),
      ),
    ));

    verifyTitle(fakeTitle, tester);

    verifyPinCodeWidget(
      tester,
      textEditingController: textEditingController,
      inactiveColor: evoColors.grayBorders,
      activeColor: evoColors.grayBorders,
      selectedFillColor: evoColors.defaultTransparent,
      inactiveFillColor: evoColors.defaultTransparent,
      activeFillColor: evoColors.defaultTransparent,
    );

    verifyError(findsNothing);

    await verifyResetMPIN(tester);
  });

  testWidgets('Show EvoMPINCodeWidget with all params', (WidgetTester tester) async {
    final FocusNode focusNode = FocusNode();
    const bool autoFocus = false;
    const bool autoUnFocus = true;

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: EvoMPINCodeWidget(
            textEditingController: textEditingController,
            title: fakeTitle,
            errorMessage: fakeErrorText,
            autoFocus: autoFocus,
            autoUnFocus: autoUnFocus,
            focusNode: focusNode,
            onSubmit: onSubmit.stringCallback,
            onChange: onChange.stringCallback,
            onResetPin: onResetPin.voidCallback,
          ),
        ),
      ),
    );

    verifyTitle(fakeTitle, tester);

    verifyPinCodeWidget(
      tester,
      textEditingController: textEditingController,
      inactiveColor: evoColors.error,
      activeColor: evoColors.error,
      selectedFillColor: evoColors.defaultTransparent,
      inactiveFillColor: evoColors.defaultTransparent,
      activeFillColor: evoColors.defaultTransparent,
      autoFocus: autoFocus,
      autoUnFocus: autoUnFocus,
      focusNode: focusNode,
      onChange: onChange,
      onSubmit: onSubmit,
    );

    verifyError(findsOneWidget);

    await verifyResetMPIN(tester, onResetPin: onResetPin);
  });
}
