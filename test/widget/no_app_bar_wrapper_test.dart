// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/no_app_bar_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';

import '../util/flutter_test_config.dart';

void main() {
  setUpAll(() {
    getItRegisterColor();
  });

  tearDownAll(() {
    getItUnregisterColor();
  });

  group('NoAppBarWrapper widget', () {
    testWidgets('should apply correct SystemUiOverlayStyle', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: NoAppBarWrapper(
            child: Text('Test Child'),
          ),
        ),
      );

      final AnnotatedRegion<SystemUiOverlayStyle> annotatedRegion =
          tester.widget<AnnotatedRegion<SystemUiOverlayStyle>>(
        find.byType(AnnotatedRegion<SystemUiOverlayStyle>),
      );
      expect(annotatedRegion.value.statusBarColor, evoColors.transparent);
      expect(annotatedRegion.value.statusBarIconBrightness, Brightness.dark);
      expect(annotatedRegion.value.statusBarBrightness, Brightness.light);
    });

    testWidgets('should have correct background color', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: NoAppBarWrapper(
            child: Text('Test Child'),
          ),
        ),
      );

      final ColoredBox coloredBox = tester.widget<ColoredBox>(find.byType(ColoredBox));
      expect(coloredBox.color, evoColors.grayBackground);
    });

    testWidgets('should contain SafeArea', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: NoAppBarWrapper(
            child: Text('Test Child'),
          ),
        ),
      );

      expect(find.byType(SafeArea), findsOneWidget);
    });

    testWidgets('should render child widget', (WidgetTester tester) async {
      const Key childKey = Key('child-widget-key');
      await tester.pumpWidget(
        const MaterialApp(
          home: NoAppBarWrapper(
            child: Text('Test Child', key: childKey),
          ),
        ),
      );

      expect(find.byKey(childKey), findsOneWidget);
    });

    testWidgets('should set a default top padding', (WidgetTester tester) async {
      final TextDirection direction = TextDirection.ltr;
      await tester.pumpWidget(
        Directionality(
          textDirection: direction,
          child: const NoAppBarWrapper(
            child: SizedBox(),
          ),
        ),
      );

      final Padding padding = tester.widget(find.byType(Padding).last);
      expect(padding.padding.resolve(direction), EdgeInsets.only(top: 16));
    });
  });
}
