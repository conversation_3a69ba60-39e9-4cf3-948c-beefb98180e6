import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/string_cta_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_test/flutter_test.dart';

import '../util/flutter_test_config.dart';

void main() {
  setUpAll(() {
    getItRegisterTextStyle();
    getItRegisterColor();
    getItRegisterButtonStyle();
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('StringCtaWidget', () {
    testWidgets('renders with correct text', (WidgetTester tester) async {
      // Arrange
      const String question = 'Test question';
      const String cta = 'Test CTA';

      // Act
      await tester.pumpWidget(const MaterialApp(
        home: Scaffold(
          body: StringCtaWidget(
            question: question,
            cta: cta,
            onTap: null,
          ),
        ),
      ));

      // Assert
      expect(find.text(question), findsOneWidget);
      expect(find.text(cta), findsOneWidget);
    });

    testWidgets('calls onTap when button is pressed', (WidgetTester tester) async {
      // Arrange
      const String question = 'Test question';
      const String cta = 'Test CTA';
      bool wasTapped = false;

      // Act
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: StringCtaWidget(
            question: question,
            cta: cta,
            onTap: () {
              wasTapped = true;
            },
          ),
        ),
      ));

      await tester.tap(find.byType(CommonButton));

      // Assert
      expect(wasTapped, true);
    });

    testWidgets('has correct styling when enabled', (WidgetTester tester) async {
      // Arrange
      const String question = 'Test question';
      const String cta = 'Test CTA';

      // Act
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: StringCtaWidget(
            question: question,
            cta: cta,
            onTap: () {},
          ),
        ),
      ));

      // Assert
      final Text textWidget = tester.widget<Text>(
        find.descendant(
          of: find.byType(CommonButton),
          matching: find.byType(Text),
        ),
      );

      expect(textWidget.style?.color, equals(evoColors.primary));
    });

    testWidgets('has correct styling when disabled', (WidgetTester tester) async {
      // Arrange
      const String question = 'Test question';
      const String cta = 'Test CTA';

      // Act
      await tester.pumpWidget(const MaterialApp(
        home: Scaffold(
          body: StringCtaWidget(
            question: question,
            cta: cta,
            onTap: null,
          ),
        ),
      ));

      // Assert
      final Text textWidget = tester.widget<Text>(
        find.descendant(
          of: find.byType(CommonButton),
          matching: find.byType(Text),
        ),
      );

      expect(textWidget.style?.color, equals(evoColors.greyScale70));
    });
  });
}
