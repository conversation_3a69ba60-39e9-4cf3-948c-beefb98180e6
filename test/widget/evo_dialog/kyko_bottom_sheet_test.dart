import 'package:evoapp/widget/evo_dialog/kyko_bottom_sheet.dart';
import 'package:evoapp/widget/evo_dialog/kyko_bottom_sheet_action.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../base/evo_page_state_base_test_config.dart';

void main() {
  setUpAll(() {
    initConfigEvoPageStateBase();
    setUpMockConfigEvoPageStateBase();
  });

  group('KykoBottomSheet', () {
    testWidgets('renders with title and content', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: KykoBottomSheet(
              title: 'Test Title',
              content: const Text('Test Content'),
            ),
          ),
        ),
      );

      // Verify title and content are displayed
      expect(find.text('Test Title'), findsOneWidget);
      expect(find.text('Test Content'), findsOneWidget);
    });

    testWidgets('renders with actions', (WidgetTester tester) async {
      // Build the widget with actions
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: KykoBottomSheet(
              title: 'Test Title',
              content: const Text('Test Content'),
              actions: [
                KykoBottomSheetAction.positive(
                  text: 'Confirm',
                  onPressed: () {},
                ),
                KykoBottomSheetAction.negative(
                  text: 'Cancel',
                  onPressed: () {},
                ),
              ],
            ),
          ),
        ),
      );

      // Verify actions are displayed
      expect(find.text('Confirm'), findsOneWidget);
      expect(find.text('Cancel'), findsOneWidget);
    });

    testWidgets('shows close button when hasCloseButton is true', (WidgetTester tester) async {
      // Build the widget with close button
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: KykoBottomSheet(
              title: 'Test Title',
              content: const Text('Test Content'),
              hasCloseButton: true,
            ),
          ),
        ),
      );

      // Verify close button is displayed (looking for the GestureDetector)
      expect(find.byType(GestureDetector), findsOneWidget);
    });

    testWidgets('action buttons trigger callbacks when tapped', (WidgetTester tester) async {
      bool positivePressed = false;
      bool negativePressed = false;

      // Build the widget with actions that set flags when pressed
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: KykoBottomSheet(
              title: 'Test Title',
              content: const Text('Test Content'),
              actions: [
                KykoBottomSheetAction.positive(
                  text: 'Confirm',
                  onPressed: () => positivePressed = true,
                ),
                KykoBottomSheetAction.negative(
                  text: 'Cancel',
                  onPressed: () => negativePressed = true,
                ),
              ],
            ),
          ),
        ),
      );

      // Tap the positive button and verify callback was triggered
      await tester.tap(find.text('Confirm'));
      expect(positivePressed, true);
      expect(negativePressed, false);

      // Reset flags
      positivePressed = false;

      // Tap the negative button and verify callback was triggered
      await tester.tap(find.text('Cancel'));
      expect(positivePressed, false);
      expect(negativePressed, true);
    });

    testWidgets('calculates correct bottom sheet padding based on view insets and screen padding',
        (WidgetTester tester) async {
      // Mock values for testing
      const double bottomInset = 20.0;
      const double bottomPadding = 15.0;

      // Build the widget with a custom MediaQuery that provides known insets
      await tester.pumpWidget(
        MaterialApp(
          home: MediaQuery(
              data: const MediaQueryData(
                viewInsets: EdgeInsets.only(bottom: bottomInset),
                padding: EdgeInsets.only(bottom: bottomPadding),
              ),
              child: KykoBottomSheet(
                title: 'Test Title',
                content: const Text('Test Content'),
              )),
        ),
      );

      final Finder finder = find.byWidgetPredicate((Widget widget) => widget is Padding);
      final Padding padding = tester.firstWidget(finder) as Padding;
      expect(
          padding.padding,
          isA<EdgeInsets>().having(
            (EdgeInsets edges) => edges.bottom,
            'verify bottom padding',
            bottomInset + bottomPadding,
          ));
    });
  });
}
