import 'package:evoapp/feature/logging/evo_navigator_observer.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('EvoNavigatorObserver', () {
    late EvoNavigatorObserver observer;
    late AppState appState;

    setUp(() {
      appState = AppState();
      getIt.registerSingleton<AppState>(appState);
      observer = EvoNavigatorObserver();
    });

    tearDown(() {
      getIt.reset();
    });

    Route<void> route(String name) =>
        MaterialPageRoute<void>(settings: RouteSettings(name: name), builder: (_) => Container());

    group('didPush()', () {
      test('should set urlPath and previousUrlPath correctly', () {
        observer.didPush(route('/home'), route('/user'));

        expect(appState.eventTrackingSharedData.urlPath, '/home');
        expect(appState.eventTrackingSharedData.previousUrlPath, '/user');
      });
    });

    group('didPop()', () {
      test('should set urlPath and previousUrlPath correctly', () {
        observer.didPop(route('/home'), route('/user'));

        expect(appState.eventTrackingSharedData.urlPath, '/user');
        expect(appState.eventTrackingSharedData.previousUrlPath, '/home');
      });
    });

    group('setUrlPath()', () {
      test('should not set urlPath if currentRoute is PopupRoute with null name', () {
        observer.setUrlPath();

        expect(appState.eventTrackingSharedData.urlPath, isNull);
        expect(appState.eventTrackingSharedData.previousUrlPath, isNull);
      });

      test('should set urlPath and previousUrlPath correctly', () {
        observer.setUrlPath(currentRoute: route('/home'), previousRoute: route('/user'));

        expect(appState.eventTrackingSharedData.urlPath, '/home');
        expect(appState.eventTrackingSharedData.previousUrlPath, '/user');
      });
    });
  });
}
