import 'package:evoapp/feature/authorization_session_expired/authorization_session_expired_popup.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/dialog_functions.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';
import '../biometric/request_user_active_biometric/request_user_active_biometric_popup_test.dart';

void main() {
  late DialogFunction mockDialogFunction;

  setUpAll(() async {
    registerFallbackValue(EvoDialogId.forceLogoutDialog);

    getIt.registerLazySingleton<DialogFunction>(() => MockDialogFunctions());
    mockDialogFunction = getIt.get<DialogFunction>();

    getIt.registerLazySingleton<AppState>(() => AppState());

    getItRegisterMockCommonUtilFunctionAndImageProvider();
  });

  tearDownAll(() async {
    await getIt.reset();
  });

  group('test show pop up condition', () {
    test('test can show popup', () {
      final AuthorizationSessionExpiredPopup sessionExpiredPopup =
          AuthorizationSessionExpiredPopup();
      sessionExpiredPopup.isShowingPopup = false;

      expect(sessionExpiredPopup.checkCanShowPopup(), true);
    });
  });

  test('test can not show popup cause another popup is showing', () {
    final AuthorizationSessionExpiredPopup sessionExpiredPopup = AuthorizationSessionExpiredPopup();

    sessionExpiredPopup.isShowingPopup = true;

    expect(sessionExpiredPopup.checkCanShowPopup(), false);
  });

  group('test show pop up', () {
    late MockContext mockNavigatorContext;

    setUpAll(() {
      mockNavigatorContext = MockContext();
      getItRegisterNavigator(context: mockNavigatorContext);
    });

    tearDownAll(() {
      getItUnregisterNavigator();
    });

    test('show popup with correct parameter', () async {
      /// setup
      when(() => evoUtilFunction.openAuthenticationScreen(
            isClearNavigationStack: any(named: 'isClearNavigationStack'),
          )).thenAnswer((_) async => Future<void>.value());

      when(() => mockDialogFunction.showDialogConfirm(
            alertType: any(named: 'alertType'),
            dialogId: any(named: 'dialogId'),
            textPositive: any(named: 'textPositive'),
            content: any(named: 'content'),
            title: any(named: 'title'),
            textNegative: any(named: 'textNegative'),
            footer: any(named: 'footer'),
            onClickNegative: any(named: 'onClickNegative'),
            onClickPositive: any(named: 'onClickPositive'),
            imageHeader: any(named: 'imageHeader'),
            isDismissible: any(named: 'isDismissible'),
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
            titleTextStyle: any(named: 'titleTextStyle'),
            contentTextStyle: any(named: 'contentTextStyle'),
            isShowButtonClose: any(named: 'isShowButtonClose'),
            loggingEventMetaData: any(named: 'loggingEventMetaData'),
            loggingEventOnShowMetaData: any(named: 'loggingEventOnShowMetaData'),
            autoClosePopupWhenClickCTA: any(named: 'autoClosePopupWhenClickCTA'),
            dialogHorizontalPadding: any(named: 'dialogHorizontalPadding'),
          )).thenAnswer((_) => Future<void>.value());

      /// Arrange
      final AuthorizationSessionExpiredPopup sessionExpiredPopup =
          AuthorizationSessionExpiredPopup();

      ///Action
      await sessionExpiredPopup.show();

      /// Assert evoDialogFunction is invoked with correct parameters
      final List<dynamic> captureData = verify(() => evoDialogFunction.showDialogConfirm(
          alertType: DialogAlertType.error,
          dialogId: EvoDialogId.unAuthorizationSessionBottomSheet,
          title: EvoStrings.titleSessionTokenExpired,
          content: EvoStrings.contentSessionTokenExpiredSignIn,
          textPositive: EvoStrings.textSubmitSessionTokenExpiredSignIn,
          isDismissible: false,
          autoClosePopupWhenClickCTA: true,
          onClickPositive: captureAny(named: 'onClickPositive'))).captured;

      /// Assert
      expect(sessionExpiredPopup.isShowingPopup, false);

      // Verify click positive which work correctly
      final Function onClickPositive = captureData.first as Function;
      onClickPositive();

      /// Assert
      verify(() => evoUtilFunction.openAuthenticationScreen(
            isClearNavigationStack: true,
          )).called(1);
    });
  });
}
