import 'package:evoapp/feature/biometric/biometric_token_module/biometrics_token_module.dart';
import 'package:evoapp/feature/login/utils/login_old_device_utils.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockBiometricsTokenModule extends Mock implements BiometricsTokenModule {}

void main() {
  late LoginOldDeviceUtils loginOldDeviceUtils;
  final BiometricsTokenModule mockBiometricsTokenModule = MockBiometricsTokenModule();

  setUpAll(() {
    getIt.registerLazySingleton<BiometricsTokenModule>(() => mockBiometricsTokenModule);
    getIt.registerLazySingleton<AppState>(() => AppState());

    loginOldDeviceUtils = LoginOldDeviceUtils();
  });

  test('isEnableBiometricAuthenticator return false', () {
    when(() => mockBiometricsTokenModule.isEnableBiometricAuthenticator())
        .thenAnswer((_) async => false);

    expect(loginOldDeviceUtils.checkCanLoginByBiometric(), completion(false));
  });

  test('isEnableBiometricAuthenticator return true, isBiometricTokenUsable return false', () {
    when(() => mockBiometricsTokenModule.isEnableBiometricAuthenticator())
        .thenAnswer((_) async => true);
    when(() => mockBiometricsTokenModule.disableBiometricAuthenticatorFeature())
        .thenAnswer((_) => Future<void>.value());

    when(() => mockBiometricsTokenModule.isBiometricTokenUsable()).thenAnswer((_) async => false);

    expect(loginOldDeviceUtils.checkCanLoginByBiometric(), completion(false));
  });

  test('isEnableBiometricAuthenticator return true, isBiometricTokenUsable return true', () {
    when(() => mockBiometricsTokenModule.isEnableBiometricAuthenticator())
        .thenAnswer((_) async => true);
    when(() => mockBiometricsTokenModule.isBiometricTokenUsable()).thenAnswer((_) async => true);

    expect(loginOldDeviceUtils.checkCanLoginByBiometric(), completion(true));
  });
}
