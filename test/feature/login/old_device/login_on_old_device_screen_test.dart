import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/response/auth_challenge_type.dart';
import 'package:evoapp/data/response/sign_in_entity.dart';
import 'package:evoapp/data/response/user_information_entity.dart';
import 'package:evoapp/feature/login/old_device/biometric/biometric_cubit.dart';
import 'package:evoapp/feature/login/old_device/login_on_old_device_cubit.dart';
import 'package:evoapp/feature/login/old_device/login_on_old_device_screen.dart';
import 'package:evoapp/feature/login/old_device/pincode/pin_code_cubit.dart';
import 'package:evoapp/feature/login/old_device/widgets/login_by_biometric_widget.dart';
import 'package:evoapp/feature/pin/change_pin/create_new_pin_flow.dart';
import 'package:evoapp/feature/pin/reset_pin/reset_pin_handler.dart';
import 'package:evoapp/feature/profile/cubit/user_profile_cubit.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/dialog_functions.dart';
import 'package:evoapp/widget/evo_mpin_code/evo_mpin_code_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/resources/ui_strings.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';
import '../../../util/app_mock_cubit.dart';

class MockLoginOnOldDeviceCubit extends AppMockCubit<LoginOnOldDeviceState>
    implements LoginOnOldDeviceCubit {}

class MockBiometricCubit extends AppMockCubit<BiometricState> implements BiometricCubit {}

class MockPinCodeCubit extends AppMockCubit<PinCodeState> implements PinCodeCubit {}

class MockUserProfileCubit extends AppMockCubit<UserProfileState> implements UserProfileCubit {}

class MockResetPinHandler extends Mock implements ResetPinHandler {}

void main() {
  late MockLoginOnOldDeviceCubit loginCubit;
  late MockBiometricCubit biometricCubit;
  late MockPinCodeCubit pinCodeCubit;
  late MockUserProfileCubit userProfileCubit;

  late ResetPinHandler resetPinHandler;

  setUpAll(() {
    initConfigEvoPageStateBase();

    resetPinHandler = getIt.registerSingleton(MockResetPinHandler());

    when(() => mockEvoUtilFunction.showHudLoading()).thenAnswer((_) async {});
    when(() => mockEvoUtilFunction.hideHudLoading()).thenAnswer((_) async {});
  });

  tearDownAll(() {
    getIt.reset();
  });

  setUp(() async {
    setUpMockConfigEvoPageStateBase();

    loginCubit = MockLoginOnOldDeviceCubit()..emit(LoginOnOldDeviceInitial());
    biometricCubit = MockBiometricCubit()..emit(BiometricInitial());
    pinCodeCubit = MockPinCodeCubit()..emit(PinCodeInitial());
    userProfileCubit = MockUserProfileCubit()..emit(UserProfileInitial());

    when(() => biometricCubit.initialize()).thenAnswer((_) async {});
    when(() => biometricCubit.authenticate(
            shouldEmitUnavailableState: any(named: 'shouldEmitUnavailableState')))
        .thenAnswer((_) async {});

    when(() => userProfileCubit.getLocalUserInfo()).thenAnswer((_) async {});

    when(() => resetPinHandler.requestResetPin(
          phoneNumber: any(named: 'phoneNumber'),
          entryScreenName: any(named: 'entryScreenName'),
          onError: any(named: 'onError'),
        )).thenAnswer((_) async {});
  });

  group('LoginOnOldDeviceScreen', () {
    Widget buildWidgetInTest({CreateNewPinFlow flow = CreateNewPinFlow.createPin}) {
      return MaterialApp(
        home: MultiBlocProvider(
          providers: <BlocProvider<dynamic>>[
            BlocProvider<LoginOnOldDeviceCubit>.value(value: loginCubit),
            BlocProvider<BiometricCubit>.value(value: biometricCubit),
            BlocProvider<PinCodeCubit>.value(value: pinCodeCubit),
            BlocProvider<UserProfileCubit>.value(value: userProfileCubit),
          ],
          child: LoginOnOldDeviceScreen(),
        ),
      );
    }

    test('should self navigate', () {
      LoginOnOldDeviceScreen.pushNamed();
      verify(() => mockNavigatorContext.pushNamed(Screen.loginOnOldDeviceScreen.name,
          extra: any(named: 'extra'))).called(1);

      LoginOnOldDeviceScreen.goNamed();
      verify(() => mockNavigatorContext.goNamed(Screen.loginOnOldDeviceScreen.name,
          extra: any(named: 'extra'))).called(1);
    });

    testWidgets('should show username if available', (WidgetTester tester) async {
      final String userName = 'User Name';

      await tester.pumpWidget(buildWidgetInTest());

      final UserProfileState state =
          UserProfileLoadedSuccess(user: UserInformationEntity(fullName: userName));
      userProfileCubit.emit(state);
      await tester.pump();

      final String title = '${EvoStrings.loginOnOldDeviceTitle}, $userName!';
      expect(find.text(title), findsOneWidget);
    });

    testWidgets('should NOT show username if unavailable', (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());

      final String title = '${EvoStrings.loginOnOldDeviceTitle}!';
      expect(find.text(title), findsOneWidget);
    });

    testWidgets('MPIN code widget should show correct UI', (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());
      expect(find.text(CommonStrings.otherGenericErrorMessage), findsNothing);

      pinCodeCubit.emit(PinCodeErrorState(null));
      await tester.pump();
      expect(find.text(CommonStrings.otherGenericErrorMessage), findsOneWidget);

      final String error = 'error';
      pinCodeCubit.emit(PinCodeErrorState(error));
      await tester.pump();
      expect(find.text(error), findsOneWidget);
    });

    testWidgets('MPIN code widget should call correct cubit methods', (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());

      final EvoMPINCodeWidget pinCodeWidget = tester.widget(find.byType(EvoMPINCodeWidget));

      when(() => loginCubit.loginByPinCode(any())).thenAnswer((_) async {});
      final String pin = '1234';
      pinCodeWidget.onSubmit?.call(pin);
      verify(() => loginCubit.loginByPinCode(pin)).called(1);

      pinCodeWidget.onChange?.call('');
      verify(() => pinCodeCubit.clearPinCodeError()).called(1);

      pinCodeWidget.onResetPin?.call();
      verify(() => resetPinHandler.requestResetPin(
            phoneNumber: any(named: 'phoneNumber'),
            entryScreenName: any(named: 'entryScreenName'),
            onError: captureAny(named: 'onError'),
          )).called(1);
    });

    testWidgets('should show login CTA', (WidgetTester tester) async {
      when(() => loginCubit.loginByPinCode(any())).thenAnswer((_) async {});

      await tester.pumpWidget(buildWidgetInTest());

      await tester.tap(find.text(EvoStrings.login));

      verify(() => loginCubit.loginByPinCode(any())).called(1);
    });

    testWidgets('should hide biometric CTA if disabled', (WidgetTester tester) async {
      biometricCubit.emit(BiometricInitial());
      await tester.pumpWidget(buildWidgetInTest());

      expect(find.byType(LoginByBiometricWidget), findsNothing);
    });

    testWidgets('should show biometric CTA if state is BiometricAuthenticationAvailable',
        (WidgetTester tester) async {
      biometricCubit.emit(BiometricAuthenticationAvailable());
      await tester.pumpWidget(buildWidgetInTest());

      final Finder bioButton = find.byType(LoginByBiometricWidget);
      expect(bioButton, findsOneWidget);

      when(() => biometricCubit.authenticate()).thenAnswer((_) async {});
      await tester.tap(bioButton);
      verify(() => biometricCubit.authenticate()).called(1);
    });

    testWidgets('should show biometric CTA if state is BiometricAuthUserDismiss',
        (WidgetTester tester) async {
      biometricCubit.emit(BiometricAuthUserDismiss());
      await tester.pumpWidget(buildWidgetInTest());

      final Finder bioButton = find.byType(LoginByBiometricWidget);
      expect(bioButton, findsOneWidget);

      when(() => biometricCubit.authenticate()).thenAnswer((_) async {});
      await tester.tap(bioButton);
      verify(() => biometricCubit.authenticate()).called(1);
    });

    testWidgets('should show biometric CTA if state is BiometricAuthSuccess',
        (WidgetTester tester) async {
      biometricCubit.emit(BiometricAuthSuccess());
      await tester.pumpWidget(buildWidgetInTest());

      final Finder bioButton = find.byType(LoginByBiometricWidget);
      expect(bioButton, findsOneWidget);

      when(() => biometricCubit.authenticate()).thenAnswer((_) async {});
      await tester.tap(bioButton);
      verify(() => biometricCubit.authenticate()).called(1);
    });

    testWidgets('should show switch account button', (WidgetTester tester) async {
      when(() => loginCubit.loginWithNewAccount()).thenAnswer((_) async {});

      await tester.pumpWidget(buildWidgetInTest());

      final Finder switchButton = find.text(EvoStrings.loginOnOldDeviceSwitchAccountCTA);
      expect(switchButton, findsOneWidget);

      await tester.tap(switchButton);
      final Function switchAccount = verify(() => mockDialogFunction.showDialogConfirm(
            title: EvoStrings.switchAccountTitle,
            textPositive: EvoStrings.ctaProceed,
            textNegative: EvoStrings.ctaCancel,
            dialogId: EvoDialogId.confirmSwitchAccountDialog,
            onClickPositive: captureAny(named: 'onClickPositive'),
          )).captured.first as Function;

      switchAccount();
      await tester.pump();

      verify(() => mockNavigatorContext.pop()).called(1);
      verify(() => loginCubit.loginWithNewAccount()).called(1);
      verify(() => mockNavigatorContext.pushReplacementNamed(Screen.verifyUsernameScreen.name,
          extra: any(named: 'extra'))).called(1);
    });

    testWidgets('should show hud loading when state is LoginOnOldDeviceLoading',
        (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());

      loginCubit.emit(LoginOnOldDeviceLoading());
      await tester.pump();

      verify(() => mockEvoUtilFunction.showHudLoading()).called(1);
    });

    testWidgets('should navigate to Main screen when state is LoginOnOldDeviceSuccess',
        (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());

      loginCubit.emit(
          LoginOnOldDeviceSuccess(type: TypeLogin.otp, challengeType: AuthChallengeType.faceAuth));
      await tester.pump();

      verify(() => mockNavigatorContext.goNamed(Screen.mainScreen.name, extra: any(named: 'extra')))
          .called(1);
    });

    testWidgets('should show exceeded error popup when state is LoginOnOldDeviceError',
        (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());

      loginCubit.emit(LoginOnOldDeviceError(
          ErrorUIModel(verdict: SignInEntity.verdictLimitExceeded), TypeLogin.otp, null));
      await tester.pump();

      final Function onClickPositive = verify(() => mockDialogFunction.showDialogConfirm(
            alertType: DialogAlertType.error,
            title: EvoStrings.loginLimitedExceededTitle,
            content: any(named: 'content'),
            textPositive: EvoStrings.backToHomePage,
            onClickPositive: captureAny(named: 'onClickPositive'),
            dialogId: EvoDialogId.loginLimitedExceededDialog,
            isDismissible: false,
          )).captured.first as Function;

      onClickPositive();
      await tester.pump();

      verify(() => mockNavigatorContext.goNamed(Screen.welcomeScreen.name)).called(1);
    });
  });
}
