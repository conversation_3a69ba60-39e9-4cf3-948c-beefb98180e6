import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/base/evo_page_state_base.dart';
import 'package:evoapp/feature/biometric/activate_biometric/active_biometric_page.dart';
import 'package:evoapp/feature/login/old_device/re_auth_input_pin/re_auth_input_pin_cubit.dart';
import 'package:evoapp/feature/login/old_device/re_auth_input_pin/re_auth_input_pin_screen.dart';
import 'package:evoapp/feature/main_screen/main_screen.dart';
import 'package:evoapp/feature/pin/reset_pin/reset_pin_handler.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/dialog_functions.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/widget/appbar/evo_support_appbar.dart';
import 'package:evoapp/widget/evo_mpin_code/evo_mpin_code_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../base/evo_page_state_base_test_config.dart';

class MockReAuthInputPinCubit extends MockCubit<ReAuthPinState> implements ReAuthInputPinCubit {}

class MockResetPinHandler extends Mock implements ResetPinHandler {}

class MockUpdateUserLoginStatus extends Mock {
  void call(bool isLogin);
}

class MockErrorCallback extends Mock {
  void call(ErrorUIModel? errorUIModel);
}

class MockDialogFunction extends Mock implements DialogFunction {}

class TestReAuthInputPinScreen extends ReAuthInputPinScreen {
  const TestReAuthInputPinScreen({required super.phoneNumber, super.sessionToken, super.key});

  @override
  EvoPageStateBase<ReAuthInputPinScreen> createState() => MockReAuthInputPinScreenState();
}

class MockReAuthInputPinScreenState extends ReAuthInputPinScreenState {
  bool? isLogin = false;

  @override
  void updateUserLoginStatus(bool isLogin) {
    getIt.get<MockUpdateUserLoginStatus>().call(isLogin);
  }

  @override
  Future<void> handleEvoApiError(ErrorUIModel? errorUIModel) async {
    getIt.get<MockErrorCallback>().call(errorUIModel);
  }
}

void main() {
  late CommonNavigator commonNavigator;
  late ReAuthInputPinCubit mockCubit;
  late EvoUtilFunction mockUtilFunction;
  late MockUpdateUserLoginStatus mockUpdateUserLoginStatusCb;
  late DialogFunction mockDialogFunction;
  late MockErrorCallback mockErrorCallback;
  late CommonImageProvider mockImageProvider;

  setUpAll(() {
    initConfigEvoPageStateBase();
    setUpMockConfigEvoPageStateBase();

    commonNavigator = getIt.get<CommonNavigator>();

    getIt.registerSingleton<ResetPinHandler>(MockResetPinHandler());

    mockUtilFunction = getIt.get<EvoUtilFunction>();

    getIt.registerSingleton<MockUpdateUserLoginStatus>(MockUpdateUserLoginStatus());
    mockUpdateUserLoginStatusCb = getIt.get<MockUpdateUserLoginStatus>();

    mockDialogFunction = getIt.get<DialogFunction>();

    registerFallbackValue(EvoDialogId.common);

    getIt.registerSingleton<MockErrorCallback>(MockErrorCallback());
    mockErrorCallback = getIt.get<MockErrorCallback>();

    mockImageProvider = getIt.get<CommonImageProvider>();
  });

  setUp(() {
    mockCubit = MockReAuthInputPinCubit();
    when(() => mockCubit.state).thenReturn(PinNotFullState());

    when(() => mockUtilFunction.hideHudLoading()).thenAnswer((_) async {});
    when(() => mockUtilFunction.showHudLoading()).thenAnswer((_) async {});

    when(() => mockImageProvider.asset(any(),
        width: any(named: 'width'),
        height: any(named: 'height'),
        fit: any(named: 'fit'),
        color: any(named: 'color'))).thenAnswer((_) {
      return const SizedBox.shrink();
    });
  });

  group('ReAuthInputPinArg', () {
    const String testPhoneNumber = '**********';
    const String testSessionToken = 'test-session-token';

    test('should create an instance with required phoneNumber and optional sessionToken', () {
      final ReAuthInputPinArg arg = ReAuthInputPinArg(
        phoneNumber: testPhoneNumber,
        sessionToken: testSessionToken,
      );

      expect(arg, isA<PageBaseArg>());
      expect(arg.phoneNumber, equals(testPhoneNumber));
      expect(arg.sessionToken, equals(testSessionToken));
    });
  });

  group('ReAuthInputPinScreen', () {
    final String mockPhoneNumber = '**********';
    final String mockSessionToken = 'mock-session-token';

    test('should pushNamed navigate correctly', () async {
      await ReAuthInputPinScreen.pushNamed(
        phoneNumber: mockPhoneNumber,
        sessionToken: mockSessionToken,
      );

      verify(() => commonNavigator.pushNamed(
            any(),
            Screen.reAuthInputPinScreen.name,
            extra: any(
              named: 'extra',
              that: predicate<ReAuthInputPinArg>(
                (ReAuthInputPinArg arg) =>
                    arg.phoneNumber == mockPhoneNumber && arg.sessionToken == mockSessionToken,
              ),
            ),
          )).called(1);
    });

    group('render UI', () {
      buildWidget(WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: BlocProvider<ReAuthInputPinCubit>.value(
              value: mockCubit,
              child: TestReAuthInputPinScreen(
                phoneNumber: mockPhoneNumber,
                sessionToken: mockSessionToken,
              ),
            ),
          ),
        );
      }

      testWidgets('test widget cannot be pop', (WidgetTester tester) async {
        await buildWidget(tester);

        bool didPop = true;
        tester.binding.addPostFrameCallback((_) {
          didPop = Navigator.of(tester.element(find.byType(PopScope))).canPop();
        });

        await tester.pump();
        expect(didPop, isFalse);
      });

      testWidgets('should render initial UI correctly', (WidgetTester tester) async {
        await buildWidget(tester);

        expect(find.byType(EvoSupportAppbar), findsOneWidget);
        expect(
            find.text(
              EvoStrings.inputPinTitle,
            ),
            findsOneWidget);
        expect(
            find.byWidgetPredicate(
              (Widget widget) =>
                  widget is EvoMPINCodeWidget && widget.title == EvoStrings.inputPinDesc,
            ),
            findsOneWidget);
      });

      group('handle state', () {
        testWidgets('should handle state correctly when state is PinLoadingState',
            (WidgetTester tester) async {
          whenListen(
              mockCubit,
              Stream.fromIterable([
                PinNotFullState(),
                PinLoadingState(),
              ]));

          await buildWidget(tester);

          verify(() => mockUtilFunction.showHudLoading()).called(1);
        });

        testWidgets('should handle correctly when state is VerifyPinSuccessState',
            (WidgetTester tester) async {
          final String mockPinCode = '123456';
          whenListen(
              mockCubit,
              Stream.fromIterable(<ReAuthPinState>[
                PinNotFullState(),
                VerifyPinSuccessState(mockPinCode),
              ]));

          await buildWidget(tester);

          mockUpdateUserLoginStatusCb.call(true);
          final ActiveBiometricScreenArg captured = verify(() => commonNavigator.pushNamed(
                any(),
                Screen.activeBiometric.name,
                extra: captureAny(named: 'extra'),
              )).captured.first as ActiveBiometricScreenArg;

          captured.onSuccess();

          verify(() => commonNavigator.goNamed(
                any(),
                Screen.mainScreen.name,
                extra: any(
                  named: 'extra',
                  that: predicate<MainScreenArg>(
                    (MainScreenArg arg) => arg.isLoggedIn,
                  ),
                ),
              )).called(1);
        });

        testWidgets('should handle correctly when state is PinSessionExpired',
            (WidgetTester tester) async {
          whenListen(
              mockCubit,
              Stream.fromIterable(<ReAuthPinState>[
                PinNotFullState(),
                PinSessionExpired(),
              ]));

          when(() => mockDialogFunction.showDialogSessionTokenExpired(
                onClickPositive: any(named: 'onClickPositive'),
                type: any(named: 'type'),
              )).thenAnswer((_) async {});

          await buildWidget(tester);

          verify(() => mockDialogFunction.showDialogSessionTokenExpired(
              onClickPositive: any(named: 'onClickPositive'), type: any(named: 'type'))).called(1);
        });

        testWidgets('should handle correctly when state is PinLimitExceeded',
            (WidgetTester tester) async {
          final String mockError = 'error-message';
          whenListen(
              mockCubit,
              Stream.fromIterable(<ReAuthPinState>[
                PinNotFullState(),
                PinLimitExceeded(mockError),
              ]));
          when(() => mockDialogFunction.showDialogConfirm(
              isDismissible: any(named: 'isDismissible'),
              title: any(named: 'title'),
              content: any(named: 'content'),
              textPositive: any(named: 'textPositive'),
              dialogId: any(named: 'dialogId'),
              alertType: any(named: 'alertType'),
              onClickPositive: any(named: 'onClickPositive'))).thenAnswer((_) async {});

          await buildWidget(tester);

          verify(() => mockDialogFunction.showDialogConfirm(
              isDismissible: false,
              title: EvoStrings.loginLimitedExceededTitle,
              content: mockError,
              textPositive: EvoStrings.backToHomePage,
              dialogId: any(named: 'dialogId'),
              alertType: DialogAlertType.error,
              onClickPositive: any(named: 'onClickPositive'))).called(1);
        });

        testWidgets('should handle correctly when state is PinLimitExceeded',
            (WidgetTester tester) async {
          final ErrorUIModel mockError = ErrorUIModel();
          whenListen(
              mockCubit,
              Stream.fromIterable(<ReAuthPinState>[
                PinNotFullState(),
                PinErrorState(errorUIModel: mockError),
              ]));
          when(() => mockErrorCallback.call(any())).thenAnswer((_) async {});

          await buildWidget(tester);

          verify(() => mockErrorCallback.call(mockError)).called(1);
        });

        testWidgets('should handle correctly when state is VerifyPinBadRequest',
            (WidgetTester tester) async {
          final String mockError = 'error-message';
          whenListen(
              mockCubit,
              Stream.fromIterable(<ReAuthPinState>[
                PinNotFullState(),
                VerifyPinBadRequest(mockError),
              ]));
          ;

          await buildWidget(tester);
          await tester.pump();

          expect(
              find.byWidgetPredicate(
                (Widget widget) => widget is EvoMPINCodeWidget && widget.errorMessage == mockError,
              ),
              findsOneWidget);
        });
      });
    });
  });
}
