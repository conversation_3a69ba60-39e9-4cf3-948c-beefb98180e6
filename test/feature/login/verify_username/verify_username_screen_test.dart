import 'package:evoapp/data/response/auth_challenge_type.dart';
import 'package:evoapp/feature/login/verify_username/verify_username_cubit.dart';
import 'package:evoapp/feature/login/verify_username/verify_username_screen.dart';
import 'package:evoapp/feature/login/verify_username/verify_username_state.dart';
import 'package:evoapp/feature/verify_otp/cubit/otp_success_model.dart';
import 'package:evoapp/feature/verify_otp/cubit/verify_otp_cubit.dart';
import 'package:evoapp/feature/verify_otp/verify_otp_page.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/appbar/evo_appbar_leading_button.dart';
import 'package:evoapp/widget/appbar/need_help_support_appbar.dart';
import 'package:evoapp/widget/evo_text_field.dart';
import 'package:evoapp/widget/string_cta_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';
import '../../../util/app_mock_cubit.dart';

class MockVerifyUsernameCubit extends AppMockCubit<VerifyUsernameState>
    implements VerifyUsernameCubit {}

void main() {
  late MockVerifyUsernameCubit cubit;

  setUpAll(() {
    initConfigEvoPageStateBase();
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();

    cubit = MockVerifyUsernameCubit()..emit(VerifyUsernameInitial());
    reset(mockEvoUtilFunction);

    when(() => mockEvoUtilFunction.showHudLoading()).thenAnswer((_) async {});
    when(() => mockEvoUtilFunction.hideHudLoading()).thenAnswer((_) async {});
    when(() => mockEvoUtilFunction.hideKeyboard()).thenAnswer((_) async {});
  });

  tearDownAll(() {
    getIt.reset();
  });

  Widget createWidgetUnderTest() {
    return MaterialApp(
      home: VerifyUsernameScreen(cubit: cubit),
    );
  }

  group('VerifyUsernameScreen', () {
    testWidgets('should have needed widgets', (WidgetTester tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      expect(find.byType(NeedHelpSupportAppbar), findsOneWidget);
      expect(find.byType(EvoAppBarLeadingButton), findsOneWidget);

      expect(find.text(EvoStrings.verifyUsernameTitle), findsOneWidget);
      expect(find.byType(EvoTextField), findsOneWidget);
      expect(find.textContaining(EvoStrings.usernameLabel, findRichText: true), findsWidgets);

      expect(find.byType(StringCtaWidget), findsOneWidget);
      expect(find.text(EvoStrings.forgotUsernameTitle), findsOneWidget);
      expect(find.text(EvoStrings.reset), findsOneWidget);

      expect(
        find.descendant(
          of: find.byType(CommonButton),
          matching: find.text(EvoStrings.login),
        ),
        findsOneWidget,
      );
    });

    testWidgets('should display error message when username verification fails',
        (WidgetTester tester) async {
      const String error = 'Invalid username';
      await tester.pumpWidget(createWidgetUnderTest());

      cubit.emit(VerifyUsernameError(error: error));
      await tester.pump();

      expect(find.text(error), findsOneWidget);
      verify(() => mockEvoUtilFunction.hideHudLoading()).called(1);
    });

    testWidgets('should call verify with entered username when login button is tapped',
        (WidgetTester tester) async {
      when(() => cubit.verify(any())).thenAnswer((_) async {});

      await tester.pumpWidget(createWidgetUnderTest());

      const String username = 'validUsername';
      await tester.enterText(find.byType(EvoTextField), username);
      await tester.tap(find.text(EvoStrings.login));

      verify(() => cubit.verify(username)).called(1);
    });

    testWidgets('should navigate to Welcome screen on back button press',
        (WidgetTester tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      await tester.tap(find.byType(EvoAppBarLeadingButton));
      await tester.pump(const Duration(milliseconds: 300));

      verify(() => mockEvoUtilFunction.hideKeyboard()).called(1);
      verify(() => mockNavigatorContext.goNamed(Screen.welcomeScreen.name)).called(1);
    });

    test('should self navigate', () {
      VerifyUsernameScreen.pushNamed();
      verify(() => mockNavigatorContext.pushNamed(Screen.verifyUsernameScreen.name)).called(1);

      VerifyUsernameScreen.pushReplacementNamed();
      verify(() => mockNavigatorContext.pushReplacementNamed(Screen.verifyUsernameScreen.name))
          .called(1);

      VerifyUsernameScreen.goNamed();
      verify(() => mockNavigatorContext.goNamed(Screen.verifyUsernameScreen.name)).called(1);
    });

    testWidgets('should show loading when state is VerifyUsernameLoading',
        (WidgetTester tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      cubit.emit(VerifyUsernameLoading());
      await tester.pump();

      verify(() => mockEvoUtilFunction.showHudLoading()).called(1);
    });

    testWidgets('should navigate to verify OTP screen when state is VerifyUsernameSuccess',
        (WidgetTester tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      cubit.emit(VerifyUsernameSuccess());
      await tester.pump();

      verify(() => mockNavigatorContext.pushNamed(
            Screen.verifyOtpScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });

    testWidgets('should call cubit.verify when the text field is submitted',
        (WidgetTester tester) async {
      when(() => cubit.verify(any())).thenAnswer((_) async {});

      await tester.pumpWidget(createWidgetUnderTest());

      final EvoTextField textField = tester.widget(find.byType(EvoTextField));
      textField.onSubmitted?.call('text');

      verify(() => cubit.verify(any())).called(1);
    });

    testWidgets('should navigate to ReAuthInputPinScreen after OTP success',
        (WidgetTester tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      cubit.emit(VerifyUsernameSuccess());
      await tester.pump();

      final VerifyOtpPageArg arg = verify(() => mockNavigatorContext.pushNamed(
            Screen.verifyOtpScreen.name,
            extra: captureAny(named: 'extra'),
          )).captured.first as VerifyOtpPageArg;

      arg.onPopSuccess?.call(
          VerifyOtpSuccess(OtpSuccessModel(challengeType: AuthChallengeType.verifyPin.value)));

      verify(() => mockNavigatorContext.pushNamed(
            Screen.reAuthInputPinScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });
  });
}
