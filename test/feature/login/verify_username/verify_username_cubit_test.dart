import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/login/verify_username/verify_username_cubit.dart';
import 'package:evoapp/feature/login/verify_username/verify_username_state.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../constant.dart';

void main() {
  group('VerifyUsernameCubit', () {
    test('initial state should be VerifyUsernameInitial', () {
      expect(VerifyUsernameCubit().state, isA<VerifyUsernameInitial>());
    });

    blocTest<VerifyUsernameCubit, VerifyUsernameState>(
      'should emit [VerifyUsernameError] when verify is called with invalid username',
      build: () => VerifyUsernameCubit(),
      act: (VerifyUsernameCubit cubit) => cubit.verify(''),
      expect: () => <dynamic>[
        isA<VerifyUsernameError>(),
      ],
    );

    blocTest<VerifyUsernameCubit, VerifyUsernameState>(
      'should emit [VerifyUsernameLoading, VerifyUsernameSuccess] when verify is called with valid username',
      build: () => VerifyUsernameCubit(),
      act: (VerifyUsernameCubit cubit) => cubit.verify('validUsername'),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<VerifyUsernameLoading>(),
        isA<VerifyUsernameSuccess>(),
      ],
    );

    blocTest<VerifyUsernameCubit, VerifyUsernameState>(
      'should emit [VerifyUsernameLoading, VerifyUsernameError] when verify is called with invalid username',
      build: () => VerifyUsernameCubit(),
      act: (VerifyUsernameCubit cubit) => cubit.verify('error'),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<VerifyUsernameLoading>(),
        isA<VerifyUsernameError>(),
      ],
    );

    blocTest<VerifyUsernameCubit, VerifyUsernameState>(
      'should emit [VerifyUsernameInitial] when calling resetError after error state',
      build: () => VerifyUsernameCubit(),
      seed: () => VerifyUsernameError(error: 'error'),
      act: (VerifyUsernameCubit cubit) => cubit.resetError(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<VerifyUsernameInitial>(),
      ],
    );

    blocTest<VerifyUsernameCubit, VerifyUsernameState>(
      'should NOT emit new state when calling resetError with non-error state',
      build: () => VerifyUsernameCubit(),
      seed: () => VerifyUsernameSuccess(),
      act: (VerifyUsernameCubit cubit) => cubit.resetError(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[],
    );
  });
}
