import 'package:evoapp/feature/login/verify_username/verify_username_state.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('VerifyUsernameState', () {
    test('State classes should be instantiable', () {
      final VerifyUsernameState initialState = VerifyUsernameInitial();
      final VerifyUsernameState loadingState = VerifyUsernameLoading();
      final VerifyUsernameState successState = VerifyUsernameSuccess();

      expect(initialState, isA<VerifyUsernameState>());
      expect(loadingState, isA<VerifyUsernameState>());
      expect(successState, isA<VerifyUsernameState>());
    });

    group('VerifyUsernameError', () {
      test('should be instantiable with error message', () {
        const String errorMessage = 'An error occurred';
        final VerifyUsernameError state = VerifyUsernameError(error: errorMessage);

        expect(state, isA<VerifyUsernameState>());
        expect(state.error, errorMessage);
      });

      test('should hold the provided error message', () {
        const String errorMessage = 'Invalid username';
        final VerifyUsernameError state = VerifyUsernameError(error: errorMessage);

        expect(state.error, equals(errorMessage));
      });
    });
  });
}
