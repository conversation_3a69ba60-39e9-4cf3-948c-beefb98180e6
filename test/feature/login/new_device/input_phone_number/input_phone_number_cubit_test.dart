import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/response/sign_in_entity.dart';
import 'package:evoapp/feature/login/new_device/input_phone_number/input_phone_number_cubit.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../constant.dart';

class AuthenticationRepoMock extends Mock implements AuthenticationRepo {}

void main() {
  late AuthenticationRepoMock authenticationRepoMock;

  setUpAll(() {
    authenticationRepoMock = AuthenticationRepoMock();
  });

  InputPhoneNumberCubit getCubit() {
    return InputPhoneNumberCubit(authenticationRepoMock);
  }

  group('test signIn function', () {
    const String phoneNumber = '035508290';

    blocTest<InputPhoneNumberCubit, InputPhoneNumberState>(
      'test SignIn failed',
      setUp: () {
        when(() => authenticationRepoMock.signIn(
              TypeLogin.otp,
              phoneNumber: phoneNumber,
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => SignInEntity.fromBaseResponse(
              BaseResponse(
                statusCode: CommonHttpClient.UNKNOWN_ERRORS,
                response: <String, dynamic>{'statusCode': CommonHttpClient.UNKNOWN_ERRORS},
              ),
            ));
      },
      build: () => getCubit(),
      act: (InputPhoneNumberCubit cubit) => cubit.signIn(phoneNumber),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<InputPhoneNumberLoading>(),
        isA<InputPhoneNumberFailed>().having(
          (InputPhoneNumberFailed state) => state.errorUIModel.statusCode,
          'verify status code',
          CommonHttpClient.UNKNOWN_ERRORS,
        ),
      ],
    );

    blocTest<InputPhoneNumberCubit, InputPhoneNumberState>(
      'test SignIn success',
      setUp: () {
        when(() => authenticationRepoMock.signIn(
              TypeLogin.otp,
              phoneNumber: phoneNumber,
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => SignInEntity.fromBaseResponse(
              BaseResponse(
                statusCode: CommonHttpClient.SUCCESS,
                response: <String, dynamic>{'verdict': BaseEntity.verdictSuccess},
              ),
            ));
      },
      build: () => getCubit(),
      act: (InputPhoneNumberCubit cubit) => cubit.signIn(phoneNumber),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<InputPhoneNumberLoading>(),
        isA<InputPhoneNumberPhoneSuccess>().having(
          (InputPhoneNumberPhoneSuccess state) => state.entity?.verdict,
          'verify verdict',
          BaseEntity.verdictSuccess,
        ),
      ],
    );
  });

  group('test changePhoneNumber function ', () {
    blocTest<InputPhoneNumberCubit, InputPhoneNumberState>(
      'emits [ChangePhoneNumber] when changePhoneNumber is called',
      build: () => getCubit(),
      act: (InputPhoneNumberCubit cubit) => cubit.changePhoneNumber(),
      expect: () => <dynamic>[
        isA<ChangePhoneNumber>(),
      ],
    );
  });
}
