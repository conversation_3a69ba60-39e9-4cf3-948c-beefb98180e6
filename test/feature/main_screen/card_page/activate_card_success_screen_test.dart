import 'package:evoapp/feature/main_screen/card_page/activate_card_success_screen.dart';
import 'package:evoapp/feature/main_screen/card_page/widgets/card_widget.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';

class _FakeImage extends SizedBox {}

void main() {
  setUpAll(() {
    initConfigEvoPageStateBase();

    when(
      () => evoImageProvider.asset(
        any(),
        width: any(named: 'width'),
        fit: any(named: 'fit'),
      ),
    ).thenReturn(_FakeImage());
  });

  tearDownAll(() {
    getIt.reset();
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();
  });

  group('ActivateCardSuccessScreen', () {
    testWidgets('should render ActivateCardSuccessScreen with correct UI',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: ActivateCardSuccessScreen(cardType: CardType.virtual),
        ),
      );
      expect(find.byType(ActivateCardSuccessScreen), findsOneWidget);
      final ColoredBox coloredBox = tester.widget(find.byType(ColoredBox));
      expect(coloredBox.color, evoColors.primary);

      expect(find.byType(_FakeImage), findsOneWidget);
      expect(find.descendant(of: find.byType(Column), matching: find.byType(Text)), findsOneWidget);

      final Finder goToCardsButton = find.byType(CommonButton);
      expect(goToCardsButton, findsOneWidget);
      expect(find.descendant(of: goToCardsButton, matching: find.text(EvoStrings.goToCardsButton)),
          findsOneWidget);
    });

    testWidgets('should render correct image and text when card type is virtual',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: ActivateCardSuccessScreen(cardType: CardType.virtual),
        ),
      );

      final List<dynamic> captured = verify(() => evoImageProvider.asset(
            captureAny(),
            width: any(named: 'width'),
            fit: any(named: 'fit'),
          )).captured;
      expect(captured[0], EvoImages.imgActivateVirtualCardSuccess);
      expect(
        find.descendant(
          of: find.byType(Column),
          matching: find.text(EvoStrings.activateVirtualCardSuccess),
        ),
        findsOneWidget,
      );
    });

    testWidgets('should render correct image and text when card type is physical',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: ActivateCardSuccessScreen(cardType: CardType.physical),
        ),
      );

      final List<dynamic> captured = verify(() => evoImageProvider.asset(
            captureAny(),
            width: any(named: 'width'),
            fit: any(named: 'fit'),
          )).captured;
      expect(captured[0], EvoImages.imgActivatePhysicalCardSuccess);
      expect(
        find.descendant(
          of: find.byType(Column),
          matching: find.text(EvoStrings.activatePhysicalCardSuccess),
        ),
        findsOneWidget,
      );
    });

    testWidgets('should navigate to Main screen when goToCards button is tapped',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: ActivateCardSuccessScreen(cardType: CardType.virtual),
        ),
      );

      await tester.tap(find.byType(CommonButton));

      final List<dynamic> captured =
          verify(() => mockNavigatorContext.popUntilNamed(captureAny())).captured;
      expect(captured[0], Screen.mainScreen.name);
    });
  });
}
