import 'package:evoapp/feature/verify_otp/mock/mock_verify_otp_use_case.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('verify getMockVerifyOtpFileNameByCase should return corrected file names', () {
    expect(getMockVerifyOtpFileNameByCase(MockVerifyOtpUseCase.getSignInOtpSuccess),
        'sign_in_otp_success.json');
    expect(getMockVerifyOtpFileNameByCase(MockVerifyOtpUseCase.getVerifySignInOtpSuccess),
        'verify_sign_in_otp_success.json');
    expect(getMockVerifyOtpFileNameByCase(MockVerifyOtpUseCase.getVerifySignInOtpIncorrectOtp),
        'verify_sign_in_otp_incorrect_otp.json');
    expect(
        getMockVerifyOtpFileNameByCase(MockVerifyOtpUseCase.getVerifySignInResendOtpLimitExceeded),
        'verify_sign_in_resend_otp_limit_exceeded.json');

    expect(getMockVerifyOtpFileNameByCase(MockVerifyOtpUseCase.getVerifyActiveAccountSuccess),
        'get_verify_active_account_success.json');
  });
}
