import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/feature/verify_otp/handler/activate_account_verify_otp_handler.dart';
import 'package:evoapp/feature/verify_otp/handler/active_card_verify_otp_handler.dart';
import 'package:evoapp/feature/verify_otp/handler/reset_pin_verify_otp_handler.dart';
import 'package:evoapp/feature/verify_otp/handler/sign_in_verify_otp_handler.dart';
import 'package:evoapp/feature/verify_otp/handler/verify_email_otp_handler.dart';
import 'package:evoapp/feature/verify_otp/handler/verify_otp_handler.dart';
import 'package:evoapp/feature/verify_otp/handler/verify_otp_handler_factory.dart';
import 'package:evoapp/feature/verify_otp/verify_otp_page.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

void main() {
  group('VerifyOtpHandlerFactory', () {
    final MockAuthenticationRepo authRepo = MockAuthenticationRepo();

    test('should return SignInVerifyOtpHandler when type is signIn', () {
      final VerifyOtpHandler handler =
          VerifyOtpHandlerFactory.createHandler(VerifyOtpType.signIn, authRepo);
      expect(handler, isA<SignInVerifyOtpHandler>());
    });

    test('should return ResetPinVerifyOtpHandler when type is resetPin', () {
      final VerifyOtpHandler handler =
          VerifyOtpHandlerFactory.createHandler(VerifyOtpType.resetPin, authRepo);
      expect(handler, isA<ResetPinVerifyOtpHandler>());
    });

    test('should return ActiveCardVerifyOtpHandler when type is activateCard', () {
      final VerifyOtpHandler handler =
          VerifyOtpHandlerFactory.createHandler(VerifyOtpType.activateCard, authRepo);
      expect(handler, isA<ActiveCardVerifyOtpHandler>());
    });

    test('should return ActivateAccountVerifyOtpHandler when type is activateAccount', () {
      final VerifyOtpHandler handler =
          VerifyOtpHandlerFactory.createHandler(VerifyOtpType.activateAccount, authRepo);
      expect(handler, isA<ActivateAccountVerifyOtpHandler>());
    });

    test('should return VerifyEmailOtpHandler when type is email', () {
      final VerifyOtpHandler handler =
      VerifyOtpHandlerFactory.createHandler(VerifyOtpType.email, authRepo);
      expect(handler, isA<VerifyEmailOtpHandler>());
    });
  });
}
