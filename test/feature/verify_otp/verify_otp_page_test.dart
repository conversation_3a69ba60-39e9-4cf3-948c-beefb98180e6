import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/feature/verify_otp/cubit/otp_success_model.dart';
import 'package:evoapp/feature/verify_otp/cubit/verify_otp_cubit.dart';
import 'package:evoapp/feature/verify_otp/verify_otp_page.dart';
import 'package:evoapp/feature/verify_otp/widget/evo_otp_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/dialog_functions.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../base/evo_page_state_base_test_config.dart';

class MockVerifyOtpCubit extends MockCubit<VerifyOtpState> implements VerifyOtpCubit {}

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

void main() {
  late MockVerifyOtpCubit mockCubit;
  late DialogFunction mockDialogFunction;
  late EvoUtilFunction mockUtilFunction;
  final String mockPhoneNumber = '+1234567890';
  final int mockOtpResendSecs = 30;
  final int mockOtpValiditySecs = 120;
  final String mockSessionToken = 'test-token';

  setUpAll(() {
    initConfigEvoPageStateBase();
    setUpMockConfigEvoPageStateBase();

    // Initialize any additional GetIt registrations if needed
    if (!getIt.isRegistered<AuthenticationRepo>()) {
      getIt.registerSingleton<AuthenticationRepo>(MockAuthenticationRepo());
    }

    // Get mocks from GetIt that were set up by the helper functions
    mockDialogFunction = getIt.get<DialogFunction>();
    mockUtilFunction = getIt.get<EvoUtilFunction>();
  });

  setUp(() {
    mockCubit = MockVerifyOtpCubit();

    when(() => mockCubit.state).thenReturn(VerifyOtpInitial());
    when(() => mockCubit.verifyOtp(any(), any())).thenAnswer((_) async {});
    when(() => mockCubit.resendOtp(any(), any())).thenAnswer((_) async {});
    when(() => mockUtilFunction.showHudLoading()).thenAnswer((_) async {});
    when(() => mockUtilFunction.hideHudLoading()).thenAnswer((_) async {});
  });

  Widget buildTestWidget({
    VerifyOtpType verifyOtpType = VerifyOtpType.signIn,
    void Function(VerifyOtpState)? onPopSuccess,
  }) {
    return MaterialApp(
      home: BlocProvider<VerifyOtpCubit>.value(
        value: mockCubit,
        child: VerifyOtpPage(
          verifyOtpType: verifyOtpType,
          contactInfo: mockPhoneNumber,
          otpResendSecs: mockOtpResendSecs,
          otpValiditySecs: mockOtpValiditySecs,
          sessionToken: mockSessionToken,
          onPopSuccess: onPopSuccess,
        ),
      ),
    );
  }

  testWidgets('VerifyOtpPage UI Tests displays page title correctly', (tester) async {
    when(() => mockCubit.state).thenReturn(VerifyOtpInitial());

    await tester.pumpWidget(buildTestWidget());
    await tester.pump();

    expect(find.text(EvoStrings.verifyOtpScreenTitle), findsOneWidget);
    expect(
        find.textContaining(mockPhoneNumber.substring(mockPhoneNumber.length - 4)), findsOneWidget);
    expect(find.byType(EvoOtpWidget), findsOneWidget);
  });

  testWidgets('should display page title correctly with VerifyOtpType.email',
      (WidgetTester tester) async {
    when(() => mockCubit.state).thenReturn(VerifyOtpInitial());

    await tester.pumpWidget(buildTestWidget(verifyOtpType: VerifyOtpType.email));

    expect(find.text(EvoStrings.verifyOtpScreenTitle), findsOneWidget);
    expect(find.text(EvoStrings.verifyEmailOtpScreenDesc), findsOneWidget);
  });

  group('VerifyOtpPage State Handling Tests', () {
    testWidgets('shows loading when VerifyOtpLoading state is emitted',
        (WidgetTester tester) async {
      // Emit loading state
      whenListen(
        mockCubit,
        Stream.fromIterable(<VerifyOtpLoading>[VerifyOtpLoading()]),
        initialState: VerifyOtpInitial(),
      );

      await tester.pumpWidget(buildTestWidget());
      await tester.pump();

      verify(() => mockUtilFunction.showHudLoading()).called(1);
    });

    testWidgets('calls onPopSuccess when VerifyOtpSuccess state is emitted', (tester) async {
      bool onPopSuccessCalled = false;
      final VerifyOtpSuccess successState = VerifyOtpSuccess(OtpSuccessModel());

      whenListen(
        mockCubit,
        Stream.fromIterable(<VerifyOtpSuccess>[successState]),
        initialState: VerifyOtpInitial(),
      );
      when(() => mockCubit.state).thenReturn(VerifyOtpInitial());

      await tester.pumpWidget(buildTestWidget(
        onPopSuccess: (VerifyOtpState state) {
          onPopSuccessCalled = true;
        },
      ));
      await tester.pump();

      expect(onPopSuccessCalled, isTrue);
    });

    testWidgets('shows error text when VerifyOtpFailed state is emitted', (tester) async {
      final String errorMessage = 'Invalid OTP code';
      final VerifyOtpFailed failedState = VerifyOtpFailed(
        error: ErrorUIModel(userMessage: errorMessage),
        unknownError: false,
      );

      whenListen(
        mockCubit,
        Stream.fromIterable([failedState]),
        initialState: VerifyOtpInitial(),
      );

      await tester.pumpWidget(buildTestWidget());
      await tester.pump();

      expect(find.text(errorMessage), findsOneWidget);
    });

    testWidgets('shows limit exceeded dialog when LimitExceedOtp state is emitted', (tester) async {
      final LimitExceedOtp limitState = LimitExceedOtp(
        errorText: 'Too many attempts',
      );

      whenListen(
        mockCubit,
        Stream.fromIterable([limitState]),
        initialState: VerifyOtpInitial(),
      );

      when(() => mockDialogFunction.showDialogConfirm(
            alertType: any(named: 'alertType'),
            title: any(named: 'title'),
            content: any(named: 'content'),
            dialogId: any(named: 'dialogId'),
            isDismissible: any(named: 'isDismissible'),
            textPositive: any(named: 'textPositive'),
            onClickPositive: any(named: 'onClickPositive'),
          )).thenAnswer((_) async => true);

      await tester.pumpWidget(buildTestWidget());
      await tester.pump();

      verify(() => mockDialogFunction.showDialogConfirm(
            alertType: DialogAlertType.error,
            title: EvoStrings.maxTriesReached,
            content: 'Too many attempts',
            dialogId: any(named: 'dialogId'),
            isDismissible: false,
            textPositive: EvoStrings.backToHomePage,
            onClickPositive: any(named: 'onClickPositive'),
          )).called(1);
    });

    testWidgets('shows session expired dialog when VerifyOtpSessionExpired state is emitted',
        (WidgetTester tester) async {
      whenListen(
        mockCubit,
        Stream.fromIterable(<VerifyOtpSessionExpired>[VerifyOtpSessionExpired()]),
        initialState: VerifyOtpInitial(),
      );

      when(() => mockDialogFunction.showDialogSessionTokenExpired(
            type: any(named: 'type'),
          )).thenAnswer((_) async => true);

      await tester.pumpWidget(buildTestWidget());
      await tester.pump();

      verify(() => mockDialogFunction.showDialogSessionTokenExpired(
            type: any(named: 'type'),
          )).called(1);
    });
  });

  group('VerifyOtpPage Interaction Tests', () {
    testWidgets('calls resendOtp when resend is triggered', (tester) async {
      when(() => mockCubit.state).thenReturn(VerifyOtpInitial());

      await tester.pumpWidget(buildTestWidget());
      await tester.pump();

      // Find OTP widget
      final otpWidget = find.byType(EvoOtpWidget);
      expect(otpWidget, findsOneWidget);

      // Get the widget instance
      final EvoOtpWidget widget = tester.widget(otpWidget);

      // Call the resend function
      widget.onResendOtp();

      verify(() => mockCubit.resendOtp(
            mockPhoneNumber,
            mockSessionToken,
          )).called(1);
    });
  });
}
