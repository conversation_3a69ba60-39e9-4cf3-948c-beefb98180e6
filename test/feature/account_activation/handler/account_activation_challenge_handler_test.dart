// Mock classes
import 'package:evoapp/data/response/account_activation_entity.dart';
import 'package:evoapp/feature/account_activation/handler/account_activation_challenge_handler.dart';
import 'package:evoapp/feature/account_activation/handler/account_activation_ui_handler.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockAccountActivationUiHandler extends Mock implements AccountActivationUiHandler {}

class MockErrorCallback extends Mock {
  void call(ErrorUIModel? uiModel);
}

void main() {
  late MockAccountActivationUiHandler mockUiHandler;
  late AccountActivationChallengeHandler challengeHandler;
  late MockErrorCallback mockErrorCallback;

  setUpAll(() {
    registerFallbackValue(AccountActivationEntity.unserializable());
    registerFallbackValue(({required AccountActivationEntity entity}) {});
  });

  setUp(() {
    mockErrorCallback = MockErrorCallback();
    mockUiHandler = MockAccountActivationUiHandler();
    challengeHandler = AccountActivationChallengeHandler(
      onError: mockErrorCallback.call,
    )..uiHandler = mockUiHandler;
  });

  group('nextChallenge', () {
    setUp(() {
      when(() => mockUiHandler.verifyOtp(
            phoneNumber: any(named: 'phoneNumber'),
            entity: any(named: 'entity'),
          )).thenAnswer((_) {});

      when(() => mockUiHandler.createUsername(entity: any(named: 'entity'))).thenAnswer((_) {});
      when(() => mockUiHandler.createPin()).thenAnswer((_) {});
      when(() => mockUiHandler.activateAccountSuccess()).thenAnswer((_) {});
    });

    final String mockPhoneNumber = '**********';
    test('should call verifyOtp for verify_otp challenge type', () {
      final AccountActivationEntity entity =
          AccountActivationEntity(challengeType: 'verify_otp', sessionToken: 'token');

      challengeHandler.nextChallenge(
        entity: entity,
        phoneNumber: mockPhoneNumber,
      );

      verify(() => mockUiHandler.verifyOtp(
            phoneNumber: mockPhoneNumber,
            entity: entity,
          )).called(1);
    });

    test('should call onError for unknown challenge type', () {
      when(() => mockUiHandler.onError).thenReturn(mockErrorCallback.call);
      when(() => mockErrorCallback.call(any())).thenReturn(null);

      final AccountActivationEntity entity =
          AccountActivationEntity(challengeType: 'unknown-challenge', sessionToken: 'token');
      challengeHandler.nextChallenge(entity: entity);

      verify(() => mockErrorCallback.call(any())).called(1);
    });

    test('should call verifySelfie for face_auth challenge type', () {
      final AccountActivationEntity entity =
          AccountActivationEntity(challengeType: 'face_auth', sessionToken: 'token');

      challengeHandler.nextChallenge(
        entity: entity,
        phoneNumber: mockPhoneNumber,
      );

      verify(() => mockUiHandler.verifySelfie(
            entity: entity,
          )).called(1);
    });

    test('should call createUsername for challenge type create_user_name', () {
      final AccountActivationEntity entity =
          AccountActivationEntity(challengeType: 'create_user_name', sessionToken: 'token');

      challengeHandler.nextChallenge(
        entity: entity,
        phoneNumber: mockPhoneNumber,
      );

      verify(() => mockUiHandler.createUsername(entity: any(named: 'entity'))).called(1);
    });

    test('should call createPin for challenge type create_pin', () {
      final AccountActivationEntity entity =
          AccountActivationEntity(challengeType: 'create_pin', sessionToken: 'token');

      challengeHandler.nextChallenge(
        entity: entity,
        phoneNumber: mockPhoneNumber,
      );

      verify(() => mockUiHandler.createPin()).called(1);
    });

    test('should call verifyEmail for challenge type verify_email', () {
      final AccountActivationEntity entity =
          AccountActivationEntity(challengeType: 'verify_email', sessionToken: 'token');

      challengeHandler.nextChallenge(entity: entity);

      verify(() => mockUiHandler.verifyEmail(entity: entity)).called(1);
    });

    test('should call verifyOtpEmail for challenge type verify_email_otp', () {
      final AccountActivationEntity entity =
          AccountActivationEntity(challengeType: 'verify_email_otp', sessionToken: 'token');

      challengeHandler.nextChallenge(entity: entity);

      verify(() => mockUiHandler.verifyEmailOtp(entity: entity)).called(1);
    });

    test('should call activateAccountSuccess for challenge type none', () {
      final AccountActivationEntity entity =
          AccountActivationEntity(challengeType: 'none', sessionToken: 'token');

      challengeHandler.nextChallenge(
        entity: entity,
        phoneNumber: mockPhoneNumber,
      );

      verify(() => mockUiHandler.activateAccountSuccess()).called(1);
    });
  });
}
