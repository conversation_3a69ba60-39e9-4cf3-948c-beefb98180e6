import 'package:evoapp/data/response/account_activation_entity.dart';
import 'package:evoapp/feature/account_activation/create_username/create_username_screen.dart';
import 'package:evoapp/feature/account_activation/handler/account_activation_ui_handler.dart';
import 'package:evoapp/feature/account_activation/verify_email/verify_email_screen.dart';
import 'package:evoapp/feature/ekyc/intro/face_capture_check_screen.dart';
import 'package:evoapp/feature/pin/change_pin/change_pin_arg.dart';
import 'package:evoapp/feature/verify_otp/cubit/otp_success_model.dart';
import 'package:evoapp/feature/verify_otp/cubit/verify_otp_cubit.dart';
import 'package:evoapp/feature/verify_otp/verify_otp_page.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';
import '../../../util/flutter_test_config.dart';

class MockOnErrorCallback extends Mock {
  void call(ErrorUIModel? error);
}

class MockOnSuccessCallback extends Mock {
  void call({required AccountActivationEntity entity});
}

class MockBuildContext extends Mock implements BuildContext {}

void main() {
  group('AccountActivationType', () {
    test('should return verifyOTP for "verify_otp"', () {
      final AccountActivationType result = AccountActivationType.fromString('verify_otp');

      expect(result, AccountActivationType.verifyOTP);
    });

    test('should return verifySelfie for "face_auth"', () {
      final AccountActivationType result = AccountActivationType.fromString('face_auth');

      expect(result, AccountActivationType.verifySelfie);
    });

    test('should return createUsername for "create_user_name"', () {
      final AccountActivationType result = AccountActivationType.fromString('create_user_name');

      expect(result, AccountActivationType.createUsername);
    });

    test('should return createPin for "create_pin"', () {
      final AccountActivationType result = AccountActivationType.fromString('create_pin');

      expect(result, AccountActivationType.createPin);
    });

    test('should return verifyEmail for "verify_email"', () {
      final AccountActivationType result = AccountActivationType.fromString('verify_email');

      expect(result, AccountActivationType.verifyEmail);
    });

    test('should return none for "none"', () {
      final AccountActivationType result = AccountActivationType.fromString('none');

      expect(result, AccountActivationType.none);
    });

    test('should return unknown for null', () {
      final result = AccountActivationType.fromString(null);

      expect(result, AccountActivationType.unknown);
    });

    test('should return unknown for unrecognized value', () {
      final result = AccountActivationType.fromString('unrecognized_value');

      expect(result, AccountActivationType.unknown);
    });

    test('should return unknown for empty string', () {
      final result = AccountActivationType.fromString('');

      expect(result, AccountActivationType.unknown);
    });

    test('value property should match the string constant', () {
      expect(AccountActivationType.verifyOTP.value, AccountActivationType.verifyOTPValue);
      expect(AccountActivationType.verifySelfie.value, AccountActivationType.verifySelfieValue);
      expect(AccountActivationType.createUsername.value, AccountActivationType.createUsernameValue);
      expect(AccountActivationType.createPin.value, AccountActivationType.createPinValue);
      expect(AccountActivationType.verifyEmail.value, AccountActivationType.verifyEmailValue);
      expect(AccountActivationType.verifyEmailOtp.value, AccountActivationType.verifyEmailOtpValue);
      expect(AccountActivationType.none.value, AccountActivationType.noneValue);
      expect(AccountActivationType.unknown.value, AccountActivationType.unknownValue);
    });
  });

  group('AccountActivationUiHandler', () {
    late MockOnSuccessCallback mockOnSuccess;
    late MockOnErrorCallback mockOnError;
    final AccountActivationEntity mockEntity = AccountActivationEntity(
      challengeType: 'challenge-type',
      sessionToken: 'session-token',
      otpResendSecs: 30,
      otpValiditySecs: 120,
    );
    late final BuildContext mockNavigatorContext;
    late AccountActivationUiHandler handler;

    setUpAll(() {
      mockNavigatorContext = MockBuildContext();
      setUpMockGlobalKeyProvider(mockNavigatorContext);

      getIt.registerSingleton<CommonNavigator>(MockCommonNavigator());

      registerFallbackValue(AccountActivationEntity());
    });

    setUp(() {
      mockOnSuccess = MockOnSuccessCallback();
      mockOnError = MockOnErrorCallback();
      handler = AccountActivationUiHandler(
        onError: mockOnError.call,
        onSuccess: mockOnSuccess.call,
      );
    });

    group('verifyOtp', () {
      final String mockPhoneNumber = '**********';

      test('should navigate Screen.verifyOtpScreen with correct args', () {
        handler.verifyOtp(
          entity: mockEntity,
          phoneNumber: mockPhoneNumber,
        );

        final dynamic captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.verifyOtpScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first;

        expect(
            captured,
            isA<VerifyOtpPageArg>().having(
                (VerifyOtpPageArg args) => (
                      args.contactInfo,
                      args.otpResendSecs,
                      args.otpValiditySecs,
                      args.otpValiditySecs,
                      args.verifyOtpType,
                      args.sessionToken,
                    ),
                'verify VerifyOtpPageArg',
                (
                  mockPhoneNumber,
                  mockEntity.otpResendSecs,
                  mockEntity.otpValiditySecs,
                  mockEntity.otpValiditySecs,
                  VerifyOtpType.activateAccount,
                  mockEntity.sessionToken,
                )));
      });

      test(
          'should called onSuccess when called onPopSuccess(state), which state is VerifyOtpSuccess',
          () {
        handler.verifyOtp(
          entity: mockEntity,
          phoneNumber: mockPhoneNumber,
        );

        final VerifyOtpPageArg captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.verifyOtpScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as VerifyOtpPageArg;
        captured.onPopSuccess?.call(VerifyOtpSuccess(OtpSuccessModel(
          challengeType: 'challenge-type',
          sessionToken: 'session-token',
        )));
        final dynamic onSuccessArgs = verify(() => mockOnSuccess.call(
              entity: captureAny(named: 'entity'),
            )).captured.first;

        expect(
            onSuccessArgs,
            isA<AccountActivationEntity>().having(
              (AccountActivationEntity args) => (
                args.challengeType,
                args.sessionToken,
              ),
              'verify onSuccess args',
              ('challenge-type', 'session-token'),
            ));
      });

      test('should called onError when called onPopSuccess(state), which state is VerifyOtpFailed',
          () {
        final ErrorUIModel mockError = ErrorUIModel();

        handler.verifyOtp(
          entity: mockEntity,
          phoneNumber: mockPhoneNumber,
        );
        final VerifyOtpPageArg captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.verifyOtpScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as VerifyOtpPageArg;
        captured.onPopSuccess?.call(VerifyOtpFailed(error: mockError));
        final dynamic onErrorArgs = verify(() => mockOnError.call(captureAny())).captured.first;

        expect(onErrorArgs, mockError);
      });
    });

    group('verifySelfie', () {
      test('should navigate Screen.verifyOtpScreen with correct args', () {
        handler.verifySelfie(
          entity: mockEntity,
        );

        final dynamic captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.faceCaptureCheckScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first;

        expect(
            captured,
            isA<FaceCaptureCheckScreenArgs>().having(
                (FaceCaptureCheckScreenArgs args) => (args.sessionToken,),
                'verify FaceCaptureCheckScreenArgs',
                (mockEntity.sessionToken,)));
      });

      test('should called onSuccess when called onPopSuccess(state) ', () {
        handler.verifySelfie(
          entity: mockEntity,
        );

        final FaceCaptureCheckScreenArgs captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.faceCaptureCheckScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as FaceCaptureCheckScreenArgs;
        final AccountActivationEntity entity = AccountActivationEntity();

        captured.onPopSuccess.call(entity);
        verify(() => mockOnSuccess.call(entity: entity)).called(1);
      });

      test(
          'should called onError when called onPopSuccess(state) with entity is not AccountActivationEntity ',
          () {
        handler.verifySelfie(
          entity: mockEntity,
        );

        final FaceCaptureCheckScreenArgs captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.faceCaptureCheckScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as FaceCaptureCheckScreenArgs;
        final BaseEntity entity = BaseEntity();

        captured.onPopSuccess.call(entity);
        verify(() => mockOnError.call(any())).called(1);
      });
    });

    group('createUsername', () {
      test('should navigate Screen.createUsernameScreen', () {
        final AccountActivationEntity entity = AccountActivationEntity(
          sessionToken: 'mock-session-token',
        );
        handler.createUsername(
          entity: entity,
        );

        final dynamic captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.createUsernameScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first;

        expect(
            captured,
            isA<CreateUsernameScreenArgs>().having(
                (CreateUsernameScreenArgs args) => (args.sessionToken,),
                'verify CreateUsernameScreenArgs',
                (entity.sessionToken,)));
      });

      test('should called onSuccess when called onPopSuccess(state)', () {
        handler.createUsername(entity: AccountActivationEntity());

        final CreateUsernameScreenArgs captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.createUsernameScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as CreateUsernameScreenArgs;
        final AccountActivationEntity entity = AccountActivationEntity();

        captured.onPopSuccess.call(entity);
        verify(() => mockOnSuccess.call(entity: entity)).called(1);
      });

      test(
          'should called onError when called onPopSuccess(state) with entity is not AccountActivationEntity ',
          () {
        handler.createUsername(entity: AccountActivationEntity());

        final CreateUsernameScreenArgs captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.createUsernameScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as CreateUsernameScreenArgs;
        final BaseEntity entity = BaseEntity();

        captured.onPopSuccess.call(entity);
        verify(() => mockOnError.call(any())).called(1);
      });
    });

    group('create_pin', () {
      test('should navigate Screen.createNewPinScreen', () {
        handler.createPin();

        verify(
          () => mockNavigatorContext.pushNamed(
            Screen.createNewPinScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first;
      });

      test('should called onSuccess when called onPopSuccess(state)', () {
        handler.createPin();

        final CreateNewPinArgs captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.createNewPinScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as CreateNewPinArgs;
        final AccountActivationEntity entity = AccountActivationEntity();

        captured.onSuccess.call(entity);
        verify(() => mockOnSuccess.call(entity: entity)).called(1);
      });

      test(
          'should called onError when called onPopSuccess(state) with entity is not AccountActivationEntity ',
          () {
        handler.createPin();

        final CreateNewPinArgs captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.createNewPinScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as CreateNewPinArgs;
        final BaseEntity entity = BaseEntity();

        captured.onSuccess.call(entity);
        verify(() => mockOnError.call(any())).called(1);
      });
    });

    group('verify_email', () {
      test('should navigate Screen.verifyEmailScreen', () {
        handler.verifyEmail(entity: mockEntity);

        verify(
          () => mockNavigatorContext.pushNamed(
            Screen.verifyEmailScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).called(1);
      });

      test('should called onSuccess when called onPopSuccess(state)', () {
        handler.verifyEmail(entity: mockEntity);

        final VerifyEmailArg captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.verifyEmailScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as VerifyEmailArg;
        final AccountActivationEntity entity = AccountActivationEntity();

        captured.onPopSuccess.call(entity);
        verify(() => mockOnSuccess.call(entity: entity)).called(1);
      });

      test(
          'should called onError when called onPopSuccess(state) with entity is not AccountActivationEntity ',
          () {
        handler.verifyEmail(entity: mockEntity);

        final VerifyEmailArg captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.verifyEmailScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as VerifyEmailArg;

        captured.onPopSuccess.call(BaseEntity());
        verify(() => mockOnError.call(any())).called(1);
      });
    });

    group('verify_email_otp', () {
      test('should navigate Screen.verifyOtpScreen', () {
        handler.verifyEmailOtp(entity: mockEntity);

        verify(
          () => mockNavigatorContext.pushNamed(
            Screen.verifyOtpScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).called(1);
      });

      test(
          'should called onSuccess when called onPopSuccess(state), which state is VerifyOtpSuccess',
          () {
        handler.verifyEmailOtp(entity: mockEntity);

        final VerifyOtpPageArg captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.verifyOtpScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as VerifyOtpPageArg;
        captured.onPopSuccess?.call(VerifyOtpSuccess(OtpSuccessModel(
          challengeType: 'challenge-type',
          sessionToken: 'session-token',
        )));
        final dynamic onSuccessArgs = verify(() => mockOnSuccess.call(
              entity: captureAny(named: 'entity'),
            )).captured.first;

        expect(
            onSuccessArgs,
            isA<AccountActivationEntity>().having(
              (AccountActivationEntity args) => (
                args.challengeType,
                args.sessionToken,
              ),
              'verify onSuccess args',
              ('challenge-type', 'session-token'),
            ));
      });

      test('should called onError when called onPopSuccess(state), which state is VerifyOtpFailed',
          () {
        final ErrorUIModel mockError = ErrorUIModel();

        handler.verifyEmailOtp(entity: mockEntity);

        final VerifyOtpPageArg captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.verifyOtpScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as VerifyOtpPageArg;
        captured.onPopSuccess?.call(VerifyOtpFailed(error: mockError));
        final dynamic onErrorArgs = verify(() => mockOnError.call(captureAny())).captured.first;

        expect(onErrorArgs, mockError);
      });
    });

    group('activateAccountSuccess', () {
      test('should pushReplacementNamed Screen.activateAccountSuccessScreen', () {
        handler.activateAccountSuccess();

        verify(
          () => mockNavigatorContext.goNamed(
            Screen.activateAccountSuccessScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first;
      });
    });
  });
}
