import 'package:evoapp/feature/account_activation/active_virtual_card/widgets/frame_card_with_name_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

void main() {
  late CommonImageProvider imageProvider;
  setUpAll(() {
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getItRegisterColor();
    getItRegisterTextStyle();

    imageProvider = getIt.get<CommonImageProvider>();
  });

  setUp(() {
    when(() => imageProvider.asset(
          any(),
          fit: any(named: 'fit'),
        )).thenAnswer((_) => Container());
  });

  tearDown(() {
    reset(imageProvider);
  });

  testWidgets('NameCardWidget displays correct name',
      (WidgetTester tester) async {
    const String testName = 'Test Name';

    // Build the widget
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: FrameCardWithNameWidget(
            name: testName,
          ),
        ),
      ),
    );

    // Verify the title text style
    final Text nameOnCardText = tester.widget(find.text(EvoStrings.nameOnCard));
    expect(
        nameOnCardText.style,
        evoTextStyles.regular(
          TextSize.sm,
          color: evoColors.greyScale75,
        ));

    // Verify the name is displayed & text style
    expect(find.text(testName), findsOneWidget);
    final Text nameText = tester.widget(find.text(testName));
    expect(
        nameText.style,
        evoTextStyles.bold(
          TextSize.base,
          color: evoColors.defaultWhite,
        ));

    // Verify the Positioned widget is displayed
    final Positioned positionedWidget = tester.widget(find.ancestor(
      of: find.text(EvoStrings.nameOnCard),
      matching: find.byType(Positioned),
    ));
    expect(positionedWidget.left, 1);
    expect(positionedWidget.right, 1);
    expect(positionedWidget.bottom, 1);
  });

  testWidgets('NameCardWidget displays correct background image',
      (WidgetTester tester) async {
    const String testName = 'Test Name';

    // Build the widget
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: FrameCardWithNameWidget(
            name: testName,
          ),
        ),
      ),
    );

    // Verify the background image is displayed
    verify(() => imageProvider.asset(
          EvoImages.frameVirtualCardNameHolder,
          fit: BoxFit.fill,
        )).called(1);
  });
}
