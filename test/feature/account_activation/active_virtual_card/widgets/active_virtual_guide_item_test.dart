import 'package:evoapp/feature/account_activation/active_virtual_card/widgets/active_virtual_guide_item.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/screen_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

void main() {
  late CommonImageProvider imageProvider;
  setUpAll(() {
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getItRegisterColor();
    getItRegisterTextStyle();

    imageProvider = getIt.get<CommonImageProvider>();
  });

  setUp(() {
    when(() => imageProvider.asset(
          any(),
          height: any(named: 'height'),
          width: any(named: 'width'),
          fit: any(named: 'fit'),
          color: any(named: 'color'),
        )).thenAnswer((_) => Container());
  });

  testWidgets('ActiveVirtualGuideItem displays correct title and icon',
      (WidgetTester tester) async {
    const String testTitle = 'Test Title';
    const String testIconAsset = 'assets/test_icon.svg';

    // Build the widget
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: ActiveVirtualGuideItem(
            title: testTitle,
            iconAsset: testIconAsset,
          ),
        ),
      ),
    );

    // Verify the title is displayed
    expect(find.text(testTitle), findsOneWidget);

    // Verify the icon is displayed
    verify(() => imageProvider.asset(testIconAsset,
        width: 48.w,
        height: 48.w,
        fit: BoxFit.scaleDown)).called(1);
  });
}
