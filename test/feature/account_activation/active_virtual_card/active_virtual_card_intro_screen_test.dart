import 'package:evoapp/feature/account_activation/active_virtual_card/active_virtual_card_intro_screen.dart';
import 'package:evoapp/feature/account_activation/active_virtual_card/widgets/active_virtual_guide_item.dart';
import 'package:evoapp/feature/account_activation/active_virtual_card/widgets/frame_card_with_name_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';

void main() {
  late CommonNavigator commonNavigator;
  const String expectedCardHolderName = 'John <PERSON>';

  setUpAll(() {
    initConfigEvoPageStateBase();

    commonNavigator = getIt.get<CommonNavigator>();
    when(() => getIt.get<CommonImageProvider>().asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          fit: any(named: 'fit'),
        )).thenReturn(Container());
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();
  });

  test('test ActiveVirtualCardArg should create an instance with provided cardHolderName', () {
    final ActiveVirtualCardArg arg = ActiveVirtualCardArg(
      cardHolderName: expectedCardHolderName,
    );

    expect(arg, isA<PageBaseArg>());
    expect(arg.cardHolderName, equals(expectedCardHolderName));
  });

  group('ActiveVirtualCardIntroScreen', () {
    test('pushNamed should navigate to the correct route with correct arguments', () async {
      await ActiveVirtualCardIntroScreen.pushNamed(
        cardHolderName: expectedCardHolderName,
      );
      verify(() => commonNavigator.pushNamed(
            any(),
            Screen.activeVirtualCardIntroScreen.name,
            extra: any(
              named: 'extra',
              that: predicate<ActiveVirtualCardArg>(
                (ActiveVirtualCardArg arg) => arg.cardHolderName == expectedCardHolderName,
              ),
            ),
          )).called(1);
    });

    buildWidget(WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: ActiveVirtualCardIntroScreen(
            cardHolderName: expectedCardHolderName,
          ),
        ),
      );
      await tester.pumpAndSettle();
    }

    group('render UI', () {
      testWidgets('should render initial UI correctly', (WidgetTester tester) async {
        await buildWidget(tester);

        // verify title
        expect(find.text(EvoStrings.activeVirtualCardTitle), findsOneWidget);
        expect(find.text(EvoStrings.activeVirtualCardDesc), findsOneWidget);

        expect(
            find.byWidgetPredicate((Widget widget) =>
                widget is FrameCardWithNameWidget && widget.name == expectedCardHolderName),
            findsOneWidget);

        // Find all ActiveVirtualGuideItem widgets
        final List<ActiveVirtualGuideItem> guideItems =
            tester.widgetList<ActiveVirtualGuideItem>(find.byType(ActiveVirtualGuideItem)).toList();

        // Verify we have exactly 3 items
        expect(guideItems.length, 3);

        // Verify each item has correct properties in the right order
        expect(guideItems[0].iconAsset, equals(EvoImages.icActiveVirtualCardGuideline1));
        expect(guideItems[0].title, equals(EvoStrings.activeVirtualCardGuideLine1));

        expect(guideItems[1].iconAsset, equals(EvoImages.icActiveVirtualCardGuideline2));
        expect(guideItems[1].title, equals(EvoStrings.activeVirtualCardGuideLine2));

        expect(guideItems[2].iconAsset, equals(EvoImages.icActiveVirtualCardGuideline3));
        expect(guideItems[2].title, equals(EvoStrings.activeVirtualCardGuideLine3));

        /// verify button
        expect(find.text(EvoStrings.ctaActiveVirtualCard), findsOneWidget);
        expect(find.text(EvoStrings.ctaActiveLater), findsOneWidget);
      });

      testWidgets('should render guideline items in correct order', (WidgetTester tester) async {
        await buildWidget(tester);
      });
    });

    group('on button click', () {
      testWidgets('should navigate to VerifyOtpPage when active button is clicked',
          (WidgetTester tester) async {
        await buildWidget(tester);

        await tester.tap(find.text(EvoStrings.ctaActiveVirtualCard));
        await tester.pumpAndSettle();

        verify(() => commonNavigator.pushNamed(
              any(),
              Screen.verifyOtpScreen.name,
              extra: any(named: 'extra'),
            )).called(1);
      });
    });
  });
}
