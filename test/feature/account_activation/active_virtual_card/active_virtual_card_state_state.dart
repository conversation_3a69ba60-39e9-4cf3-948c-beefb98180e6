import 'package:evoapp/feature/account_activation/active_virtual_card/active_virtual_card_cubit.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('ActiveVirtualCardInitial supports value equality', () {
    expect(ActiveVirtualCardInitial(), isA<ActiveVirtualCardIntroState>());
  });

  test('ActiveVirtualCardStateLoading supports value equality', () {
    expect(ActiveVirtualCardStateLoading(), isA<ActiveVirtualCardIntroState>());
  });

  test('ActiveVirtualCardSuccess supports value equality', () {
    expect(ActiveVirtualCardSuccess(), isA<ActiveVirtualCardIntroState>());
  });

  test('ActiveVirtualCardFailed has correct error message', () {
    final ErrorUIModel errorUIModel = ErrorUIModel(userMessage:'Error');
    final ActiveVirtualCardFailed state = ActiveVirtualCardFailed(errorUIModel);
    expect(state,  isA<ActiveVirtualCardIntroState>());
    expect(state.errorUIModel.userMessage, 'Error');
  });
}