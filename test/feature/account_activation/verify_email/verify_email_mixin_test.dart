// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/base/evo_page_state_base.dart';
import 'package:evoapp/feature/account_activation/verify_email/cubit/verify_email_cubit.dart';
import 'package:evoapp/feature/account_activation/verify_email/cubit/verify_email_state.dart';
import 'package:evoapp/feature/account_activation/verify_email/verify_email_mixin.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';
import '../../../util/app_mock_cubit.dart';

class MockVerifyEmailCubit extends AppMockCubit<VerifyEmailState> implements VerifyEmailCubit {}

class TestPage extends PageBase {
  const TestPage({super.key});

  @override
  State<TestPage> createState() => _TestPageState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: 'mixin');
}

class _TestPageState extends EvoPageStateBase<TestPage> with VerifyEmailMixin<TestPage> {
  int onVerifyEmailSuccessCallCount = 0;

  @override
  Widget getContentWidget(BuildContext context) {
    return provideVerifyEmailCubit(
      child: SizedBox(),
    );
  }

  @override
  void onVerifyEmailSuccess(BaseEntity entity) {
    onVerifyEmailSuccessCallCount++;
  }
}

void main() {
  late MockVerifyEmailCubit mockCubit;

  setUpAll(() {
    initConfigEvoPageStateBase();
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();
    mockCubit = MockVerifyEmailCubit()..emit(VerifyEmailInitial());

    when(() => mockEvoUtilFunction.showHudLoading()).thenAnswer((_) async {});
    when(() => mockEvoUtilFunction.hideHudLoading()).thenAnswer((_) async {});
  });

  tearDownAll(() {
    getIt.reset();
  });

  Widget createWidgetUnderTest() {
    return MaterialApp(
      home: BlocProvider<VerifyEmailCubit>.value(
        value: mockCubit,
        child: TestPage(),
      ),
    );
  }

  group('VerifyEmailMixin', () {
    testWidgets('should show loading when state is VerifyEmailLoading',
        (WidgetTester tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      mockCubit.emit(VerifyEmailLoading());
      await tester.pump();

      verify(() => mockEvoUtilFunction.showHudLoading()).called(1);
    });

    testWidgets('should hide loading when state is not VerifyEmailLoading',
        (WidgetTester tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      mockCubit.emit(VerifyEmailSuccess(BaseEntity()));
      await tester.pump();

      verify(() => mockEvoUtilFunction.hideHudLoading()).called(1);
    });

    testWidgets('should provide cubit through BlocProvider', (WidgetTester tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      expect(find.byType(BlocProvider<VerifyEmailCubit>), findsAtLeast(1));
      expect(find.byType(BlocListener<VerifyEmailCubit, VerifyEmailState>), findsAtLeast(1));
    });

    testWidgets('should handle VerifyEmailSuccess state correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      mockCubit.emit(VerifyEmailSuccess(BaseEntity()));
      await tester.pump();

      verify(() => mockEvoUtilFunction.hideHudLoading()).called(1);
      final _TestPageState state = tester.state<_TestPageState>(find.byType(TestPage));
      expect(state.onVerifyEmailSuccessCallCount, 1);
    });
  });
}
