// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/feature/account_activation/verify_email/cubit/validate_email_cubit.dart';
import 'package:evoapp/feature/account_activation/verify_email/cubit/validate_email_state.dart';
import 'package:evoapp/feature/account_activation/verify_email/cubit/verify_email_cubit.dart';
import 'package:evoapp/feature/account_activation/verify_email/cubit/verify_email_state.dart';
import 'package:evoapp/feature/account_activation/verify_email/input_email_screen.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/widget/buttons.dart';
import 'package:evoapp/widget/evo_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';
import '../../../util/app_mock_cubit.dart';

class MockValidateEmailCubit extends AppMockCubit<ValidateEmailState>
    implements ValidateEmailCubit {}

class MockVerifyEmailCubit extends AppMockCubit<VerifyEmailState> implements VerifyEmailCubit {}

class MockBaseEntity extends Mock implements BaseEntity {}

class MockOnPopSuccess extends Mock {
  void call(BaseEntity entity);
}

void main() {
  late MockValidateEmailCubit validateEmailCubit;
  late MockVerifyEmailCubit verifyEmailCubit;

  const String testEmail = '<EMAIL>';

  setUpAll(() {
    initConfigEvoPageStateBase();
    when(() => mockEvoUtilFunction.showHudLoading()).thenAnswer((_) async {});
    when(() => mockEvoUtilFunction.hideHudLoading()).thenAnswer((_) async {});
  });

  tearDownAll(() {
    getIt.reset();
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();

    validateEmailCubit = MockValidateEmailCubit()..emit(ValidateEmailInitial());
    verifyEmailCubit = MockVerifyEmailCubit()..emit(VerifyEmailInitial());

    when(() => validateEmailCubit.validate(any())).thenAnswer((_) async {});
    when(() => validateEmailCubit.onUpdateEmail()).thenAnswer((_) async {});
    when(() => verifyEmailCubit.verify(email: any(named: 'email'))).thenAnswer((_) async {});
  });

  Widget buildWidgetInTest({
    String? email,
    String? sessionToken,
    void Function(BaseEntity entity)? onPopSuccess,
  }) {
    return MaterialApp(
      home: MultiBlocProvider(
        providers: <BlocProvider<dynamic>>[
          BlocProvider<ValidateEmailCubit>.value(value: validateEmailCubit),
          BlocProvider<VerifyEmailCubit>.value(value: verifyEmailCubit),
        ],
        child: InputEmailScreen(
          email: email,
          sessionToken: sessionToken,
          onPopSuccess: onPopSuccess ?? (_) {},
        ),
      ),
    );
  }

  group('InputEmailScreen', () {
    test('should self navigate', () {
      InputEmailScreen.pushNamed(
        email: testEmail,
        onPopSuccess: (_) {},
      );

      verify(() => mockNavigatorContext.pushNamed(
            Screen.inputEmailScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });

    testWidgets('should display title and input field', (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());

      expect(find.text(EvoStrings.inputEmailTitle), findsOneWidget);
      expect(find.byType(EvoTextField), findsOneWidget);
      expect(find.widgetWithText(PrimaryButton, EvoStrings.sendEmailCodeBtn2), findsOneWidget);
    });

    testWidgets('should pre-fill email if provided', (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest(email: testEmail));

      final TextField textField = tester.widget<TextField>(
        find.descendant(
          of: find.byType(EvoTextField),
          matching: find.byType(TextField),
        ),
      );
      expect(textField.controller?.text, testEmail);
    });

    testWidgets('should call validate email when send-code button is tapped',
        (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest(email: testEmail));

      await tester.tap(find.text(EvoStrings.sendEmailCodeBtn2));
      await tester.pump();

      verify(() => validateEmailCubit.validate(testEmail)).called(1);
    });

    testWidgets('should call validate email when text field is submitted',
        (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest(email: testEmail));

      final TextField textField = tester.widget(find.byType(TextField));
      textField.onSubmitted?.call('');

      verify(() => validateEmailCubit.validate(testEmail)).called(1);
    });

    testWidgets('should display error when validation fails', (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());

      const String errorMessage = 'Invalid email format';
      validateEmailCubit.emit(ValidateEmailFailure(error: errorMessage));
      await tester.pump();

      expect(find.text(errorMessage), findsOneWidget);
    });

    testWidgets('should verify email when validation succeeds', (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest(email: testEmail));

      validateEmailCubit.emit(ValidateEmailSuccess(testEmail));
      await tester.pump();

      verify(() => verifyEmailCubit.verify(email: testEmail)).called(1);
    });

    testWidgets('should call onUpdateEmail when text is changed', (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());

      validateEmailCubit.emit(ValidateEmailFailure(error: 'Invalid email'));
      await tester.pump();

      expect(find.text('Invalid email'), findsOneWidget);

      await tester.enterText(find.byType(TextField), '<EMAIL>');
      await tester.pump();

      verify(() => validateEmailCubit.onUpdateEmail()).called(1);
    });

    testWidgets('should call onPopSuccess when email verification succeeds',
        (WidgetTester tester) async {
      final MockOnPopSuccess onPopSuccess = MockOnPopSuccess();
      await tester.pumpWidget(buildWidgetInTest(onPopSuccess: onPopSuccess.call));

      final BaseEntity entity = MockBaseEntity();
      verifyEmailCubit.emit(VerifyEmailSuccess(entity));
      await tester.pump();

      verify(() => onPopSuccess.call(entity)).called(1);
    });
  });
}
