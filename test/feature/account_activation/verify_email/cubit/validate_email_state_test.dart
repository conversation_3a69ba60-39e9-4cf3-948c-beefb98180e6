// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/feature/account_activation/verify_email/cubit/validate_email_state.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ValidateEmailState', () {
    test('ValidateEmailInitial should be a subclass of ValidateEmailState', () {
      final ValidateEmailInitial state = ValidateEmailInitial();
      expect(state, isA<ValidateEmailState>());
    });

    test('ValidateEmailSuccess should hold the email value', () {
      const String email = '<EMAIL>';
      final ValidateEmailSuccess state = ValidateEmailSuccess(email);

      expect(state, isA<ValidateEmailState>());
      expect(state.email, equals(email));
    });

    test('ValidateEmailFailure should hold the error message', () {
      const String errorMessage = 'Invalid email format';
      final ValidateEmailFailure state = ValidateEmailFailure(error: errorMessage);

      expect(state, isA<ValidateEmailState>());
      expect(state.error, equals(errorMessage));
    });
  });
}
