// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/account_activation/verify_email/cubit/verify_email_cubit.dart';
import 'package:evoapp/feature/account_activation/verify_email/cubit/verify_email_state.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../constant.dart';

void main() {
  group('VerifyEmailCubit', () {
    test('initial state is VerifyEmailInitial', () {
      final VerifyEmailCubit cubit = VerifyEmailCubit();
      expect(cubit.state, isA<VerifyEmailInitial>());
    });

    blocTest<VerifyEmailCubit, VerifyEmailState>(
      'emits [VerifyEmailLoading, VerifyEmailSuccess] when sendOtp is called',
      build: () => VerifyEmailCubit(),
      act: (VerifyEmailCubit cubit) => cubit.verify(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <Matcher>[
        isA<VerifyEmailLoading>(),
        isA<VerifyEmailSuccess>(),
      ],
    );
  });
}
