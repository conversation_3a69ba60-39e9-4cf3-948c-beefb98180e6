// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/account_activation/verify_email/cubit/validate_email_cubit.dart';
import 'package:evoapp/feature/account_activation/verify_email/cubit/validate_email_state.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../constant.dart';

void main() {
  group('ValidateEmailCubit', () {
    late ValidateEmailCubit validateEmailCubit;

    setUp(() {
      validateEmailCubit = ValidateEmailCubit();
    });

    tearDown(() {
      validateEmailCubit.close();
    });

    test('initial state is ValidateEmailInitial', () {
      expect(validateEmailCubit.state, isA<ValidateEmailInitial>());
    });

    blocTest<ValidateEmailCubit, ValidateEmailState>(
      'emits [ValidateEmailSuccess] when email is valid',
      build: () => validateEmailCubit,
      act: (ValidateEmailCubit cubit) => cubit.validate('<EMAIL>'),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <Matcher>[
        isA<ValidateEmailSuccess>(),
      ],
    );

    blocTest<ValidateEmailCubit, ValidateEmailState>(
      'emits [ValidateEmailFailure] when email is invalid',
      build: () => validateEmailCubit,
      act: (ValidateEmailCubit cubit) => cubit.validate('invalid-email'),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <Matcher>[
        isA<ValidateEmailFailure>(),
      ],
    );

    blocTest<ValidateEmailCubit, ValidateEmailState>(
      'emits [ValidateEmailFailure] when email is null',
      build: () => validateEmailCubit,
      act: (ValidateEmailCubit cubit) => cubit.validate(null),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <Matcher>[
        isA<ValidateEmailFailure>(),
      ],
    );

    blocTest<ValidateEmailCubit, ValidateEmailState>(
      'emits [ValidateEmailFailure] when email is empty',
      build: () => validateEmailCubit,
      act: (ValidateEmailCubit cubit) => cubit.validate(''),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <Matcher>[
        isA<ValidateEmailFailure>(),
      ],
    );

    blocTest<ValidateEmailCubit, ValidateEmailState>(
      'emits [ValidateEmailInitial] when onUpdateEmail is called',
      build: () => validateEmailCubit,
      seed: () => ValidateEmailFailure(error: 'error'),
      act: (ValidateEmailCubit cubit) => cubit.onUpdateEmail(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <Matcher>[
        isA<ValidateEmailInitial>(),
      ],
    );

    blocTest<ValidateEmailCubit, ValidateEmailState>(
      'does not emit a new state when onUpdateEmail is called and state is already ValidateEmailInitial',
      build: () => validateEmailCubit,
      act: (ValidateEmailCubit cubit) => cubit.onUpdateEmail(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <Matcher>[],
    );
  });
}
