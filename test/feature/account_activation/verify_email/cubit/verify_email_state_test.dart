import 'package:evoapp/feature/account_activation/verify_email/cubit/verify_email_state.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('VerifyEmailState', () {
    test('Subtypes should have right type', () {
      expect(VerifyEmailInitial(), isA<VerifyEmailState>());
      expect(VerifyEmailLoading(), isA<VerifyEmailState>());
      expect(VerifyEmailSuccess(BaseEntity()), isA<VerifyEmailState>());
      expect(VerifyEmailFailure(error: ''), isA<VerifyEmailState>());
    });

    test('VerifyEmailSuccess error property should return correct value', () {
      final BaseEntity entity = BaseEntity();
      final VerifyEmailSuccess state = VerifyEmailSuccess(entity);
      expect(state.entity, entity);
    });

    test('VerifyEmailFailure error property should return correct value', () {
      const String errorMessage = 'Test error';
      final VerifyEmailFailure state = VerifyEmailFailure(error: errorMessage);
      expect(state.error, equals(errorMessage));
    });
  });
}
