import 'package:evoapp/base/evo_page_state_base.dart';
import 'package:evoapp/feature/account_activation/activation_status/activation_status_screen.dart';
import 'package:evoapp/feature/account_activation/activation_status/activation_status_ui_model.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';

class MockActivationStatusUiModel extends Mock implements ActivationStatusUiModel {}

class MockActivationStatusScreen extends ActivationStatusScreen {
  final ActivationStatusUiModel uiModel;

  MockActivationStatusScreen(
    this.uiModel,
  ) : super(key: GlobalKey(), status: ActivationStatus.none);

  @override
  EvoPageStateBase<ActivationStatusScreen> createState() => MockActivationStatusScreenState();
}

class MockActivationStatusScreenState extends ActivationStatusScreenState {
  @override
  ActivationStatusUiModel get uiModel => (widget as MockActivationStatusScreen).uiModel;
}

void main() {
  late CommonImageProvider mockImageProvider;
  late MockActivationStatusUiModel mockUiModel;
  final String mockTitle = 'mockTitle';
  final String mockDescription = 'mockDescription';
  final String mockIconAsset = 'mockIconAsset';
  final Widget mockAction1 = Text('action-1');
  final Widget mockAction2 = Text('action-2');
  final List<Widget> mockActions = <Widget>[
    mockAction1,
    mockAction2,
  ];

  setUpAll(() {
    initConfigEvoPageStateBase();
    setUpMockConfigEvoPageStateBase();

    mockImageProvider = getIt.get<CommonImageProvider>();
  });

  setUp(() {
    mockUiModel = MockActivationStatusUiModel();

    when(() => mockUiModel.title).thenReturn(mockTitle);
    when(() => mockUiModel.description).thenReturn(mockDescription);
    when(() => mockUiModel.iconAsset).thenReturn(mockIconAsset);
    when(() => mockUiModel.actions).thenReturn(mockActions);

    when(() => mockImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          fit: any(named: 'fit'),
          color: any(named: 'color'),
        )).thenAnswer((_) => const SizedBox.shrink());
  });

  tearDown(() {
    reset(mockImageProvider);
  });

  group('ActivationStatusScreen', () {
    testWidgets('renders with correct layout structure', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: MockActivationStatusScreen(mockUiModel),
        ),
      );

      expect(find.text(mockTitle), findsOneWidget);
      expect(find.text(mockDescription), findsOneWidget);
      expect(find.byWidget(mockAction1), findsOneWidget);
      expect(find.byWidget(mockAction2), findsOneWidget);

      verify(() => mockImageProvider.asset(
            mockIconAsset,
            width: any(named: 'width'),
            height: any(named: 'height'),
            fit: any(named: 'fit'),
            color: any(named: 'color'),
          )).called(1);
    });
  });
}
