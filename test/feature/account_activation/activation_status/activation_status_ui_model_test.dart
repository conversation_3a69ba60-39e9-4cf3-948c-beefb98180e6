import 'package:evoapp/feature/account_activation/activation_status/activation_status_screen.dart';
import 'package:evoapp/feature/account_activation/activation_status/activation_status_ui_model.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/resources/images.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/widget/buttons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';

void main() {
  setUpAll(() {
    initConfigEvoPageStateBase();
    setUpMockConfigEvoPageStateBase();
  });

  group('ActivationStatusUiModel', () {
    group('create', () {
      test('creates RejectedStatusUiModel for rejected status', () {
        final ActivationStatusUiModel model =
            ActivationStatusUiModel.create(ActivationStatus.rejected);
        expect(model, isA<RejectedStatusUiModel>());
      });

      test('creates ProcessingStatusUiModel for processing status', () {
        final ActivationStatusUiModel model =
            ActivationStatusUiModel.create(ActivationStatus.processing);
        expect(model, isA<ProcessingStatusUiModel>());
      });

      test('creates NoneStatusUiModel for none status', () {
        final ActivationStatusUiModel model = ActivationStatusUiModel.create(ActivationStatus.none);
        expect(model, isA<NoneStatusUiModel>());
      });

      test('creates ExistStatusUiModel for exist status', () {
        final ActivationStatusUiModel model =
            ActivationStatusUiModel.create(ActivationStatus.exist);
        expect(model, isA<ExistStatusUiModel>());
      });
    });

    group('RejectedStatusUiModel', () {
      late RejectedStatusUiModel model;

      setUp(() {
        model = RejectedStatusUiModel();
      });

      test('has correct title', () {
        expect(model.title, equals(EvoStrings.activationRejectedTitle));
      });

      test('has correct description', () {
        expect(model.description, equals(EvoStrings.activationRejectedDesc));
      });

      test('has correct icon asset', () {
        expect(model.iconAsset, equals(EvoImages.imgActivationStatusReject));
      });

      testWidgets('verify button', (tester) async {
        await tester.pumpWidget(MaterialApp(
          home: Column(
            children: model.actions,
          ),
        ));

        await tester.tap(find.text(EvoStrings.backToHomePage));

        verify(() => mockNavigatorContext.goNamed(
              Screen.welcomeScreen.name,
              extra: any(named: 'extra'),
            )).called(1);
      });
    });

    group('ProcessingStatusUiModel', () {
      late ProcessingStatusUiModel model;

      setUp(() {
        model = ProcessingStatusUiModel();
      });

      test('has correct title', () {
        expect(model.title, equals(EvoStrings.activationProcessingTitle));
      });

      test('has correct description', () {
        expect(model.description, equals(EvoStrings.activationProcessingDesc));
      });

      test('has correct icon asset', () {
        expect(model.iconAsset, equals(EvoImages.imgActivationStatusProcessing));
      });

      test('has one action button', () {
        expect(model.actions.length, equals(1));
        expect(model.actions[0], isA<PrimaryButton>());
      });

      testWidgets('verify button', (WidgetTester tester) async {
        await tester.pumpWidget(MaterialApp(
          home: Column(
            children: model.actions,
          ),
        ));

        await tester.tap(find.text(EvoStrings.backToHomePage));

        verify(() => mockNavigatorContext.goNamed(
              Screen.welcomeScreen.name,
              extra: any(named: 'extra'),
            )).called(1);
      });
    });

    group('NoneStatusUiModel', () {
      late NoneStatusUiModel model;

      setUp(() {
        model = NoneStatusUiModel();
      });

      test('has correct title', () {
        expect(model.title, equals(EvoStrings.activationNotFoundTitle));
      });

      test('has correct description', () {
        expect(model.description, equals(EvoStrings.activationNotFoundDesc));
      });

      test('has correct icon asset', () {
        expect(model.iconAsset, equals(EvoImages.imgActivationStatusNone));
      });

      test('has two action buttons', () {
        expect(model.actions.length, equals(2));
        expect(model.actions[0], isA<PrimaryButton>());
        expect(model.actions[1], isA<SecondaryButton>());
      });

      testWidgets('verify button', (WidgetTester tester) async {
        await tester.pumpWidget(MaterialApp(
          home: Column(
            children: model.actions,
          ),
        ));

        await tester.tap(find.text(EvoStrings.backToHomePage));

        verify(() => mockNavigatorContext.goNamed(
              Screen.welcomeScreen.name,
              extra: any(named: 'extra'),
            )).called(1);

        expect(find.text(EvoStrings.ctaApplyNow), findsOne);
      });
    });

    group('ExistStatusUiModel', () {
      late ExistStatusUiModel model;

      setUp(() {
        model = ExistStatusUiModel();
      });

      test('has correct title', () {
        expect(model.title, equals(EvoStrings.activationExistTitle));
      });

      test('has correct description', () {
        expect(model.description, equals(EvoStrings.activationExistDesc));
      });

      test('has correct icon asset', () {
        expect(model.iconAsset, equals(EvoImages.imgActivationStatusExisting));
      });

      test('has two action buttons', () {
        expect(model.actions.length, equals(2));
        expect(model.actions[0], isA<PrimaryButton>());
        expect(model.actions[1], isA<SecondaryButton>());
      });

      testWidgets('verify button', (WidgetTester tester) async {
        await tester.pumpWidget(MaterialApp(
          home: Column(
            children: model.actions,
          ),
        ));

        await tester.tap(find.text(EvoStrings.backToHomePage));

        verify(() => mockNavigatorContext.goNamed(
              Screen.welcomeScreen.name,
              extra: any(named: 'extra'),
            )).called(1);

        await tester.tap(find.text(EvoStrings.login));

        verify(() => mockNavigatorContext.goNamed(
              Screen.verifyUsernameScreen.name,
              extra: any(named: 'extra'),
            )).called(1);
      });
    });
  });
}
