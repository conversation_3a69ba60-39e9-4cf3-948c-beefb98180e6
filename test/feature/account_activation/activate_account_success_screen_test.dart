import 'package:evoapp/feature/account_activation/activate_account_success_screen.dart';
import 'package:evoapp/feature/biometric/activate_biometric/active_biometric_page.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../base/evo_page_state_base_test_config.dart';

class _FakeImage extends SizedBox {}

void main() {
  setUpAll(() {
    initConfigEvoPageStateBase();

    when(() => evoImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          fit: any(named: 'fit'),
        )).thenReturn(_FakeImage());
  });

  tearDownAll(() {
    getIt.reset();
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();
  });

  group('ActivateAccountSuccessScreen', () {
    test('should have correct eventTrackingScreenId', () async {
      expect(ActivateAccountSuccessScreen().eventTrackingScreenId, EventTrackingScreenId.undefined);
    });

    test('should self navigate on pushNamed', () async {
      ActivateAccountSuccessScreen.pushNamed();

      verify(() => mockNavigatorContext.pushNamed(Screen.activateAccountSuccessScreen.name))
          .called(1);
    });

    test('should self navigate on goNamed', () async {
      ActivateAccountSuccessScreen.goNamed();

      verify(() => mockNavigatorContext.goNamed(Screen.activateAccountSuccessScreen.name))
          .called(1);
    });

    testWidgets('should render correct UI', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: ActivateAccountSuccessScreen(),
      ));

      final ColoredBox coloredBox = tester.widget(find.byType(ColoredBox));
      expect(coloredBox.color, evoColors.primary);

      expect(find.byType(_FakeImage), findsOneWidget);
      verify(() => evoImageProvider.asset(
            EvoImages.imgLoginOnNewDevice,
            width: any(named: 'width'),
            height: any(named: 'height'),
            fit: any(named: 'fit'),
          )).called(1);

      expect(find.text(EvoStrings.activateAccountSuccessDesc), findsOneWidget);

      final Finder proceedBtn = find.byType(CommonButton);
      expect(proceedBtn, findsOneWidget);
      expect(find.descendant(of: proceedBtn, matching: find.text(EvoStrings.ctaProceed)),
          findsOneWidget);
    });

    testWidgets('should navigate to ActiveBiometricScreen when proceed button is tapped',
        (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: ActivateAccountSuccessScreen(),
      ));

      await tester.tap(find.byType(CommonButton));

      verify(
        () => mockNavigatorContext.pushNamed(
          Screen.activeBiometric.name,
          extra: any(named: 'extra'),
        ),
      ).called(1);
    });

    testWidgets('should navigate to ActiveBiometricScreen when popped',
        (WidgetTester tester) async {
      final GlobalKey<NavigatorState> navKey = GlobalKey<NavigatorState>();
      await tester.pumpWidget(MaterialApp(
        navigatorKey: navKey,
        home: ActivateAccountSuccessScreen(),
      ));

      navKey.currentState?.pop();

      verify(() => mockNavigatorContext.pushNamed(
            Screen.activeBiometric.name,
            extra: any(named: 'extra'),
          )).called(1);
    });

    testWidgets('should navigate to ActiveVirtualCardIntroScreen on call onSuccess',
        (WidgetTester tester) async {
      final GlobalKey<NavigatorState> navKey = GlobalKey<NavigatorState>();
      await tester.pumpWidget(MaterialApp(
        navigatorKey: navKey,
        home: ActivateAccountSuccessScreen(),
      ));

      navKey.currentState?.pop();

      final ActiveBiometricScreenArg arg = verify(() => mockNavigatorContext.pushNamed(
            Screen.activeBiometric.name,
            extra: captureAny(named: 'extra'),
          )).captured.first as ActiveBiometricScreenArg;

      arg.onSuccess();

      verify(() => mockNavigatorContext.pushNamed(
            Screen.activeVirtualCardIntroScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });
  });
}
