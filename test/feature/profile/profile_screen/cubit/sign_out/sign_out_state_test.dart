import 'package:evoapp/feature/profile/profile_screen/cubit//sign_out/sign_out_cubit.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('SignOutInitial should be an instance of SignOutState', () {
    final SignOutInitial state = SignOutInitial();
    expect(state, isA<SignOutState>());
  });

  test('SignOutLoading should be an instance of SignOutState', () {
    final SignOutLoading state = SignOutLoading();
    expect(state, isA<SignOutState>());
  });

  test('SignOutSuccess should be an instance of SignOutState', () {
    final SignOutSuccess state = SignOutSuccess();
    expect(state, isA<SignOutState>());
  });

  test('SignOutSuccess should contain the correct BaseEntity', () {
    final BaseEntity baseEntity = BaseEntity();
    final SignOutSuccess state = SignOutSuccess(baseEntity: baseEntity);
    expect(state.baseEntity, baseEntity);
  });

  test('SignOutFail should be an instance of SignOutState', () {
    final SignOutFail state = SignOutFail();
    expect(state, isA<SignOutState>());
  });

  test('SignOutFail should contain the correct ErrorUIModel', () {
    final ErrorUIModel error = ErrorUIModel();
    final SignOutFail state = SignOutFail(error: error);
    expect(state.error, error);
  });
}
