import 'package:evoapp/feature/profile/profile_screen/cubit/sign_out/mock_signout_file_name.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('Test MockSignOutUseCase', () {
    expect(MockSignOutUseCase.success.value, 'sign_out_success.json');
    expect(MockSignOutUseCase.failure.value, 'sign_out_failure.json');
  });

  test('Test getSignOutMockFileName', () {
    expect(getSignOutMockFileName(MockSignOutUseCase.success), 'sign_out_success.json');
    expect(getSignOutMockFileName(MockSignOutUseCase.failure), 'sign_out_failure.json');
  });
}
