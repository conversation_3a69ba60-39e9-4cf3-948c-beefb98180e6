import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/feature/profile/profile_screen/cubit/sign_out/sign_out_cubit.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../constant.dart';
import '../../../../../util/test_util.dart';

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

void main() {
  late SignOutCubit cubit;
  final AuthenticationRepo authenticationRepo = MockAuthenticationRepo();

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    getIt.registerLazySingleton<EvoUtilFunction>(() => MockEvoUtilFunction());
  });

  setUp(() {
    cubit = SignOutCubit(authenticationRepo);
  });

  blocTest<SignOutCubit, SignOutState>(
    'Sign out success',
    build: () => cubit,
    setUp: () async {
      when(() => authenticationRepo.logout(mockConfig: any(named: 'mockConfig')))
          .thenAnswer((_) async => BaseEntity.fromBaseResponse(BaseResponse(
                statusCode: CommonHttpClient.SUCCESS,
                response: await TestUtil.getResponseMock('sign_out_success.json'),
              )));
      when(() => evoUtilFunction.clearUserDataOnLogout()).thenAnswer((_) => Future<void>.value());
    },
    act: (SignOutCubit cubit) => cubit.signOut(),
    wait: TestConstant.blocEmitStateDelayDuration,
    expect: () => <dynamic>[
      isA<SignOutLoading>(),
      isA<SignOutSuccess>()
          .having(
            (SignOutSuccess state) => state.baseEntity?.userMessage,
            'verify user message',
            'sign out successfully',
          )
          .having(
            (SignOutSuccess state) => state.baseEntity?.message,
            'verify message',
            'sign out successfully',
          )
          .having(
            (SignOutSuccess state) => state.baseEntity?.statusCode,
            'verify statusCode',
            200,
          ),
    ],
    verify: (_) {
      verify(() => authenticationRepo.logout(mockConfig: any(named: 'mockConfig'))).called(1);
      verify(() => evoUtilFunction.clearUserDataOnLogout()).called(1);
    },
  );

  blocTest<SignOutCubit, SignOutState>(
    'Sign out failed',
    build: () => cubit,
    setUp: () async {
      when(() => authenticationRepo.logout(mockConfig: any(named: 'mockConfig')))
          .thenAnswer((_) async => BaseEntity.fromBaseResponse(BaseResponse(
                statusCode: CommonHttpClient.UNKNOWN_ERRORS,
                response: await TestUtil.getResponseMock('sign_out_failure.json'),
              )));
    },
    act: (SignOutCubit cubit) => cubit.signOut(),
    wait: TestConstant.blocEmitStateDelayDuration,
    expect: () => <dynamic>[
      isA<SignOutLoading>(),
      isA<SignOutFail>()
          .having(
            (SignOutFail state) => state.error?.userMessage,
            'verify error message',
            'sign out failure',
          )
          .having(
            (SignOutFail state) => state.error?.statusCode,
            'verify statusCode',
            500,
          ),
    ],
    verify: (_) {
      verify(() => authenticationRepo.logout(mockConfig: any(named: 'mockConfig'))).called(1);
      verifyNever(() => evoUtilFunction.clearUserDataOnLogout());
    },
  );
}
