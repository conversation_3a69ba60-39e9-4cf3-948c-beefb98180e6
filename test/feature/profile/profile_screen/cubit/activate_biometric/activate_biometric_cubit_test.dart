import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/biometric/biometric_token_module/biometrics_token_module.dart';
import 'package:evoapp/feature/biometric/model/biometric_status_change_notifier.dart';
import 'package:evoapp/feature/biometric/utils/bio_auth_result.dart';
import 'package:evoapp/feature/biometric/utils/biometric_functions.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/feature/profile/profile_screen/cubit/activate_biometric/activate_biometric_cubit.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../constant.dart';

class BiometricsAuthenticateMock extends Mock implements BiometricsAuthenticate {}

class EvoLocalStorageHelperMock extends Mock implements EvoLocalStorageHelper {}

class MockBiometricStatusChangeNotifier extends Mock implements BiometricStatusChangeNotifier {}

class MockBiometricFunction extends Mock implements BiometricFunctions {}

final BiometricStatusChangeNotifier mockBioStatusNotifier = MockBiometricStatusChangeNotifier();

class MockAppState extends Mock implements AppState {
  @override
  BiometricStatusChangeNotifier biometricStatusChangeNotifier = mockBioStatusNotifier;
}

class MockBiometricsTokenModule extends Mock implements BiometricsTokenModule {}

void main() {
  late BiometricsAuthenticateMock biometricsAuthenticateMock;
  late EvoLocalStorageHelperMock mockStorage;
  late BiometricsTokenModule mockBioToken;
  late ActivateBiometricCubit cubit;
  late AppState mockAppState;

  setUpAll(() {
    getIt.registerLazySingleton<BiometricsTokenModule>(() => MockBiometricsTokenModule());

    mockBioToken = getIt.get<BiometricsTokenModule>();

    registerFallbackValue(BiometricStatus.notSetup);
    when(() => mockBioStatusNotifier.value).thenReturn(BiometricStatus.notSetup);
  });

  setUp(() {
    biometricsAuthenticateMock = BiometricsAuthenticateMock();
    mockStorage = EvoLocalStorageHelperMock();

    mockAppState = MockAppState();

    getIt.registerSingleton<AppState>(mockAppState);

    cubit = ActivateBiometricCubit(
      bioAuth: biometricsAuthenticateMock,
      secureStorageHelper: mockStorage,
      appState: mockAppState,
    );

    when(() => mockBioToken.isEnableBiometricAuthenticator()).thenAnswer((_) async {
      return true;
    });

    when(() => mockBioToken.isBiometricTokenUsable()).thenAnswer((_) async {
      return true;
    });

    when(() => mockStorage.isEnableBiometricAuthenticator()).thenAnswer((_) async {
      return true;
    });
  });

  tearDown(() {
    getIt.unregister<AppState>();
  });

  test('init state', () {
    expect(cubit.state, isA<ActivateBiometricInitState>());
  });

  group('verify initialize method', () {
    blocTest<ActivateBiometricCubit, ActivateBiometricState>(
        'should emit [BiometricUnsupported] when bioAuth.isDeviceSupportBiometrics return false',
        setUp: () {
          when(() => biometricsAuthenticateMock.isDeviceSupportBiometrics()).thenAnswer((_) async {
            return false;
          });
        },
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (_) => cubit.initialize(),
        expect: () => <TypeMatcher<BiometricUnsupported>>[
              isA<BiometricUnsupported>(),
            ],
        verify: (_) {
          verify(() => mockBioStatusNotifier.addListener(cubit.listenOnBiometricStatusChanged))
              .called(1);
        });

    blocTest<ActivateBiometricCubit, ActivateBiometricState>(
        'should emit BiometricSupported when bioAuth.isDeviceSupportBiometrics return true',
        setUp: () {
          when(() => biometricsAuthenticateMock.isDeviceSupportBiometrics()).thenAnswer((_) async {
            return true;
          });
        },
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (_) => cubit.initialize(),
        expect: () => <TypeMatcher<BiometricSupported>>[
              isA<BiometricSupported>(),
            ]);
  });

  group('verify checkBioAuthState method', () {
    blocTest<ActivateBiometricCubit, ActivateBiometricState>(
        'should emit [BiometricActivated] when [isBiometricTokenUnUsable] is false and [isEnableBiometricAuth] is true',
        build: () => cubit,
        act: (_) => cubit.checkBioAuthState(),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <TypeMatcher<BiometricActivated>>[
              isA<BiometricActivated>(),
            ]);

    blocTest<ActivateBiometricCubit, ActivateBiometricState>(
        'should emit [BiometricDeactivated] when [isBiometricTokenUnUsable] is true',
        setUp: () {
          when(() => mockBioToken.isBiometricTokenUsable()).thenAnswer((_) async {
            return false;
          });
          when(() => mockBioToken.disableBiometricAuthenticatorFeature()).thenAnswer((_) async {});

          when(() => mockAppState.biometricStatusChangeNotifier.value = any())
              .thenReturn(BiometricStatus.notSetup);
        },
        build: () => cubit,
        act: (_) => cubit.checkBioAuthState(),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => [
              isA<BiometricDeactivated>(),
            ],
        verify: (_) {
          verify(() => mockBioToken.disableBiometricAuthenticatorFeature()).called(1);

          final BiometricStatus captured =
              verify(() => mockAppState.biometricStatusChangeNotifier.update(captureAny()))
                  .captured
                  .first as BiometricStatus;

          expect(captured, BiometricStatus.biometricTokenUnusable);
        });

    blocTest<ActivateBiometricCubit, ActivateBiometricState>(
        'should emit [BiometricDeactivated] when [isEnableBiometricAuth] is false',
        setUp: () {
          when(() => mockStorage.isEnableBiometricAuthenticator()).thenAnswer((_) async {
            return false;
          });
        },
        build: () => cubit,
        act: (_) => cubit.checkBioAuthState(),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => [
              isA<BiometricDeactivated>(),
            ],
        verify: (_) {});
  });

  group('verify toggleBioAuth method', () {
    late BiometricFunctions mockBioFunction;

    setUpAll(() {
      mockBioFunction = MockBiometricFunction();
      getIt.registerSingleton<BiometricFunctions>(mockBioFunction);
    });

    setUp(() {
      when(() => mockBioToken.hasEnrolledBiometrics()).thenAnswer((_) async {
        return true;
      });
    });

    tearDown(() {
      reset(mockBioFunction);
    });

    blocTest<ActivateBiometricCubit, ActivateBiometricState>(
        setUp: () {
          when(() => mockBioToken.hasEnrolledBiometrics()).thenAnswer((_) async {
            return true;
          });
          when(() => mockBioToken.enable(callback: any(named: 'callback')))
              .thenAnswer((_) async {});
        },
        'should call biometricsTokenModule.enable when hasEnrolledBiometrics is true',
        build: () => cubit,
        act: (_) => cubit.toggleBioAuth(true),
        wait: TestConstant.blocEmitStateDelayDuration,
        verify: (_) {
          verify(() => mockBioToken.enable(callback: cubit)).called(1);
        });

    blocTest<ActivateBiometricCubit, ActivateBiometricState>(
        setUp: () {
          when(() => mockBioToken.disableBiometricAuthenticatorFeature()).thenAnswer((_) async {});
        },
        'should call biometricsTokenModule.disableBiometricAuthenticatorFeature when hasEnrolledBiometrics is true',
        build: () => cubit,
        act: (_) => cubit.toggleBioAuth(false),
        wait: TestConstant.blocEmitStateDelayDuration,
        verify: (_) {
          verifyNever(() => mockBioToken.enable(callback: any(named: 'callback')));
          verify(() => mockBioToken.disableBiometricAuthenticatorFeature()).called(1);
        });

    blocTest<ActivateBiometricCubit, ActivateBiometricState>(
        setUp: () {
          when(() => mockBioToken.hasEnrolledBiometrics()).thenAnswer((_) async {
            return false;
          });

          when(() => mockBioFunction.handleBioError(bioError: any(named: 'bioError')))
              .thenAnswer((_) async {});
        },
        'should call biometricFunctions.handleBioError when de-active hasEnrolledBiometrics is false',
        build: () => cubit,
        act: (_) => cubit.toggleBioAuth(false),
        wait: TestConstant.blocEmitStateDelayDuration,
        verify: (_) {
          final BioAuthError captured =
              verify(() => mockBioFunction.handleBioError(bioError: captureAny(named: 'bioError')))
                  .captured
                  .first as BioAuthError;

          expect(captured, BioAuthError.notEnrolled);
        });
  });

  blocTest<ActivateBiometricCubit, ActivateBiometricState>(
      'verify close method should removeListener',
      setUp: () {
        reset(mockBioStatusNotifier);
      },
      build: () => cubit,
      act: (_) => cubit.close(),
      wait: TestConstant.blocEmitStateDelayDuration,
      verify: (_) {
        verify(() => mockBioStatusNotifier.removeListener(cubit.listenOnBiometricStatusChanged))
            .called(isNonZero);
      });

  blocTest<ActivateBiometricCubit, ActivateBiometricState>('verify onRetrieveTokenSuccess method',
      build: () => cubit,
      act: (_) => cubit.onRetrieveTokenSuccess(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => [
            isA<BiometricActivated>(),
            isA<RetrieveBiometricTokenSuccess>(),
          ]);

  group('verify onRetrieveTokenError method', () {
    const BioAuthError bioAuthError = BioAuthError.notEnrolled;
    const String mockErrorMessage = 'mock-error-message';

    blocTest<ActivateBiometricCubit, ActivateBiometricState>(
        'should emit RetrieveBiometricTokenBioAuthFailure when type is BiometricTokenModuleErrorType.biometrics',
        build: () => cubit,
        act: (_) => cubit.onRetrieveTokenError(
              type: BiometricTokenModuleErrorType.biometrics,
              bioError: bioAuthError,
            ),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => [
              isA<RetrieveBiometricTokenBioAuthFailure>().having(
                  (RetrieveBiometricTokenBioAuthFailure state) => state.error,
                  'verify bioError',
                  bioAuthError),
            ]);

    blocTest<ActivateBiometricCubit, ActivateBiometricState>(
        'should emit RetrieveBiometricTokenFailure when type is BiometricTokenModuleErrorType.apiError',
        build: () => cubit,
        act: (_) => cubit.onRetrieveTokenError(
              type: BiometricTokenModuleErrorType.apiError,
              error: ErrorUIModel(userMessage: mockErrorMessage),
            ),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <TypeMatcher<RetrieveBiometricTokenFailure>>[
              isA<RetrieveBiometricTokenFailure>().having(
                  (RetrieveBiometricTokenFailure state) => state.errorMessage,
                  'verify errorMessage',
                  mockErrorMessage),
            ]);

    blocTest<ActivateBiometricCubit, ActivateBiometricState>(
        'should emit RetrieveBiometricTokenFailure when type unknown',
        build: () => cubit,
        act: (_) => cubit.onRetrieveTokenError(
              type: BiometricTokenModuleErrorType.unknown,
              userMessage: mockErrorMessage,
            ),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <TypeMatcher<RetrieveBiometricTokenFailure>>[
              isA<RetrieveBiometricTokenFailure>().having(
                  (RetrieveBiometricTokenFailure state) => state.errorMessage,
                  'verify errorMessage',
                  mockErrorMessage),
            ]);
  });
}
