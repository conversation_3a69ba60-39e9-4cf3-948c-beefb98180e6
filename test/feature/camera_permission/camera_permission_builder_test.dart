import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/base/evo_page_state_base.dart';
import 'package:evoapp/feature/biometric/biometric_token_module/biometrics_token_module.dart';
import 'package:evoapp/feature/camera_permission/camera_permission_builder.dart';
import 'package:evoapp/feature/camera_permission/cubit/camera_permission_cubit.dart';
import 'package:evoapp/feature/camera_permission/cubit/camera_permission_state.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/dialog_functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../base/evo_page_state_base_test_config.dart';

class MockCameraPermissionCubit extends MockCubit<CameraPermissionState>
    implements CameraPermissionCubit {}

class MockDialogFunction extends Mock implements DialogFunction {}

final GlobalKey<State<StatefulWidget>> dialogKey = GlobalKey();

class TestCameraPermissionBuilder extends CameraPermissionBuilder {
  const TestCameraPermissionBuilder({required super.builder, super.key});

  @override
  EvoPageStateBase<PageBase> createState() => TestCameraPermissionBuilderState();
}

class TestCameraPermissionBuilderState extends CameraPermissionBuilderState {
  @override
  GlobalKey<State<StatefulWidget>> get cameraDialogKey => dialogKey;

  @override
  bool isTopVisible() {
    /// mock for page visible, enable for `onPagePaused` check
    return true;
  }
}

void main() {
  late CameraPermissionCubit mockCubit;

  setUpAll(() {
    initConfigEvoPageStateBase();
    setUpMockConfigEvoPageStateBase();

    registerFallbackValue(EvoDialogId.common);
  });

  tearDownAll(() {
    getIt.reset();
  });

  setUp(() {
    mockCubit = MockCameraPermissionCubit();
    when(() => mockCubit.state).thenReturn(CameraPermissionInitial());

    when(() => mockDialogFunction.showDialogConfirm(
          key: any(named: 'key'),
          dialogId: any(named: 'dialogId'),
          title: any(named: 'title'),
          content: any(named: 'content'),
          textPositive: any(named: 'textPositive'),
          textNegative: any(named: 'textNegative'),
          isDismissible: any(named: 'isDismissible'),
          onClickPositive: any(named: 'onClickPositive'),
          onClickNegative: any(named: 'onClickNegative'),
        )).thenAnswer((_) async {});
  });

  buildWidget(WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
          home: BlocProvider<CameraPermissionCubit>.value(
        value: mockCubit,
        child: TestCameraPermissionBuilder(
            builder: (BuildContext context, CameraPermissionState state) {
          return switch (state) {
            CameraPermissionInitial() => const Text('init'),
            CameraPermissionGranted() => const Text('granted'),
            CameraPermissionDenied() => const Text('denied'),
          };
        }),
      )),
    );
  }

  group('verify build function', () {
    testWidgets('should returned widget correctly with CameraPermissionInitial state',
        (WidgetTester tester) async {
      await buildWidget(tester);

      verify(() => mockCubit.requestPermission()).called(1);

      expect(find.text('init'), findsOne);
    });

    testWidgets('should returned widget correctly with CameraPermissionGranted state',
        (WidgetTester tester) async {
      when(() => mockCubit.state).thenReturn(CameraPermissionGranted());

      await buildWidget(tester);

      expect(find.text('granted'), findsOne);
    });

    testWidgets('should returned widget correctly with CameraPermissionDenied state',
        (WidgetTester tester) async {
      whenListen(
          mockCubit,
          Stream<CameraPermissionState>.fromIterable([
            CameraPermissionInitial(),
            CameraPermissionDenied(),
          ]),
          initialState: CameraPermissionInitial());

      await buildWidget(tester);
      await tester.pumpAndSettle();

      expect(find.text('denied'), findsOne);
      verify(() => mockDialogFunction.showDialogConfirm(
            key: dialogKey,
            dialogId: EvoDialogId.openDeviceSecuritySettingDialog,
            title: EvoStrings.cameraPermissionTitle,
            content: EvoStrings.cameraPermissionDesc,
            textPositive: EvoStrings.settingTitle,
            textNegative: EvoStrings.ignoreTitle,
            isDismissible: false,
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
          )).called(1);
    });
  });

  group('verify lifecycle handling ', () {
    testWidgets('should called cubit.resetIfPermissionDenied() when widget is onPagePaused() ',
        (WidgetTester tester) async {
      await buildWidget(tester);

      WidgetsBinding.instance.handleAppLifecycleStateChanged(AppLifecycleState.paused);
      await tester.pumpAndSettle();

      verify(() => mockCubit.resetIfPermissionDenied()).called(1);
    });

    testWidgets('should called cubit.requestPermissionOnResume() when widget is onPageResumed() ',
        (WidgetTester tester) async {
      /// stub biometric checking on onResumed()
      final BiometricsTokenModule biometricTokenModule = getIt.get<BiometricsTokenModule>();
      when(() => biometricTokenModule.isEnableBiometricAuthenticator())
          .thenAnswer((_) async => false);

      await buildWidget(tester);

      WidgetsBinding.instance.handleAppLifecycleStateChanged(AppLifecycleState.resumed);
      await tester.pumpAndSettle();

      verify(() => mockCubit.requestPermissionOnResume()).called(1);
    });
  });
}
