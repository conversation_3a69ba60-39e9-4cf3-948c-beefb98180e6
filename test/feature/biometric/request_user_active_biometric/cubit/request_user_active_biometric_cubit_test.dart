import 'package:evoapp/feature/biometric/biometric_token_module/biometrics_token_module.dart';
import 'package:evoapp/feature/biometric/model/biometric_status_change_notifier.dart';
import 'package:evoapp/feature/biometric/request_user_active_biometric/cubit/request_user_active_biometric_cubit.dart';
import 'package:evoapp/feature/biometric/request_user_active_biometric/request_user_active_biometric_handler.dart';
import 'package:evoapp/feature/biometric/request_user_active_biometric/request_user_active_biometric_handler_impl.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:mocktail/mocktail.dart';

class MockAppState extends Mock implements AppState {}

class MockRequestUserActivateBiometricHandler extends Mock
    implements RequestUserActivateBiometricHandler {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockBiometricsTokenModule extends Mock implements BiometricsTokenModule {}

/// Spy class for [RequestUserActiveBiometricCubit] is used to count the number of times the methods are called.
/// It help to saving time to create mock class for each method from [BiometricChangeMixin], [BiometricTokenUsabilityMixin]
class SpyRequestUserActiveBiometricCubit extends RequestUserActiveBiometricCubit {
  SpyRequestUserActiveBiometricCubit({
    required super.appState,
    required super.requestUserActiveBiometricHandler,
    required super.evoLocalStorageHelper,
  });

  int numberOfShowBiometricChangedPopupAndUpdateStatus = 0;
  int numberOfShowBiometricTokenUnUsableToastAndUpdateStatus = 0;

  @override
  Future<void> showBiometricChangedPopupAndUpdateStatus() async {
    numberOfShowBiometricChangedPopupAndUpdateStatus++;
  }

  @override
  Future<void> showBiometricTokenUnUsableToastAndUpdateStatus() async {
    numberOfShowBiometricTokenUnUsableToastAndUpdateStatus++;
  }

  void reset() {
    numberOfShowBiometricChangedPopupAndUpdateStatus = 0;
    numberOfShowBiometricTokenUnUsableToastAndUpdateStatus = 0;
  }
}

void main() {
  late SpyRequestUserActiveBiometricCubit cubit;
  late AppState appState;
  late MockRequestUserActivateBiometricHandler mockRequestUserActivateBiometricHandler;
  late MockEvoLocalStorageHelper mockEvoLocalStorageHelper;
  late MockBiometricsTokenModule mockBiometricsTokenModule;

  setUpAll(() {
    appState = AppState();
    mockRequestUserActivateBiometricHandler = MockRequestUserActivateBiometricHandler();
    mockEvoLocalStorageHelper = MockEvoLocalStorageHelper();
    mockBiometricsTokenModule = MockBiometricsTokenModule();

    getIt.registerSingleton<AppState>(appState);
    getIt.registerSingleton<BiometricsTokenModule>(mockBiometricsTokenModule);
  });

  setUp(() {
    cubit = SpyRequestUserActiveBiometricCubit(
      appState: appState,
      requestUserActiveBiometricHandler: mockRequestUserActivateBiometricHandler,
      evoLocalStorageHelper: mockEvoLocalStorageHelper,
    );
  });

  tearDown(() {
    cubit.reset();
    reset(mockRequestUserActivateBiometricHandler);
    reset(mockEvoLocalStorageHelper);
  });

  blocTest<RequestUserActiveBiometricCubit, RequestUserActiveBiometricState>(
    'emits [RequestUserActiveBiometricCompletedState] when requestActiveBiometricIfNeed is called and status is not inProgress',
    setUp: () {
      when(() => mockRequestUserActivateBiometricHandler.start(useCase: any(named: 'useCase')))
          .thenAnswer((_) async => RequestUserActivateBiometricStatus.success);
      when(() => mockEvoLocalStorageHelper.setNewDevice(false))
          .thenAnswer((_) async => Future<void>.value());
      appState.biometricStatusChangeNotifier.update(BiometricStatus.usable);
    },
    build: () {
      return cubit;
    },
    act: (RequestUserActiveBiometricCubit cubit) => cubit.requestActiveBiometricIfNeed(),
    expect: () => <dynamic>[
      isA<RequestUserActiveBiometricCompletedState>().having(
        (RequestUserActiveBiometricCompletedState state) => state.status,
        'verify status is BiometricStatus.usable',
        BiometricStatus.usable,
      ),
    ],
    verify: (_) {
      verify(() => mockEvoLocalStorageHelper.setNewDevice(false)).called(1);
      expect(cubit.numberOfShowBiometricTokenUnUsableToastAndUpdateStatus, 0);
      expect(cubit.numberOfShowBiometricChangedPopupAndUpdateStatus, 0);
    },
  );

  blocTest<RequestUserActiveBiometricCubit, RequestUserActiveBiometricState>(
    'emits [RequestUserActiveBiometricCompletedState] when biometricStatus is BiometricStatus.deviceSettingChanged',
    setUp: () {
      when(() => mockRequestUserActivateBiometricHandler.start(useCase: any(named: 'useCase')))
          .thenAnswer((_) async => RequestUserActivateBiometricStatus.success);
      when(() => mockEvoLocalStorageHelper.setNewDevice(false))
          .thenAnswer((_) async => Future<void>.value());
      appState.biometricStatusChangeNotifier.update(BiometricStatus.deviceSettingChanged);
    },
    build: () {
      return cubit;
    },
    act: (RequestUserActiveBiometricCubit cubit) => cubit.requestActiveBiometricIfNeed(),
    expect: () => <dynamic>[
      isA<RequestUserActiveBiometricCompletedState>().having(
        (RequestUserActiveBiometricCompletedState state) => state.status,
        'verify status is BiometricStatus.deviceSettingChanged',
        BiometricStatus.deviceSettingChanged,
      ),
    ],
    verify: (_) {
      expect(cubit.numberOfShowBiometricChangedPopupAndUpdateStatus, 1);
      expect(cubit.numberOfShowBiometricTokenUnUsableToastAndUpdateStatus, 0);
      verify(() => mockEvoLocalStorageHelper.setNewDevice(false)).called(1);
    },
  );

  blocTest<RequestUserActiveBiometricCubit, RequestUserActiveBiometricState>(
    'emits [RequestUserActiveBiometricCompletedState] when biometricStatus is BiometricStatus.biometricTokenUnusable',
    setUp: () {
      when(() => mockRequestUserActivateBiometricHandler.start(useCase: any(named: 'useCase')))
          .thenAnswer((_) async => RequestUserActivateBiometricStatus.success);
      when(() => mockEvoLocalStorageHelper.setNewDevice(false))
          .thenAnswer((_) async => Future<void>.value());
      appState.biometricStatusChangeNotifier.update(BiometricStatus.biometricTokenUnusable);
    },
    build: () {
      return cubit;
    },
    act: (RequestUserActiveBiometricCubit cubit) => cubit.requestActiveBiometricIfNeed(),
    expect: () => <dynamic>[
      isA<RequestUserActiveBiometricCompletedState>().having(
        (RequestUserActiveBiometricCompletedState state) => state.status,
        'verify status is BiometricStatus.biometricTokenUnusable',
        BiometricStatus.biometricTokenUnusable,
      ),
    ],
    verify: (_) {
      expect(cubit.numberOfShowBiometricTokenUnUsableToastAndUpdateStatus, 1);
      expect(cubit.numberOfShowBiometricChangedPopupAndUpdateStatus, 0);
      verify(() => mockEvoLocalStorageHelper.setNewDevice(false)).called(1);
    },
  );

  blocTest<RequestUserActiveBiometricCubit, RequestUserActiveBiometricState>(
    'does not emit new state when requestActiveBiometricIfNeed is called and status is inProgress',
    setUp: () {
      when(() => mockRequestUserActivateBiometricHandler.start(useCase: any(named: 'useCase')))
          .thenAnswer((_) async => RequestUserActivateBiometricStatus.inProgress);
      when(() => mockEvoLocalStorageHelper.setNewDevice(false))
          .thenAnswer((_) async => Future<void>.value());
      appState.biometricStatusChangeNotifier.update(BiometricStatus.notSetup);
    },
    build: () {
      return cubit;
    },
    act: (RequestUserActiveBiometricCubit cubit) => cubit.requestActiveBiometricIfNeed(),
    expect: () => <dynamic>[],
    verify: (_) {
      verifyNever(() => mockEvoLocalStorageHelper.setNewDevice(false));
      expect(cubit.appState.biometricStatusChangeNotifier.value, BiometricStatus.notSetup);
    },
  );
}
