// test/request_user_active_biometric_state_test.dart
import 'package:evoapp/feature/biometric/model/biometric_status_change_notifier.dart';
import 'package:evoapp/feature/biometric/request_user_active_biometric/cubit/request_user_active_biometric_cubit.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('RequestUserActiveBiometricState', () {
    test('RequestUserActiveBiometricInitial should be instantiated', () {
      final RequestUserActiveBiometricState state = RequestUserActiveBiometricInitial();
      expect(state, isA<RequestUserActiveBiometricInitial>());
    });

    test('RequestUserActiveBiometricCompletedState should be instantiated with status', () {
      const BiometricStatus status = BiometricStatus.notSetup; // Replace with an actual BiometricStatus value
      final RequestUserActiveBiometricCompletedState state = RequestUserActiveBiometricCompletedState(status: status);
      expect(state, isA<RequestUserActiveBiometricCompletedState>());
      expect(state.status, status);
    });
  });
}