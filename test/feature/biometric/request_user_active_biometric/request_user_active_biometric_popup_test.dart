import 'package:evoapp/data/repository/user_repo.dart';
import 'package:evoapp/data/repository/user_repo_impl.dart';
import 'package:evoapp/feature/biometric/biometric_token_module/biometrics_token_module.dart';
import 'package:evoapp/feature/biometric/model/biometric_ui_model.dart';
import 'package:evoapp/feature/biometric/request_user_active_biometric/request_user_active_biometric_handler_impl.dart';
import 'package:evoapp/feature/biometric/request_user_active_biometric/request_user_active_biometric_popup.dart';
import 'package:evoapp/feature/biometric/utils/bio_auth_result.dart';
import 'package:evoapp/feature/biometric/utils/biometric_functions.dart';
import 'package:evoapp/feature/biometric/utils/biometric_type_helper.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate_impl.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/dialog_functions.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper_impl.dart';
import 'package:evoapp/util/token_utils/evo_jwt_helper_impl.dart';
import 'package:evoapp/util/token_utils/jwt_helper.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/share_preference_helper.dart';
import 'package:flutter_common_package/util/shared_preferences_impl.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../data/repository/mock_common_http_client.dart';

class MockAppState extends Mock implements AppState {}

class MockEvoSecureStorageHelperImpl extends Mock implements EvoSecureStorageHelperImpl {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockBiometricsTokenModule extends Mock implements BiometricsTokenModule {}

class MockBiometricTypeHelper extends Mock implements BiometricTypeHelper {}

class MockBiometricAuthenticateImpl extends Mock implements BiometricAuthenticateImpl {}

class MockEvoImageProvider extends Mock implements CommonImageProvider {}

class MockBiometricUtilityFunctions extends Mock implements BiometricFunctions {}

class MockDialogFunctions extends Mock implements DialogFunction {}

class MockEvoSnackBar extends Mock implements EvoSnackBar {}

void main() {
  late AppState mockAppState;
  late EvoSecureStorageHelperImpl mockEvoSecureStorage;
  late BiometricsAuthenticate mockBiometricsAuthenticate;
  late BiometricsTokenModule mockBiometricsTokenModule;
  late RequestUserActiveBiometricPopup activeBiometricPopup;
  late EvoSnackBar mockEvoSnackBar;
  late CommonImageProvider mockCommonImageProvider;
  late BiometricFunctions mockBiometricUtilityFunctions;
  late DialogFunction mockDialogFunction;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    getIt.registerLazySingleton<AppState>(() => MockAppState());
    mockAppState = getIt.get<AppState>();

    registerFallbackValue(EvoDialogId.openDeviceSecuritySettingDialog);
    registerFallbackValue(SnackBarType.error);
    registerFallbackValue(SnackBarDuration.short.value);

    getIt.registerLazySingleton<BiometricFunctions>(() => MockBiometricUtilityFunctions());
    mockBiometricUtilityFunctions = getIt.get<BiometricFunctions>();

    getIt.registerLazySingleton<CommonSharedPreferencesHelper>(
        () => CommonSharedPreferencesHelperImpl());

    getIt.registerLazySingleton<EvoSecureStorageHelperImpl>(() => MockEvoSecureStorageHelperImpl());
    mockEvoSecureStorage = getIt.get<EvoSecureStorageHelperImpl>();

    getIt.registerLazySingleton<EvoLocalStorageHelper>(() => MockEvoLocalStorageHelper());

    getIt.registerLazySingleton<UserRepo>(
        () => UserRepoImpl(MockCommonHttpClient(), mockEvoSecureStorage, mockAppState));

    getIt.registerLazySingleton<JwtHelper>(() => EvoJwtHelperImpl());

    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());

    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    getIt.registerLazySingleton<EvoColors>(() => EvoColors());

    // register Biometrics
    getIt.registerLazySingleton<BiometricsAuthenticate>(() => MockBiometricAuthenticateImpl());
    mockBiometricsAuthenticate = getIt.get<BiometricsAuthenticate>();

    getIt.registerLazySingleton<BiometricsTokenModule>(() => MockBiometricsTokenModule());
    mockBiometricsTokenModule = getIt.get<BiometricsTokenModule>();

    getIt.registerLazySingleton<BiometricTypeHelper>(() => MockBiometricTypeHelper());

    getIt.registerLazySingleton<EvoSnackBar>(() => MockEvoSnackBar());
    mockEvoSnackBar = getIt.get<EvoSnackBar>();

    getIt.registerLazySingleton<CommonImageProvider>(() => MockEvoImageProvider());
    mockCommonImageProvider = getIt.get<CommonImageProvider>();

    getIt.registerLazySingleton<DialogFunction>(() => MockDialogFunctions());
    mockDialogFunction = getIt.get<DialogFunction>();

    when(() => mockCommonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          fit: any(named: 'fit'),
        )).thenReturn(const SizedBox());

    getIt.registerSingleton(GlobalKeyProvider());
  });

  setUp(() {
    when(() => mockDialogFunction.showDialogConfirm(
          dialogId: any(named: 'dialogId'),
          textPositive: any(named: 'textPositive'),
          content: any(named: 'content'),
          title: any(named: 'title'),
          textNegative: any(named: 'textNegative'),
          footer: any(named: 'footer'),
          onClickNegative: any(named: 'onClickNegative'),
          onClickPositive: any(named: 'onClickPositive'),
          imageHeader: any(named: 'imageHeader'),
          isDismissible: any(named: 'isDismissible'),
          positiveButtonStyle: any(named: 'positiveButtonStyle'),
          negativeButtonStyle: any(named: 'negativeButtonStyle'),
          titleTextStyle: any(named: 'titleTextStyle'),
          contentTextStyle: any(named: 'contentTextStyle'),
          isShowButtonClose: any(named: 'isShowButtonClose'),
          loggingEventMetaData: any(named: 'loggingEventMetaData'),
          loggingEventOnShowMetaData: any(named: 'loggingEventOnShowMetaData'),
          dialogHorizontalPadding: any(named: 'dialogHorizontalPadding'),
        )).thenAnswer((_) => Future<void>.value());

    when(() => mockBiometricUtilityFunctions.handleBioError(bioError: any(named: 'bioError')))
        .thenAnswer((_) => Future<void>.value());
    activeBiometricPopup = RequestUserActiveBiometricPopup();
  });

  tearDown(() {
    reset(evoDialogFunction);
    reset(mockBiometricUtilityFunctions);
    reset(mockAppState);
  });

  tearDownAll(() async {
    await getIt.reset();
  });

  void verifyDialogUI() {
    verify(() => mockDialogFunction.showDialogConfirm(
          dialogId: EvoDialogId.requestEnableActiveBiometricBottomSheet,
          content: EvoStrings.activeBiometricDesc,
          title: EvoStrings.activeBiometricTitle,
          textPositive: EvoStrings.ctaActive,
          textNegative: EvoStrings.ctaSkip,
          dialogHorizontalPadding: 56.75,
          imageHeader:
              mockCommonImageProvider.asset(EvoImages.imgEnableBiometric, fit: BoxFit.fitWidth),
          onClickNegative: any(named: 'onClickNegative'),
          onClickPositive: any(named: 'onClickPositive'),
        )).called(1);
  }

  group('verify method showDialogActiveBiometric()', () {
    late BiometricTypeUIModel model;

    setUp(() {
      model = BiometricTypeUIModel.faceAndFinger();
      when(() => mockAppState.bioTypeInfo).thenReturn(model);
    });

    testWidgets('verify showDialogActiveBiometric() with return value',
        (WidgetTester widgetTester) async {
      final RequestUserActivateBiometricStatus result =
          await activeBiometricPopup.showDialogActiveBiometric();

      expect(result, RequestUserActivateBiometricStatus.fail);
      expect(activeBiometricPopup.isTappedActivateBiometric, false);

      verifyDialogUI();
    });
  });

  group('verify method show()', () {
    late BiometricTypeUIModel model;

    setUp(() {
      model = BiometricTypeUIModel.faceAndFinger();
      when(() => mockAppState.bioTypeInfo).thenReturn(model);
    });

    testWidgets('verify method show() that the user taps active button',
        (WidgetTester widgetTester) async {
      activeBiometricPopup.isTappedActivateBiometric = true;

      await activeBiometricPopup.show();

      verifyDialogUI();
    });

    testWidgets('verify method show() that the user does not tap active button',
        (WidgetTester widgetTester) async {
      activeBiometricPopup.isTappedActivateBiometric = false;

      await activeBiometricPopup.show();

      verifyDialogUI();
    });
  });

  group('verify method onTapActive()', () {
    test('verify method onTapActive(), hasEnrolledBiometric = true', () async {
      when(() => mockBiometricsAuthenticate.hasEnrolledBiometric())
          .thenAnswer((_) async => Future<bool>.value(true));

      when(() => mockBiometricsTokenModule.enable(
            callback: any(named: 'callback'),
          )).thenAnswer((_) async => Future<void>.value());

      await activeBiometricPopup.onTapActive();

      verify(() => mockBiometricsAuthenticate.hasEnrolledBiometric()).called(1);
      verify(() => mockBiometricsTokenModule.enable(
            callback: any(named: 'callback'),
          )).called(1);

      expect(activeBiometricPopup.isTappedActivateBiometric, true);
    });

    test('verify method onTapActive(), hasEnrolledBiometric = false', () async {
      when(() => mockBiometricsAuthenticate.hasEnrolledBiometric())
          .thenAnswer((_) async => Future<bool>.value(false));

      when(() => mockAppState.bioTypeInfo).thenReturn(BiometricTypeUIModel.faceAndFinger());

      await activeBiometricPopup.onTapActive();

      verify(() => mockBiometricsAuthenticate.hasEnrolledBiometric()).called(1);
      final VerificationResult verification = verify(() =>
          mockBiometricUtilityFunctions.handleBioError(bioError: captureAny(named: 'bioError')));

      verification.called(1);
      expect(verification.captured.firstOrNull, BioAuthError.notEnrolled);
      expect(activeBiometricPopup.isTappedActivateBiometric, true);
    });
  });

  group('verify onSuccess() method', () {
    test('onSuccess shows success message', () {
      final BiometricTypeUIModel mockBioTypeInfo = BiometricTypeUIModel.faceAndFinger();
      when(() => mockAppState.bioTypeInfo).thenReturn(mockBioTypeInfo);

      when(() => mockEvoSnackBar.show(
            any(),
            durationInMilliSec: any(named: 'durationInMilliSec'),
          )).thenAnswer((_) {
        return Future<bool?>.value(true);
      });

      activeBiometricPopup.onRetrieveTokenSuccess();

      final String expectedMessage =
          '${EvoStrings.enableText} ${EvoStrings.authenticateText.toLowerCase()} ${mockBioTypeInfo.biometricTypeName}';

      verify(() => mockEvoSnackBar.show(expectedMessage,
          durationInMilliSec: SnackBarDuration.short.value)).called(1);
    });
  });

  group('verify onError() method', () {
    setUp(() {
      when(() => mockBiometricUtilityFunctions.handleBioError(bioError: any(named: 'bioError')))
          .thenAnswer((_) async {
        return Future<void>.value();
      });
    });

    test('onError handles bio error with BiometricTokenModuleErrorType.biometrics', () async {
      const BioAuthError bioError = BioAuthError.userDismiss;

      activeBiometricPopup.onRetrieveTokenError(
        type: BiometricTokenModuleErrorType.biometrics,
        bioError: bioError,
      );

      verify(() => mockBiometricUtilityFunctions.handleBioError(bioError: any(named: 'bioError')))
          .called(1);
    });

    test('onError handles bio error', () async {
      when(() => mockEvoSnackBar.show(
            any(),
            typeSnackBar: any(named: 'typeSnackBar'),
            durationInMilliSec: any(named: 'durationInMilliSec'),
          )).thenAnswer((_) {
        return Future<bool?>.value(true);
      });

      activeBiometricPopup.onRetrieveTokenError(
          type: BiometricTokenModuleErrorType.noSupportExtraChallenge);

      verify(() => mockEvoSnackBar.show(CommonStrings.otherGenericErrorMessage,
          typeSnackBar: SnackBarType.error,
          durationInMilliSec: SnackBarDuration.short.value)).called(1);
    });
  });

  group('verify showDialogActivateBiometric() method', () {
    test('verify call showDialogBottomSheet() method in Util Function', () async {
      await activeBiometricPopup.showDialogActivateBiometric(onActive: () {});

      verifyDialogUI();
    });

    test('onClickNegative() should call onSkip()', () async {
      bool isCalled = false;
      await activeBiometricPopup.showDialogActivateBiometric(
          onSkip: () => isCalled = true, onActive: () {});
      final List<dynamic> captured = verify(() => mockDialogFunction.showDialogConfirm(
            dialogId: any(named: 'dialogId'),
            content: any(named: 'content'),
            title: any(named: 'title'),
            textPositive: any(named: 'textPositive'),
            textNegative: any(named: 'textNegative'),
            dialogHorizontalPadding: any(named: 'dialogHorizontalPadding'),
            imageHeader: any(named: 'imageHeader'),
            onClickNegative: captureAny(named: 'onClickNegative'),
            onClickPositive: any(named: 'onClickPositive'),
          )).captured;
      final VoidCallback onClick = captured[0] as VoidCallback;

      onClick.call();

      expect(isCalled, isTrue);
    });

    test('onClickPositive() should call onActive()', () async {
      bool isCalled = false;
      await activeBiometricPopup.showDialogActivateBiometric(onActive: () => isCalled = true);
      final List<dynamic> captured = verify(() => mockDialogFunction.showDialogConfirm(
            dialogId: any(named: 'dialogId'),
            content: any(named: 'content'),
            title: any(named: 'title'),
            textPositive: any(named: 'textPositive'),
            textNegative: any(named: 'textNegative'),
            dialogHorizontalPadding: any(named: 'dialogHorizontalPadding'),
            imageHeader: any(named: 'imageHeader'),
            onClickNegative: any(named: 'onClickNegative'),
            onClickPositive: captureAny(named: 'onClickPositive'),
          )).captured;
      final VoidCallback onClick = captured[0] as VoidCallback;

      onClick.call();

      expect(isCalled, isTrue);
    });
  });
}
