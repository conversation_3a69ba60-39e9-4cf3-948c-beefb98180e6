import 'package:evoapp/feature/biometric/biometric_token_module/biometrics_token_module.dart';
import 'package:evoapp/feature/biometric/request_user_active_biometric/request_user_active_biometric_popup.dart';
import 'package:evoapp/feature/biometric/request_user_active_biometric/request_user_active_biometric_util.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper_impl.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockEvoSecureStorageHelperImpl extends Mock implements EvoSecureStorageHelperImpl {}

class MockBuildContext extends Mock implements BuildContext {}

class MockAppState extends Mock implements AppState {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

class MockBiometricsAuthenticate extends Mock implements BiometricsAuthenticate {}

class MockBiometricsTokenModule extends Mock implements BiometricsTokenModule {}

class MockEvoImageProvider extends Mock implements CommonImageProvider {}

void main() {
  late RequestUserActiveBiometricUtil requestUserActiveBiometricUtil;
  late EvoSecureStorageHelperImpl mockEvoSecureStorage;
  late EvoUtilFunction mockEvoUtilFunction;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    getIt.registerLazySingleton<AppState>(() => MockAppState());

    getIt.registerLazySingleton<EvoSecureStorageHelperImpl>(() => MockEvoSecureStorageHelperImpl());
    mockEvoSecureStorage = getIt.get<EvoSecureStorageHelperImpl>();

    getIt.registerLazySingleton<EvoUtilFunction>(() => MockEvoUtilFunction());
    mockEvoUtilFunction = getIt.get<EvoUtilFunction>();

    getIt.registerLazySingleton<BiometricsAuthenticate>(() => MockBiometricsAuthenticate());

    getIt.registerLazySingleton<BiometricsTokenModule>(() => MockBiometricsTokenModule());

    getIt.registerLazySingleton<CommonImageProvider>(() => MockEvoImageProvider());
  });

  setUp(() {
    requestUserActiveBiometricUtil =
        RequestUserActiveBiometricUtil(localStorageHelper: mockEvoSecureStorage);
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('verify constants timeEnableBiometricInHours', () {
    test('timeEnableBiometricInHours is 24h', () {
      expect(RequestUserActiveBiometricUtil.timeEnableBiometricInHours, 24);
    });
  });

  group('verify getRequestUserActiveBiometricPopup() method', () {
    test('verify getRequestUserActiveBiometricPopup()', () {
      final RequestUserActiveBiometricPopup result =
          requestUserActiveBiometricUtil.getRequestUserActiveBiometricPopup();

      expect(result, isA<RequestUserActiveBiometricPopup>());
    });
  });

  group('verify getEnableBiometricAuthenticator() method', () {
    test('verify call local storage', () async {
      when(() => mockEvoSecureStorage.isEnableBiometricAuthenticator())
          .thenAnswer((_) async => true);

      final bool? result = await requestUserActiveBiometricUtil.getEnableBiometricAuthenticator();

      expect(result, true);
      verify(() => mockEvoSecureStorage.isEnableBiometricAuthenticator()).called(1);
    });
  });

  group('verify checkTimeShowBiometric() method', () {
    final DateTime now = DateTime(2023, 08, 17, 22, 22, 22); // 2023, Aug 23, 22:22:22
    const int expectedTimeEnableBiometricInHours = 24;

    setUp(() {
      expect(RequestUserActiveBiometricUtil.timeEnableBiometricInHours,
          expectedTimeEnableBiometricInHours);

      when(() => mockEvoUtilFunction.getCurrentTime()).thenReturn(now);
    });

    test('test time with lastTimeLocal is null', () {
      final bool result = requestUserActiveBiometricUtil.checkTimeShowBiometric(null);

      expect(result, true);
    });

    test('test time not enough to enable biometric', () {
      final DateTime lastTime = DateTime(2023, 08, 17, 22, 00, 22); // 2023, Aug 23, 22:00:22

      final bool result =
          requestUserActiveBiometricUtil.checkTimeShowBiometric(lastTime.toString());

      expect(result, false);
    });

    test('test time not enough to enable biometric', () {
      final DateTime lastTime = DateTime(2023, 08, 16, 22, 00, 22); // 2023, Aug 16, 22:00:22

      final bool result =
          requestUserActiveBiometricUtil.checkTimeShowBiometric(lastTime.toString());

      expect(result, true);
    });
  });

  group('verify isNewDevice() method', () {
    test('verify call local storage', () async {
      when(() => mockEvoSecureStorage.isNewDevice())
          .thenAnswer((_) async => Future<bool>.value(true));

      final bool result = await requestUserActiveBiometricUtil.isNewDevice();

      verify(() => mockEvoSecureStorage.isNewDevice()).called(1);
      expect(result, true);
    });
  });

  group('verify saveTimeShowBiometric method', () {
    test('verify call local storage to store value', () async {
      /// Arrange
      const String now = '2024-08-13 22:22:22'; // 2023, Aug 13, 22:22:22
      when(() => mockEvoUtilFunction.getCurrentTimeString()).thenReturn(now);
      when(() => mockEvoSecureStorage.saveTimeShowBiometric(any()))
          .thenAnswer((_) async => Future<bool>.value(true));

      /// Action
      await requestUserActiveBiometricUtil.saveTimeShowBiometric();

      /// Assert
      verify(() => mockEvoSecureStorage.saveTimeShowBiometric(now)).called(1);
    });
  });

  group('verify getTimeShowBiometric', (){
    test('verify call local storage to get value', () async {
      /// Arrange
      const String value = '2024-08-13 22:22:22'; // 2023, Aug 13, 22:22:22
      when(() => mockEvoUtilFunction.getCurrentTimeString()).thenReturn(value);
      when(() => mockEvoSecureStorage.getTimeShowBiometric())
          .thenAnswer((_) async => value);

      /// Action
      final String? savedTime =  await requestUserActiveBiometricUtil.getTimeShowBiometric();

      /// Assert
      verify(() => mockEvoSecureStorage.getTimeShowBiometric()).called(1);
      expect(savedTime, value);
    });
  });

}
