import 'package:evoapp/feature/biometric/biometric_token_module/biometrics_token_module.dart';
import 'package:evoapp/feature/biometric/model/biometric_ui_model.dart';
import 'package:evoapp/feature/biometric/request_user_active_biometric/request_user_active_biometric_handler.dart';
import 'package:evoapp/feature/biometric/request_user_active_biometric/request_user_active_biometric_handler_impl.dart';
import 'package:evoapp/feature/biometric/request_user_active_biometric/request_user_active_biometric_popup.dart';
import 'package:evoapp/feature/biometric/request_user_active_biometric/request_user_active_biometric_util.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper_impl.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockRequestUserActivateBiometricHandler extends Mock
    implements RequestUserActivateBiometricHandler {}

class MockBiometricsAuthenticate extends Mock implements BiometricsAuthenticate {}

class MockAppState extends Mock implements AppState {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

class MockEvoSecureStorageHelperImpl extends Mock implements EvoSecureStorageHelperImpl {}

class MockBiometricsTokenModule extends Mock implements BiometricsTokenModule {}

class MockRequestUserActiveBiometricUtil extends Mock implements RequestUserActiveBiometricUtil {}

class MockRequestUserActiveBiometricPopup extends Mock implements RequestUserActiveBiometricPopup {}

void main() {
  late MockRequestUserActivateBiometricHandler mockRequestUserActivateBiometricHandler;
  late MockBiometricsAuthenticate mockBiometricsAuthenticate;
  late RequestUserActiveBiometricHandlerImp activeBiometricHandler;
  late AppState mockAppState;
  late RequestUserActiveBiometricUtil mockRequestUserActiveBiometricUtil;
  late RequestUserActiveBiometricPopup mockRequestUserActiveBiometricPopup;

  const String expectedTime = '2024-08-13 10:00:00.000';

  setUpAll(() async {
    TestWidgetsFlutterBinding.ensureInitialized();

    getIt.registerLazySingleton<EvoSecureStorageHelperImpl>(() => MockEvoSecureStorageHelperImpl());

    mockRequestUserActivateBiometricHandler = MockRequestUserActivateBiometricHandler();

    getIt.registerLazySingleton<AppState>(() => MockAppState());
    mockAppState = getIt.get<AppState>();

    getIt.registerLazySingleton<RequestUserActiveBiometricUtil>(
        () => MockRequestUserActiveBiometricUtil());
    mockRequestUserActiveBiometricUtil = getIt.get<RequestUserActiveBiometricUtil>();

    getIt.registerLazySingleton<BiometricsTokenModule>(() => MockBiometricsTokenModule());

    getIt.registerLazySingleton<BiometricsAuthenticate>(() => mockBiometricsAuthenticate);
    getIt.registerLazySingleton<EvoFlutterWrapper>(() => EvoFlutterWrapper());
    getIt.registerLazySingleton<EvoUtilFunction>(() => MockEvoUtilFunction());

    mockRequestUserActiveBiometricPopup = MockRequestUserActiveBiometricPopup();
  });

  setUp(() {
    mockBiometricsAuthenticate = MockBiometricsAuthenticate();

    activeBiometricHandler = RequestUserActiveBiometricHandlerImp(
      requestUserActiveBiometricUtil: mockRequestUserActiveBiometricUtil,
      biometricsAuthenticate: mockBiometricsAuthenticate,
    );

    /// stub show dialog active biometric
    when(() => mockRequestUserActiveBiometricUtil.saveTimeShowBiometric()).thenAnswer((_) async {
      return Future<void>.value();
    });

    when(() => mockRequestUserActiveBiometricUtil.getRequestUserActiveBiometricPopup())
        .thenReturn(mockRequestUserActiveBiometricPopup);

    when(() => mockRequestUserActiveBiometricPopup.showDialogActiveBiometric()).thenAnswer((_) =>
        Future<RequestUserActivateBiometricStatus>.value(
            RequestUserActivateBiometricStatus.success));
  });

  tearDown(() {
    reset(mockRequestUserActivateBiometricHandler);
    reset(mockRequestUserActiveBiometricUtil);
  });

  tearDownAll(() async {
    await getIt.reset();
  });

  group('Verify start() method', () {
    setUp(() {
      when(() => mockRequestUserActiveBiometricUtil.getRequestUserActiveBiometricPopup())
          .thenReturn(mockRequestUserActiveBiometricPopup);

      final BiometricTypeUIModel model = BiometricTypeUIModel.faceAndFinger();
      when(() => mockAppState.bioTypeInfo).thenReturn(model);

      when(() => mockBiometricsAuthenticate.isDeviceSupportBiometrics()).thenAnswer((_) async {
        return Future<bool>.value(true);
      });

      when(() => mockRequestUserActiveBiometricUtil.getEnableBiometricAuthenticator())
          .thenAnswer((_) async => false);
    });

    test('Verify that no popup if the device has not support biometric', () async {
      when(() => mockBiometricsAuthenticate.isDeviceSupportBiometrics()).thenAnswer((_) async {
        return Future<bool>.value(false);
      });

      final RequestUserActivateBiometricStatus result =
          await activeBiometricHandler.start(useCase: ActivateBiometricUseCase.newDevice);

      verify(() => mockBiometricsAuthenticate.isDeviceSupportBiometrics()).called(1);

      expect(result, RequestUserActivateBiometricStatus.unQualified);
    });

    test('Verify that no popup if the device has support biometric & biometric is enabled',
        () async {
      when(() => mockBiometricsAuthenticate.isDeviceSupportBiometrics()).thenAnswer((_) async {
        return Future<bool>.value(true);
      });

      when(() => mockRequestUserActiveBiometricUtil.getEnableBiometricAuthenticator())
          .thenAnswer((_) async => true);

      final RequestUserActivateBiometricStatus result =
          await activeBiometricHandler.start(useCase: ActivateBiometricUseCase.newDevice);

      verify(() => mockBiometricsAuthenticate.isDeviceSupportBiometrics()).called(1);

      verify(() => mockRequestUserActiveBiometricUtil.getEnableBiometricAuthenticator()).called(1);

      expect(result, RequestUserActivateBiometricStatus.success);
    });

    test('Verify that popup is displayed, if ActivateBiometricUseCase = newDevice', () async {
      /// Arrange
      when(() => mockRequestUserActiveBiometricUtil.isNewDevice()).thenAnswer((_) async => true);

      /// Action
      final RequestUserActivateBiometricStatus result =
          await activeBiometricHandler.start(useCase: ActivateBiometricUseCase.newDevice);

      /// Asserts
      expect(result, RequestUserActivateBiometricStatus.success);

      verifyInOrder(<Object Function()>[
        () => mockRequestUserActiveBiometricUtil.isNewDevice(),
        () => mockRequestUserActiveBiometricUtil.saveTimeShowBiometric(),
        () => mockRequestUserActiveBiometricUtil.getRequestUserActiveBiometricPopup(),
        () => mockRequestUserActiveBiometricPopup.showDialogActiveBiometric(),
      ]);
    });

    test('Verify that popup is displayed, if ActivateBiometricUseCase = null', () async {
      /// Arrange
      when(() => mockRequestUserActiveBiometricUtil.getTimeShowBiometric()).thenAnswer((_) async {
        return expectedTime;
      });
      when(() => mockRequestUserActiveBiometricUtil.checkTimeShowBiometric(any())).thenReturn(true);

      /// Action
      final RequestUserActivateBiometricStatus result =
          await activeBiometricHandler.start();

      /// Asserts
      expect(result, RequestUserActivateBiometricStatus.success);

      verifyInOrder(<Object Function()>[
        () => mockRequestUserActiveBiometricUtil.getTimeShowBiometric(),
        () => mockRequestUserActiveBiometricUtil.checkTimeShowBiometric(expectedTime),
        () => mockRequestUserActiveBiometricUtil.saveTimeShowBiometric(),
        () => mockRequestUserActiveBiometricUtil.getRequestUserActiveBiometricPopup(),
        () => mockRequestUserActiveBiometricPopup.showDialogActiveBiometric(),
      ]);
    });
  });

  group('Verify handleUseCaseNewDevice() method', () {
    test('Verify that no popup is displayed when isNewDevice = false', () async {
      when(() => mockRequestUserActiveBiometricUtil.isNewDevice()).thenAnswer((_) async => false);

      final RequestUserActivateBiometricStatus result =
          await activeBiometricHandler.handleUseCaseNewDevice();

      expect(result, RequestUserActivateBiometricStatus.unQualified);

      verify(() => mockRequestUserActiveBiometricUtil.isNewDevice()).called(1);
    });

    test('Verify that popup is displayed when isNewDevice = true', () async {
      /// Arrange
      when(() => mockRequestUserActiveBiometricUtil.isNewDevice()).thenAnswer((_) async => true);

      /// Action
      final RequestUserActivateBiometricStatus result =
          await activeBiometricHandler.handleUseCaseNewDevice();

      /// Asserts
      expect(result, RequestUserActivateBiometricStatus.success);

      verifyInOrder(<Object Function()>[
        () => mockRequestUserActiveBiometricUtil.isNewDevice(),
        () => mockRequestUserActiveBiometricUtil.saveTimeShowBiometric(),
        () => mockRequestUserActiveBiometricUtil.getRequestUserActiveBiometricPopup(),
        () => mockRequestUserActiveBiometricPopup.showDialogActiveBiometric(),
      ]);
    });
  });

  group('Verify handleUseCaseReLoginOnOldDevice method', () {
    test(
      'Verify that no popup is displayed, if the allowed display time has not yet been reached',
      () async {
        /// Arrange
        when(() => mockRequestUserActiveBiometricUtil.getTimeShowBiometric()).thenAnswer((_) async {
          return expectedTime;
        });

        /// stub time show biometric has not yet been reached
        when(() => mockRequestUserActiveBiometricUtil.checkTimeShowBiometric(any()))
            .thenReturn(false);

        /// Action
        final RequestUserActivateBiometricStatus result =
            await activeBiometricHandler.handleUseCaseReLoginOnOldDevice();

        /// Assert
        expect(result, RequestUserActivateBiometricStatus.unQualified);

        verifyInOrder(<Object Function()>[
          () => mockRequestUserActiveBiometricUtil.getTimeShowBiometric(),
          () => mockRequestUserActiveBiometricUtil.checkTimeShowBiometric(expectedTime),
        ]);
      },
    );

    test('Verify that popup is displayed, if the allowed display time has been reached', () async {
      /// Arrange
      when(() => mockRequestUserActiveBiometricUtil.getTimeShowBiometric()).thenAnswer((_) async {
        return expectedTime;
      });
      when(() => mockRequestUserActiveBiometricUtil.checkTimeShowBiometric(any())).thenReturn(true);

      /// Action
      final RequestUserActivateBiometricStatus result =
          await activeBiometricHandler.handleUseCaseReLoginOnOldDevice();

      /// Asserts
      expect(result, RequestUserActivateBiometricStatus.success);

      verifyInOrder(<Object Function()>[
        () => mockRequestUserActiveBiometricUtil.getTimeShowBiometric(),
        () => mockRequestUserActiveBiometricUtil.checkTimeShowBiometric(expectedTime),
        () => mockRequestUserActiveBiometricUtil.saveTimeShowBiometric(),
        () => mockRequestUserActiveBiometricUtil.getRequestUserActiveBiometricPopup(),
        () => mockRequestUserActiveBiometricPopup.showDialogActiveBiometric(),
      ]);
    });
  });

  group('Verify showDialogActiveBiometric method', () {
    setUp(() {
      when(() => mockRequestUserActiveBiometricUtil.saveTimeShowBiometric()).thenAnswer((_) async {
        return Future<void>.value();
      });

      when(() => mockRequestUserActiveBiometricUtil.getRequestUserActiveBiometricPopup())
          .thenReturn(mockRequestUserActiveBiometricPopup);
    });

    tearDown(() {
      activeBiometricHandler.isShowingPopupActiveBiometric = false;
    });

    test('verify that a popup is displayed at a time', () async {
      activeBiometricHandler.isShowingPopupActiveBiometric = true;

      final RequestUserActivateBiometricStatus result =
          await activeBiometricHandler.showDialogActiveBiometric();

      expect(result, RequestUserActivateBiometricStatus.inProgress);

      verify(() => mockRequestUserActiveBiometricUtil.saveTimeShowBiometric()).called(1);
      verifyNever(() => mockRequestUserActiveBiometricUtil.getRequestUserActiveBiometricPopup());
      verifyNever(() => mockRequestUserActiveBiometricPopup.showDialogActiveBiometric());

      expect(activeBiometricHandler.isShowingPopupActiveBiometric, true);
    });

    test('verify that a popup is displayed correctly',
        () async {
      when(() => mockRequestUserActiveBiometricPopup.showDialogActiveBiometric()).thenAnswer((_) =>
          Future<RequestUserActivateBiometricStatus>.value(
              RequestUserActivateBiometricStatus.success));

      final RequestUserActivateBiometricStatus result =
          await activeBiometricHandler.showDialogActiveBiometric();

      expect(result, RequestUserActivateBiometricStatus.success);

      verifyInOrder(<Object Function()>[
        () => mockRequestUserActiveBiometricUtil.saveTimeShowBiometric(),
        () => mockRequestUserActiveBiometricUtil.getRequestUserActiveBiometricPopup(),
        () => mockRequestUserActiveBiometricPopup.showDialogActiveBiometric(),
      ]);

      expect(activeBiometricHandler.isShowingPopupActiveBiometric, false);
    });

  });
}
