import 'package:evoapp/feature/account_activation/mock/mock_account_activation_use_case.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('verify getMockAccountActivationFileNameByCase should return corrected file names', () {
    expect(
        getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.getVerifyOtpChallengeType),
        'get_account_activation_verify_otp_challenge_type.json');

    expect(
        getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.getVerifySelfieChallengeType),
        'get_account_activation_verify_selfie_challenge_type.json');

    expect(
        getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.getCreateUsernameChallengeType),
        'get_account_activation_create_username_challenge_type.json');

    expect(
        getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.getVerifyEmailChallengeType),
        'get_account_activation_verify_email_challenge_type.json');

    expect(
        getMockAccountActivationFileNameByCase(MockAccountActivationUseCase.getNoneChallengeType),
        'get_none_challenge_type.json');
  });
}
