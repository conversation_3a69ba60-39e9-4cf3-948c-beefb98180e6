// ignore_for_file: depend_on_referenced_packages

import 'package:evoapp/feature/biometric/utils/bio_auth_result.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate_impl.dart';
import 'package:evoapp/feature/logging/evo_logging_event.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/util/share_preference_helper.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:local_auth/error_codes.dart' as auth_error;
import 'package:local_auth/local_auth.dart';
import 'package:local_auth_android/local_auth_android.dart';
import 'package:local_auth_darwin/local_auth_darwin.dart';
import 'package:mocktail/mocktail.dart';

//mock local auth
class MockLocalAuth extends Mock implements LocalAuthentication {}

class MockSecureStorage extends Mock implements CommonSharedPreferencesHelper {}

class MockEvoFlutterWrapper extends Mock implements EvoFlutterWrapper {}

class MockLoggingRepo extends Mock implements LoggingRepo {}

void main() {
  late BiometricsAuthenticate bioAuth;
  late MockLocalAuth mockLocalAuth;
  late EvoFlutterWrapper mockEvoFlutterWrapper;
  late MockLoggingRepo mockLoggingRepo;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    mockLocalAuth = MockLocalAuth();
    getIt.registerLazySingleton<EvoFlutterWrapper>(() => MockEvoFlutterWrapper());
    mockEvoFlutterWrapper = getIt.get<EvoFlutterWrapper>();

    mockLoggingRepo = MockLoggingRepo();
    getIt.registerLazySingleton<LoggingRepo>(() => mockLoggingRepo);
  });

  tearDownAll(() {
    getIt.reset();
  });

  group(
    'checking getAvailableBiometric() When it is Android device & forcing using Strong bio ',
    () {
      setUp(() {
        TestWidgetsFlutterBinding.ensureInitialized();
        when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(true);
        bioAuth = BiometricAuthenticateImpl(
          localAuth: mockLocalAuth,
          isForceStrongBiometric: true,
        );
      });

      test(
          'hasEnrolledBiometric() return TRUE when list of Available Biometrics contain STRONG BIO',
          () async {
        /// setup
        when(() => mockLocalAuth.getAvailableBiometrics()).thenAnswer((_) async {
          return <BiometricType>[BiometricType.strong, BiometricType.weak, BiometricType.face];
        });

        ///action
        final bool hasEnrolled = await bioAuth.hasEnrolledBiometric();
        final List<BiometricType> listOfBio = await bioAuth.getAvailableBiometricType();

        ///assert
        expect(hasEnrolled, true);
        expect(listOfBio, <BiometricType>[BiometricType.strong]);
      });

      test(
          'hasEnrolledBiometric() return FALSE when list of Available Biometrics DO NOT contain Strong class',
          () async {
        /// setup
        when(() => mockLocalAuth.getAvailableBiometrics()).thenAnswer((_) async {
          return <BiometricType>[BiometricType.weak, BiometricType.face];
        });

        ///action
        final bool hasEnrolled = await bioAuth.hasEnrolledBiometric();
        final List<BiometricType> listOfBio = await bioAuth.getAvailableBiometricType();

        ///assert
        expect(hasEnrolled, false);
        expect(listOfBio, <BiometricType>[]);
      });
    },
  );

  group(
    'checking getAvailableBiometric() When it is Android device & NOT forcing using STRONG BIO',
    () {
      setUp(() {
        TestWidgetsFlutterBinding.ensureInitialized();
        when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(true);
        bioAuth = BiometricAuthenticateImpl(
          localAuth: mockLocalAuth,
        );
      });

      test('hasEnrolledBiometric() return TRUE when list of Available Biometrics do NOT EMPTY',
          () async {
        /// setup
        when(() => mockLocalAuth.getAvailableBiometrics()).thenAnswer((_) async {
          return <BiometricType>[BiometricType.strong, BiometricType.weak, BiometricType.face];
        });

        ///action
        final bool hasEnrolled = await bioAuth.hasEnrolledBiometric();
        final List<BiometricType> listOfBio = await bioAuth.getAvailableBiometricType();

        ///assert
        expect(hasEnrolled, true);
        expect(listOfBio,
            <BiometricType>[BiometricType.strong, BiometricType.weak, BiometricType.face]);
      });

      test('hasEnrolledBiometric() return FALSE when list of Available Biometrics is EMPTY',
          () async {
        /// setup
        when(() => mockLocalAuth.getAvailableBiometrics()).thenAnswer((_) async {
          return <BiometricType>[];
        });

        ///action
        final bool hasEnrolled = await bioAuth.hasEnrolledBiometric();
        final List<BiometricType> listOfBio = await bioAuth.getAvailableBiometricType();

        ///assert
        expect(hasEnrolled, false);
        expect(listOfBio, <BiometricType>[]);
      });
    },
  );

  group(
    'checking getAvailableBiometric() When it is iOS device and isSupportingAndroidStrongBiometric is TRUE',
    () {
      setUp(() {
        TestWidgetsFlutterBinding.ensureInitialized();
        when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(false);
        bioAuth = BiometricAuthenticateImpl(localAuth: mockLocalAuth, isForceStrongBiometric: true);
      });

      test('hasEnrolledBiometric() return TRUE when list of Available Biometrics is NOT EMPTY',
          () async {
        /// setup
        when(() => mockLocalAuth.getAvailableBiometrics()).thenAnswer((_) async {
          return <BiometricType>[BiometricType.fingerprint, BiometricType.face];
        });

        ///action
        final bool hasEnrolled = await bioAuth.hasEnrolledBiometric();
        final List<BiometricType> listOfBio = await bioAuth.getAvailableBiometricType();

        ///assert
        expect(hasEnrolled, true);
        expect(listOfBio, <BiometricType>[BiometricType.fingerprint, BiometricType.face]);
      });

      test('hasEnrolledBiometric() return FALSE when list of Available Biometrics is EMPTY',
          () async {
        /// setup
        when(() => mockLocalAuth.getAvailableBiometrics()).thenAnswer((_) async {
          return <BiometricType>[];
        });

        ///action
        final bool hasEnrolled = await bioAuth.hasEnrolledBiometric();
        final List<BiometricType> listOfBio = await bioAuth.getAvailableBiometricType();

        ///assert
        expect(hasEnrolled, false);
        expect(listOfBio, <BiometricType>[]);
      });
    },
  );

  group('checking authenticate() with Android Device', () {
    late List<AuthMessages> authMessages;
    setUp(() {
      TestWidgetsFlutterBinding.ensureInitialized();
      when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(true);
      when(() => mockEvoFlutterWrapper.isIOS()).thenReturn(false);
      bioAuth = BiometricAuthenticateImpl(
        localAuth: mockLocalAuth,
      );

      authMessages = bioAuth.getAuthMessages();

      when(() => mockLoggingRepo.logErrorEvent(
          errorType: any(named: 'errorType'),
          args: any(named: 'args'))).thenAnswer((_) => Future<void>.value());
    });

    tearDown(() {
      reset(mockLoggingRepo);
    });

    test('authentication return SUCCESS', () async {
      when(() => mockLocalAuth.authenticate(
            localizedReason: EvoStrings.localizedReasonForUsingBiometrics,
            options: BiometricAuthenticateImpl.defaultAuthenticationOptions,
            authMessages: authMessages,
          )).thenAnswer((_) async {
        return true;
      });

      final BioAuthResult actual =
          await bioAuth.authenticate(localizedReason: EvoStrings.localizedReasonForUsingBiometrics);

      expect(actual, isA<BioAuthResult>());
      expect(actual.isAuthSuccess, true);
    });

    test('return BioAuthError.userDismiss when user click Cancel System biometric dialog',
        () async {
      when(() => mockLocalAuth.authenticate(
          localizedReason: EvoStrings.localizedReasonForUsingBiometrics,
          options: BiometricAuthenticateImpl.defaultAuthenticationOptions,
          authMessages: authMessages)).thenAnswer((_) async {
        return false;
      });

      final BioAuthResult actual =
          await bioAuth.authenticate(localizedReason: EvoStrings.localizedReasonForUsingBiometrics);

      expect(actual.isAuthSuccess, false);
      expect(actual.error, BioAuthError.userDismiss);
    });

    test('return BioAuthError.permanentlyLockedOut when biometric system is locked permanently',
        () async {
      when(() => mockLocalAuth.authenticate(
              localizedReason: EvoStrings.localizedReasonForUsingBiometrics,
              options: BiometricAuthenticateImpl.defaultAuthenticationOptions,
              authMessages: authMessages))
          .thenThrow(PlatformException(code: auth_error.permanentlyLockedOut));

      final BioAuthResult actual =
          await bioAuth.authenticate(localizedReason: EvoStrings.localizedReasonForUsingBiometrics);

      expect(actual.isAuthSuccess, false);
      expect(actual.error, BioAuthError.permanentlyLockedOut);

      verify(() => mockLoggingRepo.logErrorEvent(
          errorType: EvoEventType.biometrics.name, args: any(named: 'args'))).called(1);
    });

    test('return BioAuthError.androidLockedOut when biometric is locked temporary', () async {
      when(() => mockLocalAuth.authenticate(
          localizedReason: EvoStrings.localizedReasonForUsingBiometrics,
          options: BiometricAuthenticateImpl.defaultAuthenticationOptions,
          authMessages: authMessages)).thenThrow(PlatformException(code: auth_error.lockedOut));

      final BioAuthResult actual =
          await bioAuth.authenticate(localizedReason: EvoStrings.localizedReasonForUsingBiometrics);

      expect(actual.isAuthSuccess, false);
      expect(actual.error, BioAuthError.androidLockedOut);

      verify(() => mockLoggingRepo.logErrorEvent(
          errorType: EvoEventType.biometrics.name, args: any(named: 'args'))).called(1);
    });

    test('return BioAuthError.notEnrolled when there is no biometric in system device', () async {
      when(() => mockLocalAuth.authenticate(
          localizedReason: EvoStrings.localizedReasonForUsingBiometrics,
          options: BiometricAuthenticateImpl.defaultAuthenticationOptions,
          authMessages: authMessages)).thenThrow(PlatformException(code: auth_error.notEnrolled));

      final BioAuthResult actual =
          await bioAuth.authenticate(localizedReason: EvoStrings.localizedReasonForUsingBiometrics);

      expect(actual.isAuthSuccess, false);
      expect(actual.error, BioAuthError.notEnrolled);

      verify(() => mockLoggingRepo.logErrorEvent(
          errorType: EvoEventType.biometrics.name, args: any(named: 'args'))).called(1);
    });

    test('return BioAuthError.unknown if lib return other exception', () async {
      when(() => mockLocalAuth.authenticate(
          localizedReason: EvoStrings.localizedReasonForUsingBiometrics,
          options: BiometricAuthenticateImpl.defaultAuthenticationOptions,
          authMessages: authMessages)).thenThrow(PlatformException(code: auth_error.notAvailable));

      final BioAuthResult actual =
          await bioAuth.authenticate(localizedReason: EvoStrings.localizedReasonForUsingBiometrics);

      expect(actual.isAuthSuccess, false);
      expect(actual.error, BioAuthError.unknown);

      verify(() => mockLoggingRepo.logErrorEvent(
          errorType: EvoEventType.biometrics.name, args: any(named: 'args'))).called(1);
    });
  });

  group('checking authenticate() with iOS Device', () {
    late List<AuthMessages> authMessages;
    setUp(() {
      TestWidgetsFlutterBinding.ensureInitialized();
      when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(false);
      when(() => mockEvoFlutterWrapper.isIOS()).thenReturn(true);
      bioAuth = BiometricAuthenticateImpl(
        localAuth: mockLocalAuth,
      );

      authMessages = bioAuth.getAuthMessages();

      when(() => mockLoggingRepo.logErrorEvent(
          errorType: any(named: 'errorType'),
          args: any(named: 'args'))).thenAnswer((_) => Future<void>.value());
    });

    tearDown(() {
      reset(mockLoggingRepo);
    });

    test('authentication return SUCCESS', () async {
      when(() => mockLocalAuth.authenticate(
          localizedReason: EvoStrings.localizedReasonForUsingBiometrics,
          options: BiometricAuthenticateImpl.defaultAuthenticationOptions,
          authMessages: authMessages)).thenAnswer((_) async {
        return true;
      });

      final BioAuthResult actual =
          await bioAuth.authenticate(localizedReason: EvoStrings.localizedReasonForUsingBiometrics);

      expect(actual, isA<BioAuthResult>());
      expect(actual.isAuthSuccess, true);
    });

    test('return BioAuthError.userDismiss when user click Cancel System biometric dialog',
        () async {
      when(() => mockLocalAuth.authenticate(
          localizedReason: EvoStrings.localizedReasonForUsingBiometrics,
          options: BiometricAuthenticateImpl.defaultAuthenticationOptions,
          authMessages: authMessages)).thenThrow(PlatformException(code: auth_error.notAvailable));

      final BioAuthResult actual =
          await bioAuth.authenticate(localizedReason: EvoStrings.localizedReasonForUsingBiometrics);

      expect(actual.isAuthSuccess, false);
      expect(actual.error, BioAuthError.userDismiss);

      verify(() => mockLoggingRepo.logErrorEvent(
          errorType: EvoEventType.biometrics.name, args: any(named: 'args'))).called(1);
    });

    test('return BioAuthError.permanentlyLockedOut If authenticate() return false', () async {
      when(() => mockLocalAuth.authenticate(
          localizedReason: EvoStrings.localizedReasonForUsingBiometrics,
          options: BiometricAuthenticateImpl.defaultAuthenticationOptions,
          authMessages: authMessages)).thenAnswer((_) async {
        return false;
      });

      final BioAuthResult actual =
          await bioAuth.authenticate(localizedReason: EvoStrings.localizedReasonForUsingBiometrics);

      expect(actual.isAuthSuccess, false);
      expect(actual.error, BioAuthError.permanentlyLockedOut);
    });

    test('return BioAuthError.unknown if lib return other PlatformException', () async {
      when(() => mockLocalAuth.authenticate(
              localizedReason: EvoStrings.localizedReasonForUsingBiometrics,
              options: BiometricAuthenticateImpl.defaultAuthenticationOptions,
              authMessages: authMessages))
          .thenThrow(PlatformException(code: auth_error.biometricOnlyNotSupported));

      final BioAuthResult actual =
          await bioAuth.authenticate(localizedReason: EvoStrings.localizedReasonForUsingBiometrics);

      expect(actual.isAuthSuccess, false);
      expect(actual.error, BioAuthError.unknown);

      verify(() => mockLoggingRepo.logErrorEvent(
          errorType: EvoEventType.biometrics.name, args: any(named: 'args'))).called(1);
    });

    test('logErrorEvent is invoked if lib return ERROR', () async {
      when(() => mockLocalAuth.authenticate(
          localizedReason: EvoStrings.localizedReasonForUsingBiometrics,
          options: BiometricAuthenticateImpl.defaultAuthenticationOptions,
          authMessages: authMessages)).thenThrow(Error());

      bioAuth.authenticate(localizedReason: EvoStrings.localizedReasonForUsingBiometrics);

      verify(() => mockLoggingRepo.logErrorEvent(
          errorType: EvoEventType.biometrics.name, args: any(named: 'args'))).called(1);
    });

    test('logErrorEvent is invoked if lib return other Exception', () async {
      when(() => mockLocalAuth.authenticate(
          localizedReason: EvoStrings.localizedReasonForUsingBiometrics,
          options: BiometricAuthenticateImpl.defaultAuthenticationOptions,
          authMessages: authMessages)).thenThrow(Exception());

      bioAuth.authenticate(localizedReason: EvoStrings.localizedReasonForUsingBiometrics);

      verify(() => mockLoggingRepo.logErrorEvent(
          errorType: EvoEventType.biometrics.name, args: any(named: 'args'))).called(1);
    });
  });

  group('verify getAuthMessage', () {
    setUp(() {
      bioAuth = BiometricAuthenticateImpl(
        localAuth: mockLocalAuth,
        isForceStrongBiometric: true,
      );
    });

    test('cancelButton should be `use MPIN`', () {
      final List<AuthMessages> authMessages = bioAuth.getAuthMessages();

      expect(authMessages.length, 2);
      final (IOSAuthMessages iosMessage, AndroidAuthMessages androidMessage) = authMessages.let(
        (List<AuthMessages> messages) =>
            (messages[0] as IOSAuthMessages, messages[1] as AndroidAuthMessages),
      );
      expect(iosMessage.localizedFallbackTitle, '');
      expect(iosMessage.authCancelButton, EvoStrings.useMPIN);
      expect(androidMessage.cancelButton, EvoStrings.useMPIN);

      expect(authMessages[0], isA<IOSAuthMessages>());
    });

    test('cancelButton should match', () {
      const String cancelButton = 'cancel_button';
      final List<AuthMessages> authMessages = bioAuth.getAuthMessages(cancelButton: cancelButton);

      expect(authMessages.length, 2);
      final (IOSAuthMessages iosMessage, AndroidAuthMessages androidMessage) = authMessages.let(
        (List<AuthMessages> messages) =>
            (messages[0] as IOSAuthMessages, messages[1] as AndroidAuthMessages),
      );
      expect(iosMessage.localizedFallbackTitle, '');
      expect(iosMessage.authCancelButton, cancelButton);
      expect(androidMessage.cancelButton, cancelButton);

      expect(authMessages[0], isA<IOSAuthMessages>());
    });
  });
}
