import 'package:app_settings/app_settings.dart';
import 'package:evoapp/feature/biometric/model/biometric_ui_model.dart';
import 'package:evoapp/feature/biometric/utils/bio_auth_result.dart';
import 'package:evoapp/feature/biometric/utils/biometric_functions.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/images.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/app_settings_wrapper.dart';
import 'package:evoapp/util/dialog_functions.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/util/navigator/evo_router_navigator.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/widget/hud_loading/hud_loading.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockEvoNavigator extends Mock implements EvoRouterNavigator {}

class MockHudLoading extends Mock implements HudLoading {}

class MockLoggingRepo extends Mock implements LoggingRepo {}

class MockDeviceInfoPlugin extends Mock implements DeviceInfoPlugin {}

class MockCommonUtilFunction extends Mock implements CommonUtilFunction {}

class MockDialogFunction extends Mock implements DialogFunction {}

class MockAppSettingsWrapper extends Mock implements AppSettingsWrapper {}

class MockContext extends Mock implements BuildContext {}

class MockEvoSnackBar extends Mock implements EvoSnackBar {}

void main() {
  late BiometricFunctions biometricFunctions;
  late EvoSnackBar mockSnackBar;

  setUpAll(() {
    getIt.registerLazySingleton<EvoSnackBar>(() => MockEvoSnackBar());
    mockSnackBar = getIt.get<EvoSnackBar>();

    registerFallbackValue(SnackBarType.error);
    registerFallbackValue(SnackBarType.warning);
    registerFallbackValue(SnackBarType.neutral);
    registerFallbackValue(SnackBarType.success);
  });

  setUp(() {
    biometricFunctions = BiometricFunctions();
  });

  group('test getBiometricUIModel() function', () {
    test('test model is TsBiometricType.Face', () {
      final BiometricTypeUIModel model =
          biometricFunctions.getBiometricUIModel(TsBiometricType.face);
      expect(model.biometricTypeName, EvoStrings.faceText);
      expect(model.iconPath, EvoImages.icFaceId);
      expect(model.iconSettingPath, EvoImages.icSettingFaceId);
    });

    test('test model is TsBiometricType.Finger', () {
      final BiometricTypeUIModel model =
          biometricFunctions.getBiometricUIModel(TsBiometricType.finger);
      expect(model.biometricTypeName, EvoStrings.fingerText);
      expect(model.iconPath, EvoImages.icFingerId);
      expect(model.iconSettingPath, EvoImages.icSettingFingerId);
    });

    test('test model is BiometricTypeUIModel.faceAndFinger on android OS', () {
      final BiometricTypeUIModel model =
          biometricFunctions.getBiometricUIModel(TsBiometricType.androidBio);
      expect(model.biometricTypeName, EvoStrings.faceFingerText);
      expect(model.iconPath, EvoImages.icFaceFingerId);
      expect(model.iconSettingPath, EvoImages.icSettingFaceFingerId);
    });

    test('test model is BiometricTypeUIModel.faceAndFinger on unknown', () {
      final BiometricTypeUIModel model =
          biometricFunctions.getBiometricUIModel(TsBiometricType.unknown);
      expect(model.biometricTypeName, EvoStrings.faceFingerText);
      expect(model.iconPath, EvoImages.icFaceFingerId);
      expect(model.iconSettingPath, EvoImages.icSettingFaceFingerId);
    });
  });

  group('test handleBioErrors', () {
    late BuildContext mockNavigatorContext;

    setUpAll(() {
      getIt.registerLazySingleton<CommonNavigator>(() => MockEvoNavigator());
      mockNavigatorContext = MockContext();
      setUpMockGlobalKeyProvider(mockNavigatorContext);

      getIt.registerLazySingleton<AppSettingsWrapper>(() => MockAppSettingsWrapper());
      getIt.registerLazySingleton<DialogFunction>(() => MockDialogFunction());

      registerFallbackValue(EvoDialogId.openDeviceSecuritySettingDialog);
      registerFallbackValue(AppSettingsType.security);

      when(() => evoDialogFunction.showDialogConfirm(
            dialogId: any(named: 'dialogId'),
            title: any(named: 'title'),
            content: any(named: 'content'),
            textPositive: any(named: 'textPositive'),
            textNegative: any(named: 'textNegative'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
          )).thenAnswer((_) => Future<void>.value());

      when(() => appSettingsWrapper.openAppSettings(type: any(named: 'type')))
          .thenAnswer((_) => Future<void>.value());
    });

    tearDownAll(() {
      getIt.unregister<CommonNavigator>();
      getIt.unregister<GlobalKeyProvider>();
    });

    void verifyBiometricDialogSettingShowed({required String title, required String content}) {
      final List<dynamic> result = verify(
        () => evoDialogFunction.showDialogConfirm(
          dialogId: EvoDialogId.openDeviceSecuritySettingDialog,
          title: title,
          content: content,
          textPositive: EvoStrings.settingTitle,
          textNegative: EvoStrings.ignoreTitle,
          onClickPositive: captureAny(named: 'onClickPositive'),
          onClickNegative: captureAny(named: 'onClickNegative'),
        ),
      ).captured;

      final VoidCallback onClickPositive = result[0] as VoidCallback;
      onClickPositive.call();
      verify(() => mockNavigatorContext.pop()).called(1);
      verify(() => appSettingsWrapper.openAppSettings(type: AppSettingsType.security)).called(1);

      final VoidCallback onClickNegative = result[1] as VoidCallback;
      onClickNegative.call();
      verify(() => mockNavigatorContext.pop()).called(1);
    }

    testWidgets('should onActionWhenDismiss is called when bioError is userDismiss',
        (WidgetTester tester) async {
      bool isActionWhenDismissCalled = false;
      await biometricFunctions.handleBioError(
          bioError: BioAuthError.userDismiss,
          onActionWhenDismiss: () {
            isActionWhenDismissCalled = true;
          });

      expect(isActionWhenDismissCalled, isTrue);
    });

    testWidgets('should toast displayed when bioError is unknown', (WidgetTester tester) async {
      when(() => mockSnackBar.show(
            any(),
            typeSnackBar: any(named: 'typeSnackBar'),
            durationInMilliSec: any(named: 'durationInMilliSec'),
          )).thenAnswer((_) async {
        return Future<bool?>.value(true);
      });

      await biometricFunctions.handleBioError(bioError: BioAuthError.unknown);

      verify(() => mockSnackBar.show(
            EvoStrings.unknownError,
            typeSnackBar: SnackBarType.error,
            durationInMilliSec: SnackBarDuration.short.value,
          )).called(1);
    });

    testWidgets('should show biometric required when bioError is notEnrolled',
        (WidgetTester tester) async {
      await biometricFunctions.handleBioError(bioError: BioAuthError.notEnrolled);

      verifyBiometricDialogSettingShowed(
        title: EvoStrings.biometricRequiredTitle,
        content: EvoStrings.openDeviceSecuritySettingDesc,
      );
    });

    testWidgets('should show biometric locked when bioError is permanentlyLockedOut',
        (WidgetTester tester) async {
      await biometricFunctions.handleBioError(bioError: BioAuthError.permanentlyLockedOut);

      verifyBiometricDialogSettingShowed(
        title: EvoStrings.biometricLockedTitle,
        content: EvoStrings.openDeviceSecuritySettingToUnlockDesc,
      );
    });

    testWidgets('should show biometric locked when bioError is androidLockedOut',
        (WidgetTester tester) async {
      await biometricFunctions.handleBioError(bioError: BioAuthError.androidLockedOut);

      verifyBiometricDialogSettingShowed(
        title: EvoStrings.biometricLockedTitle,
        content: EvoStrings.openDeviceSecuritySettingToUnlockDesc,
      );
    });
  });
}
