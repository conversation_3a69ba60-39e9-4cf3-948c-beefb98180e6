import 'package:evoapp/feature/biometric/base/ext_biometric_token_entity.dart';
import 'package:evoapp/feature/biometric/biometric_token_module/biometric_token_action.dart';
import 'package:evoapp/feature/biometric/biometric_token_module/biometrics_token_module.dart';
import 'package:evoapp/feature/biometric/utils/bio_auth_result.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('BiometricTokenAction Tests', () {
    test('BiometricTokenSuccess should store biometricToken', () {
      final ActionSuccess success = ActionSuccess('token123');
      expect(success.biometricToken, 'token123');
    });

    test('BiometricTokenChallenge should store challengeType', () {
      const ChallengeType challengeType = ChallengeType.none;
      final ActionChallenge challenge = ActionChallenge(challengeType);
      expect(challenge.challengeType, challengeType);
    });

    test('BiometricTokenError should store errorType and errorMessage', () {
      const BiometricTokenModuleErrorType errorType =
          BiometricTokenModuleErrorType.ignoreExtraChallenge;
      final ActionError error =
          ActionError(errorType: errorType, errorMessage: 'An error occurred');
      expect(error.errorType, errorType);
      expect(error.errorMessage, 'An error occurred');
    });

    test('BiometricTokenApiError should store error and have correct errorType', () {
      final ErrorUIModel error =
          ErrorUIModel(userMessage: 'API error occurred'); // replace with actual class constructor
      final ActionApiError apiError = ActionApiError(error: error);
      expect(apiError.error, error);
      expect(apiError.errorType, BiometricTokenModuleErrorType.apiError);
      expect(apiError.errorMessage, 'API error occurred');
    });

    test('BiometricTokenBiometricError should store error and have correct errorType', () {
      const BioAuthError bioError =
          BioAuthError.notEnrolled; // replace with actual class constructor
      final ActionBiometricError biometricError = ActionBiometricError(error: bioError);
      expect(biometricError.error, bioError);
      expect(biometricError.errorType, BiometricTokenModuleErrorType.biometrics);
    });
  });
}
