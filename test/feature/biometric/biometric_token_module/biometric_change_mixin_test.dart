import 'package:evoapp/data/repository/user_repo.dart';
import 'package:evoapp/feature/biometric/biometric_token_module/biometric_change_mixin.dart';
import 'package:evoapp/feature/biometric/biometric_token_module/biometrics_token_module.dart';
import 'package:evoapp/feature/biometric/model/biometric_status_change_notifier.dart';
import 'package:evoapp/feature/biometric/utils/biometric_functions.dart';
import 'package:evoapp/feature/biometric/utils/biometric_status_helper.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/token_utils/evo_jwt_helper_impl.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:local_auth/local_auth.dart';
import 'package:mocktail/mocktail.dart';
import 'package:ts_bio_detect_changed/ts_bio_detect_changed.dart';

import '../../../util/flutter_test_config.dart';
import 'biometric_token_usability_mixin_test.dart';

class MockBiometricsAuthenticate extends Mock implements BiometricsAuthenticate {}

class MockTsBioDetectChanged extends Mock implements TsBioDetectChanged {}

class MockUserRepo extends Mock implements UserRepo {}

class TestBiometricChangeMixin with BiometricChangeMixin {}

class MockContext extends Mock implements BuildContext {}

class MockEvoUtil extends Mock implements EvoUtilFunction {}

class MockBiometricStatusHelper extends Mock implements BiometricStatusHelper {}

class MockLoggingRepo extends Mock implements LoggingRepo {}

class MockBiometricTokenModule extends Mock implements BiometricsTokenModule {}

class MockBiometricStatusChangeNotifier extends Mock implements BiometricStatusChangeNotifier {}

class MockBiometricFunctions extends Mock implements BiometricFunctions {}

void main() {
  const String biometricTokenValue = 'evo_biometric_token_value_test';

  late BiometricsAuthenticate bioAuth;
  late EvoLocalStorageHelper storageHelper;
  late FlutterSecureStorage secureStorage;
  late UserRepo userRepo;
  late TsBioDetectChanged bioDetectChanged;
  late AppState? appState;
  late TestBiometricChangeMixin testBiometricChangeMixin;
  late MockEvoUtil evoUtil;
  late BiometricStatusHelper biometricStatusHelper;
  late MockLoggingRepo loggingRepo;
  late BiometricFunctions mockBioFunctions;

  setUpAll(() {
    WidgetsFlutterBinding.ensureInitialized();

    bioAuth = MockBiometricsAuthenticate();
    userRepo = MockUserRepo();
    bioDetectChanged = MockTsBioDetectChanged();

    secureStorage = testFlutterSecureStorageExecutable();
    storageHelper = testEvoSecureStorageHelperExecutable(secureStorage: secureStorage);

    loggingRepo = MockLoggingRepo();
    getIt.registerLazySingleton<LoggingRepo>(() => loggingRepo);

    testBiometricTokenModuleExecutable(
        bioAuth: bioAuth,
        userRepo: userRepo,
        bioDetectChanged: bioDetectChanged,
        storageHelper: storageHelper,
        jwtHelper: EvoJwtHelperImpl());

    appState = AppState();
    getIt.registerLazySingleton<AppState>(() => appState!);
    evoUtil = MockEvoUtil();
    getIt.registerLazySingleton<EvoUtilFunction>(() => evoUtil);

    biometricStatusHelper = MockBiometricStatusHelper();
    getIt.registerLazySingleton<BiometricStatusHelper>(() => biometricStatusHelper);

    getIt.registerLazySingleton<BiometricFunctions>(() => MockBiometricFunctions());
    mockBioFunctions = getIt.get<BiometricFunctions>();
  });

  tearDownAll(() async {
    await getIt.reset();
    appState = null;
  });

  setUp(() {
    when(() => mockBioFunctions.handleBioError(
          bioError: any(named: 'bioError'),
          onActionWhenDismiss: any(named: 'onActionWhenDismiss'),
        )).thenAnswer((_) async {});
  });

  tearDown(() {
    reset(mockBioFunctions);
  });

  group('check checkBiometricChange()', () {
    setUp(() async {
      testBiometricChangeMixin = TestBiometricChangeMixin();

      await storageHelper.setBiometricToken(biometricTokenValue);
      await storageHelper.setBiometricAuthenticator(true);
    });

    tearDown(() async {
      await storageHelper.deleteAllData();
      reset(bioDetectChanged);
      reset(bioAuth);
    });

    test(
        'checkBiometricChange() return true when isEnableBiometricAuthenticator is enable'
        'getAvailableBiometricType() return empty list and isBiometricChanged() is true', () async {
      /// setup
      await storageHelper.setBiometricAuthenticator(true);
      when(() => bioAuth.getAvailableBiometricType()).thenAnswer((_) async {
        return <BiometricType>[];
      });
      when(() => bioDetectChanged.isBiometricChanged()).thenAnswer((_) async {
        return true;
      });

      final bool isChanged = await testBiometricChangeMixin.checkBiometricChange();
      verify(() => bioAuth.getAvailableBiometricType()).called(1);
      verify(() => bioDetectChanged.isBiometricChanged()).called(1);

      expect(isChanged, true);
    });

    test(
        'checkBiometricChange() return true when isEnableBiometricAuthenticator is enable'
        'getAvailableBiometricType() return empty list and isBiometricChanged() is false',
        () async {
      /// setup
      await storageHelper.setBiometricAuthenticator(true);
      when(() => bioAuth.getAvailableBiometricType()).thenAnswer((_) async {
        return <BiometricType>[];
      });
      when(() => bioDetectChanged.isBiometricChanged()).thenAnswer((_) async {
        return false;
      });

      final bool isChanged = await testBiometricChangeMixin.checkBiometricChange();
      verify(() => bioAuth.getAvailableBiometricType()).called(1);
      verify(() => bioDetectChanged.isBiometricChanged()).called(1);

      expect(isChanged, true);
    });

    test(
        'checkBiometricChange() return true when isEnableBiometricAuthenticator is enable'
        'getAvailableBiometricType() return non-empty list and isBiometricChanged() is true',
        () async {
      /// setup
      await storageHelper.setBiometricAuthenticator(true);
      when(() => bioAuth.getAvailableBiometricType()).thenAnswer((_) async {
        return <BiometricType>[BiometricType.fingerprint];
      });
      when(() => bioDetectChanged.isBiometricChanged()).thenAnswer((_) async {
        return true;
      });

      final bool isChanged = await testBiometricChangeMixin.checkBiometricChange();
      verify(() => bioAuth.getAvailableBiometricType()).called(1);
      verify(() => bioDetectChanged.isBiometricChanged()).called(1);

      expect(isChanged, true);
    });

    test(
        'checkBiometricChange() return false when isEnableBiometricAuthenticator is enable'
        'getAvailableBiometricType() return non-empty list and isBiometricChanged() is false',
        () async {
      /// setup
      await storageHelper.setBiometricAuthenticator(true);
      when(() => bioAuth.getAvailableBiometricType()).thenAnswer((_) async {
        return <BiometricType>[BiometricType.fingerprint];
      });
      when(() => bioDetectChanged.isBiometricChanged()).thenAnswer((_) async {
        return false;
      });

      final bool isChanged = await testBiometricChangeMixin.checkBiometricChange();
      verify(() => bioAuth.getAvailableBiometricType()).called(1);
      verify(() => bioDetectChanged.isBiometricChanged()).called(1);

      expect(isChanged, false);
    });

    test(
        'checkBiometricChange() return false when isEnableBiometricAuthenticator is disabled'
        'getAvailableBiometricType() return empty list and isBiometricChanged() is false',
        () async {
      /// setup
      await storageHelper.setBiometricAuthenticator(false);
      when(() => bioAuth.getAvailableBiometricType()).thenAnswer((_) async {
        return <BiometricType>[];
      });
      when(() => bioDetectChanged.isBiometricChanged()).thenAnswer((_) async {
        return false;
      });

      final bool isChanged = await testBiometricChangeMixin.checkBiometricChange();
      verifyNever(() => bioAuth.getAvailableBiometricType());
      verifyNever(() => bioDetectChanged.isBiometricChanged());

      expect(isChanged, false);
    });

    test(
        'checkBiometricChange() return false when isEnableBiometricAuthenticator is disabled'
        'getAvailableBiometricType() return empty list and isBiometricChanged() is true', () async {
      /// setup
      await storageHelper.setBiometricAuthenticator(false);
      when(() => bioAuth.getAvailableBiometricType()).thenAnswer((_) async {
        return <BiometricType>[];
      });
      when(() => bioDetectChanged.isBiometricChanged()).thenAnswer((_) async {
        return true;
      });

      final bool isChanged = await testBiometricChangeMixin.checkBiometricChange();
      verifyNever(() => bioAuth.getAvailableBiometricType());
      verifyNever(() => bioDetectChanged.isBiometricChanged());

      expect(isChanged, false);
    });

    test(
        'checkBiometricChange() return false when isEnableBiometricAuthenticator is disabled'
        'getAvailableBiometricType() return non-empty list and isBiometricChanged() is false',
        () async {
      /// setup
      await storageHelper.setBiometricAuthenticator(false);
      when(() => bioAuth.getAvailableBiometricType()).thenAnswer((_) async {
        return <BiometricType>[BiometricType.face];
      });
      when(() => bioDetectChanged.isBiometricChanged()).thenAnswer((_) async {
        return false;
      });

      final bool isChanged = await testBiometricChangeMixin.checkBiometricChange();
      verifyNever(() => bioAuth.getAvailableBiometricType());
      verifyNever(() => bioDetectChanged.isBiometricChanged());

      expect(isChanged, false);
    });

    test(
        'checkBiometricChange() return false when isEnableBiometricAuthenticator is disabled'
        'getAvailableBiometricType() return non-empty list and isBiometricChanged() is true',
        () async {
      /// setup
      await storageHelper.setBiometricAuthenticator(false);
      when(() => bioAuth.getAvailableBiometricType()).thenAnswer((_) async {
        return <BiometricType>[BiometricType.face];
      });
      when(() => bioDetectChanged.isBiometricChanged()).thenAnswer((_) async {
        return true;
      });

      final bool isChanged = await testBiometricChangeMixin.checkBiometricChange();
      verifyNever(() => bioAuth.getAvailableBiometricType());
      verifyNever(() => bioDetectChanged.isBiometricChanged());

      expect(isChanged, false);
    });
  });

  group('check handleBiometricChangedIfNeed()', () {
    setUpAll(() {
      registerFallbackValue(MockContext());

      when(() => evoUtil.showDialogBottomSheet(
            content: any(named: 'content'),
            dialogId: EvoDialogId.biometricChangedWarningBottomSheet,
            textPositive: any(named: 'textPositive'),
            textNegative: any(named: 'textNegative'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
          )).thenAnswer((_) => Future<void>.value());

      when(() => evoUtil.getCurrentTimeString()).thenAnswer((_) => DateTime.now().toString());

      when(() => biometricStatusHelper.updateBiometricStatus()).thenAnswer((_) async {
        return Future<void>.value();
      });

      when(() => evoUtil.clearBiometricAuthenticationData())
          .thenAnswer((_) => Future<void>.value());
    });

    setUp(() async {
      testBiometricChangeMixin = TestBiometricChangeMixin();

      await storageHelper.setBiometricToken(biometricTokenValue);
      await storageHelper.setBiometricAuthenticator(true);
      appState?.isUserLogIn = true;
    });

    tearDown(() async {
      await storageHelper.deleteAllData();
      appState?.isUserLogIn = true;
      appState?.biometricStatusChangeNotifier.value = BiometricStatus.usable;
    });

    test(
        'biometricStatusChangeNotifier is update to deviceSettingChanged and show Dialog if Biometric Detected Changed',
        () async {
      /// setup
      when(() => bioAuth.getAvailableBiometricType()).thenAnswer((_) async {
        return <BiometricType>[BiometricType.face];
      });
      when(() => bioDetectChanged.isBiometricChanged()).thenAnswer((_) async {
        return true;
      });

      await testBiometricChangeMixin.handleBiometricChangedIfNeed();

      expect(appState?.biometricStatusChangeNotifier.value, BiometricStatus.deviceSettingChanged);

      verify(() => mockBioFunctions.handleBioError(bioError: any(named: 'bioError'))).called(1);
      verify(() => biometricStatusHelper.updateBiometricStatus()).called(1);
    });

    test(
        'biometricStatusChangeNotifier is update to deviceSettingChanged and do show Dialog when Enrolled Biometric changed',
        () async {
      /// setup
      when(() => bioAuth.getAvailableBiometricType()).thenAnswer((_) async {
        return <BiometricType>[];
      });
      when(() => bioDetectChanged.isBiometricChanged()).thenAnswer((_) async {
        return true;
      });

      await testBiometricChangeMixin.handleBiometricChangedIfNeed();

      expect(appState?.biometricStatusChangeNotifier.value, BiometricStatus.deviceSettingChanged);

      verify(() => mockBioFunctions.handleBioError(bioError: any(named: 'bioError'))).called(1);
      verify(() => biometricStatusHelper.updateBiometricStatus()).called(1);
    });

    test(
        'biometricStatusChangeNotifier is update to deviceSettingChanged and do not show Dialog when user not login',
        () async {
      /// setup
      appState?.isUserLogIn = false;

      when(() => bioAuth.getAvailableBiometricType()).thenAnswer((_) async {
        return <BiometricType>[];
      });
      when(() => bioDetectChanged.isBiometricChanged()).thenAnswer((_) async {
        return true;
      });

      when(() => biometricStatusHelper.updateBiometricStatus()).thenAnswer((_) async {
        return Future<void>.value();
      });

      await testBiometricChangeMixin.handleBiometricChangedIfNeed();

      expect(appState?.biometricStatusChangeNotifier.value, BiometricStatus.deviceSettingChanged);

      verifyNever(() => mockBioFunctions.handleBioError(bioError: any(named: 'bioError')));
      verifyNever(() => biometricStatusHelper.updateBiometricStatus());
    });

    test(
        'biometricStatusChangeNotifier do not update and do not show Dialog if biometric not change',
        () async {
      /// setup
      when(() => bioAuth.getAvailableBiometricType()).thenAnswer((_) async {
        return <BiometricType>[BiometricType.strong];
      });
      when(() => bioDetectChanged.isBiometricChanged()).thenAnswer((_) async {
        return false;
      });

      await testBiometricChangeMixin.handleBiometricChangedIfNeed();

      expect(appState?.biometricStatusChangeNotifier.value, BiometricStatus.usable);

      verifyNever(() => mockBioFunctions.handleBioError(bioError: any(named: 'bioError')));
      verifyNever(() => biometricStatusHelper.updateBiometricStatus());
    });
  });

  group('test showBiometricChangedWarningPopup()', () {
    setUpAll(() {
      registerFallbackValue(MockContext());
      testBiometricChangeMixin = TestBiometricChangeMixin();
    });

    test('show popup if isShowBiometricChangedPopup = false ', () async {
      /// setup
      testBiometricChangeMixin.isShowBiometricChangedPopup = false;

      await testBiometricChangeMixin.showBiometricChangedWarningPopup();

      verify(() => mockBioFunctions.handleBioError(bioError: any(named: 'bioError'))).called(1);
    });

    test('do not show popup if isShowBiometricChangedPopup = true ', () async {
      /// setup
      testBiometricChangeMixin.isShowBiometricChangedPopup = true;
      when(() => evoUtil.showDialogBottomSheet(
            content: any(named: 'content'),
            dialogId: EvoDialogId.biometricChangedWarningBottomSheet,
            textPositive: any(named: 'textPositive'),
            textNegative: any(named: 'textNegative'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
          )).thenAnswer((_) => Future<void>.value());
      await testBiometricChangeMixin.showBiometricChangedWarningPopup();

      verifyNever(() => mockBioFunctions.handleBioError(bioError: any(named: 'bioError')));
    });
  });

  group('test showBiometricChangedPopupAndUpdateStatus function flow', () {
    setUpAll(() {
      registerFallbackValue(MockContext());
      testBiometricChangeMixin = TestBiometricChangeMixin();
      getIt.unregister(instance: biometricStatusHelper);
      biometricStatusHelper = MockBiometricStatusHelper();
      getIt.registerLazySingleton<BiometricStatusHelper>(() => biometricStatusHelper);
    });
    test('make call update biometric status after show popup', () async {
      testBiometricChangeMixin.isShowBiometricChangedPopup = false;

      when(() => biometricStatusHelper.updateBiometricStatus()).thenAnswer((_) async {
        return Future<void>.value();
      });

      await testBiometricChangeMixin.showBiometricChangedPopupAndUpdateStatus();

      verify(() => mockBioFunctions.handleBioError(bioError: any(named: 'bioError'))).called(1);

      verify(() => biometricStatusHelper.updateBiometricStatus()).called(1);
    });
  });

  group('verify order of function called in handleBiometricChangedIfNeed()', () {
    late MockBiometricsTokenModule biometricsTokenModule;
    setUpAll(() {
      registerFallbackValue(MockContext());
      registerFallbackValue(BiometricStatus.notSetup);
      registerFallbackValue(BiometricStatus.usable);
      registerFallbackValue(BiometricStatus.deviceSettingChanged);
      registerFallbackValue(BiometricStatus.biometricTokenUnusable);

      getIt.unregister<BiometricsTokenModule>();
      getIt.registerLazySingleton<BiometricsTokenModule>(() => MockBiometricsTokenModule());
      biometricsTokenModule = getIt.get<BiometricsTokenModule>() as MockBiometricsTokenModule;

      when(() => evoUtil.showDialogBottomSheet(
            content: any(named: 'content'),
            dialogId: EvoDialogId.biometricChangedWarningBottomSheet,
            textPositive: any(named: 'textPositive'),
            textNegative: any(named: 'textNegative'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
          )).thenAnswer((_) => Future<void>.value());

      appState?.isUserLogIn = true;
      appState?.biometricStatusChangeNotifier = MockBiometricStatusChangeNotifier();
    });

    tearDown(() {
      reset(biometricsTokenModule);
      reset(appState?.biometricStatusChangeNotifier);
    });

    setUp(() async {
      testBiometricChangeMixin = TestBiometricChangeMixin();
    });

    test(
        'make sure order of function called is [disableBiometricAuthenticatorFeature(),'
        ' biometricStatusChangeNotifier.update()] if biometricChanged is trigger', () async {
      /// setup
      when(() => biometricsTokenModule.isEnableBiometricAuthenticator())
          .thenAnswer((_) async => true);
      when(() => biometricsTokenModule.hasEnrolledBiometrics()).thenAnswer((_) async => false);
      when(() => biometricsTokenModule.getBiometricChanged()).thenAnswer((_) async => true);

      when(() => biometricsTokenModule.disableBiometricAuthenticatorFeature())
          .thenAnswer((_) async => Future<void>.value());

      when(() => biometricStatusHelper.updateBiometricStatus()).thenAnswer((_) async {
        return Future<void>.value();
      });

      when(() => appState?.biometricStatusChangeNotifier.update(any())).thenAnswer((_) {});

      /// action
      await testBiometricChangeMixin.handleBiometricChangedIfNeed();

      /// assert
      verifyInOrder(<VoidCallback>[
        () => biometricsTokenModule.disableBiometricAuthenticatorFeature(),
        () => appState?.biometricStatusChangeNotifier.update(BiometricStatus.deviceSettingChanged),
      ]);
    });
  });

  group('verify disableBiometric', () {
    late MockBiometricsTokenModule biometricsTokenModule;
    setUpAll(() {
      getIt.unregister<BiometricsTokenModule>();
      getIt.registerLazySingleton<BiometricsTokenModule>(() => MockBiometricsTokenModule());
      biometricsTokenModule = getIt.get<BiometricsTokenModule>() as MockBiometricsTokenModule;
    });

    tearDown(() {
      reset(biometricsTokenModule);
    });

    setUp(() async {
      testBiometricChangeMixin = TestBiometricChangeMixin();
    });

    test('make sure disableBiometricAuthenticatorFeature() is called ', () async {
      /// arrange
      when(() => biometricsTokenModule.disableBiometricAuthenticatorFeature())
          .thenAnswer((_) async => Future<void>.value());

      /// action
      await testBiometricChangeMixin.disableBiometric();

      /// verify
      verify(() => biometricsTokenModule.disableBiometricAuthenticatorFeature()).called(1);
    });
  });
}
