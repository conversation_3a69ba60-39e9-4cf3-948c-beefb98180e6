import 'package:evoapp/data/repository/user_repo.dart';
import 'package:evoapp/data/response/biometric_token_entity.dart';
import 'package:evoapp/feature/biometric/base/ext_biometric_token_entity.dart';
import 'package:evoapp/feature/biometric/biometric_token_module/biometric_challenge_handler.dart';
import 'package:evoapp/feature/biometric/biometric_token_module/biometric_token_action.dart';
import 'package:evoapp/feature/biometric/biometric_token_module/biometrics_token_module.dart';
import 'package:evoapp/feature/biometric/mock/mock_biometric_token_use_case.dart';
import 'package:evoapp/feature/biometric/model/biometric_status_change_notifier.dart';
import 'package:evoapp/feature/biometric/utils/bio_auth_result.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/feature/logging/evo_logging_event.dart';
import 'package:evoapp/prepare_for_app_initiation.dart' show AppState;
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/navigator/evo_router_navigator.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/token_utils/evo_jwt_helper_impl.dart';
import 'package:evoapp/util/token_utils/jwt_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:ts_bio_detect_changed/ts_bio_detect_changed.dart';

import '../../../util/test_util.dart';

class MockBiometricsAuthenticate extends Mock implements BiometricsAuthenticate {}

class MockTsBioDetectChanged extends Mock implements TsBioDetectChanged {}

class MockUserRepo extends Mock implements UserRepo {}

class MockLoggingRepo extends Mock implements LoggingRepo {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockBiometricStatusChangeNotifier extends Mock implements BiometricStatusChangeNotifier {}

class MockAppState extends Mock implements AppState {}

class MockBiometricTokenModuleCallback extends Mock implements BiometricTokenModuleCallback {}

class MockEvoNavigator extends Mock implements EvoRouterNavigator {}

class MockContext extends Mock implements BuildContext {}

class MockBiometricTokenChallengeHandler extends Mock implements BiometricTokenChallengeHandler {}

class MockJwtHelper extends Mock implements EvoJwtHelperImpl {}

class SpyBiometricsTokenModule extends BiometricsTokenModule {
  SpyBiometricsTokenModule({
    required super.biometricsAuthenticate,
    required super.userRepo,
    required super.secureStorageHelper,
    required super.bioDetectChanged,
    required super.jwtHelper,
    super.challengeHandler,
  });

  int enableBiometricAuthenticatorFeatureCalled = 0;
  bool enableBiometricAuthenticatorFeatureShouldCallSuper = true;

  int handleBiometricsTokenCalled = 0;
  bool handleBiometricsTokenShouldCallSuper = true;

  void reset() {
    enableBiometricAuthenticatorFeatureCalled = 0;
    enableBiometricAuthenticatorFeatureShouldCallSuper = true;
    handleBiometricsTokenCalled = 0;
    handleBiometricsTokenShouldCallSuper = true;
  }

  @override
  Future<void> enableBiometricAuthenticatorFeature(String? biometricToken) async {
    enableBiometricAuthenticatorFeatureCalled += 1;
    if (enableBiometricAuthenticatorFeatureShouldCallSuper) {
      return super.enableBiometricAuthenticatorFeature(biometricToken);
    }
  }

  @override
  Future<void> handleBiometricsToken(BiometricTokenAction action) async {
    handleBiometricsTokenCalled += 1;
    if (handleBiometricsTokenShouldCallSuper) {
      return super.handleBiometricsToken(action);
    }
  }
}

void main() {
  late BiometricsAuthenticate bioAuth;
  late EvoLocalStorageHelper storageHelper;
  late AppState mockAppState;
  late UserRepo userRepo;
  late TsBioDetectChanged bioDetectChanged;
  late MockLoggingRepo loggingRepo;
  late BiometricsTokenModule module;
  late EvoUtilFunction mockEvoUtilFunction;
  final BiometricStatusChangeNotifier mockBioStatusNotifier = MockBiometricStatusChangeNotifier();
  late BiometricTokenChallengeHandler mockChallengeHandler;

  const String mockBioToken = 'mock_biometric_token';

  setUpAll(() {
    WidgetsFlutterBinding.ensureInitialized();

    loggingRepo = MockLoggingRepo();
    getIt.registerLazySingleton<LoggingRepo>(() => loggingRepo);

    getIt.registerLazySingleton<EvoUtilFunction>(() => MockEvoUtilFunction());
    mockEvoUtilFunction = getIt.get<EvoUtilFunction>();

    getIt.registerLazySingleton<AppState>(() => MockAppState());
    mockAppState = getIt.get<AppState>();
    getIt.registerLazySingleton<UserRepo>(() => MockUserRepo());
    userRepo = getIt.get<UserRepo>();

    registerFallbackValue(ChallengeType.none);
    registerFallbackValue(BiometricTokenModuleErrorType.unknown);
    registerFallbackValue(BiometricStatus.notSetup);
  });

  verifyBioDataSaved(String token) {
    verify(() => storageHelper.setBiometricToken(token)).called(1);
    verify(() => storageHelper.setBiometricAuthenticator(true)).called(1);
    verify(() => bioDetectChanged.initialize()).called(1);
    final BiometricStatus capturedStatus =
        verify(() => mockBioStatusNotifier.update(captureAny())).captured.first;
    expect(
      capturedStatus,
      BiometricStatus.usable,
    );
  }

  setUp(() {
    bioAuth = MockBiometricsAuthenticate();
    bioDetectChanged = MockTsBioDetectChanged();
    storageHelper = MockEvoLocalStorageHelper();
    mockChallengeHandler = MockBiometricTokenChallengeHandler();

    when(() => mockAppState.biometricStatusChangeNotifier).thenReturn(
      mockBioStatusNotifier,
    );
    when(
      () => mockEvoUtilFunction.showHudLoading(),
    ).thenAnswer((_) => Future<void>.value());

    when(
      () => mockEvoUtilFunction.hideHudLoading(),
    ).thenAnswer((_) => Future<void>.value());

    when(() => storageHelper.setBiometricToken(any())).thenAnswer((_) async {
      return Future<void>.value();
    });
    when(() => storageHelper.setBiometricAuthenticator(any())).thenAnswer((_) {
      return Future<void>.value();
    });
    when(() => storageHelper.delete(key: any(named: 'key'))).thenAnswer((_) async {
      return Future<void>.value();
    });

    when(() => bioDetectChanged.initialize()).thenAnswer((_) => Future<void>.value());

    when(() => mockBioStatusNotifier.update(any())).thenAnswer((_) {});

    module = BiometricsTokenModule(
      biometricsAuthenticate: bioAuth,
      userRepo: userRepo,
      secureStorageHelper: storageHelper,
      bioDetectChanged: bioDetectChanged,
      jwtHelper: EvoJwtHelperImpl(),
      challengeHandler: mockChallengeHandler,
    );
  });

  tearDown(() {
    reset(storageHelper);
    reset(mockBioStatusNotifier);
    reset(bioDetectChanged);
    reset(mockEvoUtilFunction);
  });

  test('Constructor should create BiometricTokenChallengeHandler if not provided', () {
    final BiometricsTokenModule module = BiometricsTokenModule(
      biometricsAuthenticate: bioAuth,
      userRepo: userRepo,
      secureStorageHelper: storageHelper,
      bioDetectChanged: bioDetectChanged,
      jwtHelper: EvoJwtHelperImpl(),
    );
    expect(module.challengeHandler, isNotNull);
  });

  group('test getBiometricChanged()', () {
    test('work correctly if TSBioDetectChanged.biometricChange() return TRUE', () async {
      when(() => bioDetectChanged.isBiometricChanged()).thenAnswer((_) async {
        return true;
      });

      final bool isResult = await module.getBiometricChanged();

      expect(isResult, true);
    });

    test('work correctly if TSBioDetectChanged.biometricChange() return NULL', () async {
      when(() => bioDetectChanged.isBiometricChanged()).thenAnswer((_) async {
        return null;
      });

      final bool isResult = await module.getBiometricChanged();

      expect(isResult, false);
    });

    test('work correctly if TSBioDetectChanged.biometricChange() throw EXCEPTION', () async {
      when(() => bioDetectChanged.isBiometricChanged()).thenThrow(Exception());
      when(() => loggingRepo.logErrorEvent(
            errorType: any(named: 'errorType'),
            args: any(named: 'args'),
          )).thenAnswer((_) => Future<void>.value());

      final bool isResult = await module.getBiometricChanged();

      expect(isResult, false);

      verify(() => loggingRepo.logErrorEvent(
          errorType: EvoEventType.biometrics.name, args: any(named: 'args'))).called(1);
    });

    test('work correctly if TSBioDetectChanged.biometricChange() throw ERROR', () async {
      when(() => bioDetectChanged.isBiometricChanged()).thenThrow(Error());
      when(() => loggingRepo.logErrorEvent(
          errorType: any(named: 'errorType'),
          args: any(named: 'args'))).thenAnswer((_) => Future<void>.value());

      final bool isResult = await module.getBiometricChanged();

      expect(isResult, false);

      verify(() => loggingRepo.logErrorEvent(
          errorType: EvoEventType.biometrics.name, args: any(named: 'args'))).called(1);
    });
  });

  group('verify disableBiometricAuthenticatorFeature() method', () {
    const String timeNow = '2023-08-17 10:00:00.000';

    setUpAll(() async {
      when(() => mockEvoUtilFunction.clearBiometricAuthenticationData()).thenAnswer((_) {
        return Future<void>.value();
      });
    });

    test('verify call local storage', () async {
      await module.disableBiometricAuthenticatorFeature();
      verify(() => mockEvoUtilFunction.clearBiometricAuthenticationData()).called(1);
    });
  });

  group('verify enable() method', () {
    late MockBiometricTokenModuleCallback mockCallback;

    setUp(() {
      mockCallback = MockBiometricTokenModuleCallback();
      when(() => mockCallback.onRetrieveTokenSuccess()).thenAnswer((_) {});
      when(() => mockCallback.onRetrieveTokenError(
          type: any(named: 'type'),
          userMessage: any(named: 'userMessage'),
          error: any(named: 'error'),
          bioError: any(named: 'bioError'))).thenAnswer((_) {});

      when(() => userRepo.getBiometricTokenByPin(
              pin: any(named: 'pin'), mockConfig: any(named: 'mockConfig')))
          .thenAnswer((_) async =>
              Future<BiometricTokenEntity>.value(BiometricTokenEntity.fromBaseResponse(BaseResponse(
                statusCode: CommonHttpClient.SUCCESS,
                response: await TestUtil.getResponseMock(getMockBiometricTokenFileNameByCase(
                  MockTestBiometricTokenUseCase.getBiometricTokenSuccess,
                )),
              ))));

      when(
        () => bioAuth.authenticate(
          localizedReason: any(named: 'localizedReason'),
          cancelButton: any(named: 'cancelButton'),
        ),
      ).thenAnswer((_) => Future<BioAuthResult>.value(BioAuthResult.success()));
    });

    test('should handle correctly when biometric authenticated', () async {
      await module.enable(
        callback: mockCallback,
      );

      expect(
        verify(() => userRepo.getBiometricTokenByPin(
              pin: captureAny(named: 'pin'),
              mockConfig: any(named: 'mockConfig'),
            )).captured.single,
        null,
      );
      verify(() => bioAuth.authenticate(
            localizedReason: any(named: 'localizedReason'),
            cancelButton: any(named: 'cancelButton'),
          )).called(1);
      verify(() => mockCallback.onRetrieveTokenSuccess()).called(1);
      verifyBioDataSaved(mockBioToken);
    });

    test('should call callback.onError() when biometric unauthorized', () async {
      const BioAuthError bioError = BioAuthError.notEnrolled;
      when(
        () => bioAuth.authenticate(
          localizedReason: any(named: 'localizedReason'),
          cancelButton: any(named: 'cancelButton'),
        ),
      ).thenAnswer((_) => Future<BioAuthResult>.value(BioAuthResult.error(bioError)));

      await module.enable(
        callback: mockCallback,
      );

      final VerificationResult verification = verify(() => mockCallback.onRetrieveTokenError(
          type: captureAny(named: 'type'),
          userMessage: any(named: 'userMessage'),
          error: any(named: 'error'),
          bioError: captureAny(named: 'bioError')))
        ..called(1);

      expect(verification.captured[0], BiometricTokenModuleErrorType.biometrics);
      expect(verification.captured[1], bioError);
    });

    test('should call callback.onError() when getBiometricToken() return status code != 200',
        () async {
      when(() => userRepo.getBiometricTokenByPin(
              pin: any(named: 'pin'), mockConfig: any(named: 'mockConfig')))
          .thenAnswer((_) async =>
              Future<BiometricTokenEntity>.value(BiometricTokenEntity.fromBaseResponse(BaseResponse(
                statusCode: CommonHttpClient.BAD_REQUEST,
                response: <String, dynamic>{},
              ))));

      await module.enable(callback: mockCallback);

      final VerificationResult verification = verify(() => mockCallback.onRetrieveTokenError(
          type: captureAny(named: 'type'),
          userMessage: any(named: 'userMessage'),
          error: any(named: 'error'),
          bioError: captureAny(named: 'bioError')))
        ..called(1);

      expect(verification.captured[0], BiometricTokenModuleErrorType.apiError);
    });

    test('should handle challenge when entity has challenge type', () async {
      when(() => userRepo.getBiometricTokenByPin(
              pin: any(named: 'pin'), mockConfig: any(named: 'mockConfig')))
          .thenAnswer((_) async =>
              Future<BiometricTokenEntity>.value(BiometricTokenEntity.fromBaseResponse(BaseResponse(
                statusCode: CommonHttpClient.SUCCESS,
                response: await TestUtil.getResponseMock(getMockBiometricTokenFileNameByCase(
                  MockTestBiometricTokenUseCase.getBiometricTokenWithChallengeType,
                )),
              ))));

      when(() => mockChallengeHandler.handle(challengeType: any(named: 'challengeType')))
          .thenAnswer(
        (_) => Future<void>.value(),
      );

      await module.enable();
      verify(() => mockChallengeHandler.handle(challengeType: ChallengeType.pin)).called(1);
    });
  });

  group('verify enableBiometricAuthenticatorFeature', () {
    test('should save data properly', () async {
      await module.enableBiometricAuthenticatorFeature(mockBioToken);
      verifyBioDataSaved(mockBioToken);
      verify(() => mockEvoUtilFunction.showHudLoading()).called(1);
      verify(() => mockEvoUtilFunction.hideHudLoading()).called(1);
    });

    test('should do nothing if token is null', () async {
      await module.enableBiometricAuthenticatorFeature(null);
      verifyNever(() => storageHelper.setBiometricToken(any()));
      verifyNever(() => storageHelper.setBiometricAuthenticator(any()));
      verifyNever(() => bioDetectChanged.initialize());
      verifyNever(() => mockBioStatusNotifier.update(captureAny()));
    });
  });

  test('isBiometricTokenUsable() should get biometric token and check', () async {
    final JwtHelper jwtHelper = MockJwtHelper();
    final BiometricsTokenModule module = BiometricsTokenModule(
      biometricsAuthenticate: bioAuth,
      userRepo: userRepo,
      secureStorageHelper: storageHelper,
      bioDetectChanged: bioDetectChanged,
      jwtHelper: jwtHelper,
    );

    when(() => storageHelper.getBiometricToken()).thenAnswer((_) async => mockBioToken);
    when(() => jwtHelper.isCanUse(mockBioToken)).thenReturn(false);

    final bool result = await module.isBiometricTokenUsable();

    verify(() => storageHelper.getBiometricToken()).called(1);
    verify(() => jwtHelper.isCanUse(mockBioToken)).called(1);
    expect(result, isFalse);
  });

  group('handleBiometricsToken()', () {
    late MockBiometricTokenModuleCallback callback;
    late BiometricTokenChallengeHandler challengeHandler;
    late SpyBiometricsTokenModule module;

    setUp(() {
      callback = MockBiometricTokenModuleCallback();
      challengeHandler = MockBiometricTokenChallengeHandler();
      module = SpyBiometricsTokenModule(
        biometricsAuthenticate: MockBiometricsAuthenticate(),
        userRepo: MockUserRepo(),
        secureStorageHelper: MockEvoLocalStorageHelper(),
        bioDetectChanged: MockTsBioDetectChanged(),
        jwtHelper: MockJwtHelper(),
        challengeHandler: challengeHandler,
      );
      module.callback = callback;

      module.handleBiometricsTokenShouldCallSuper = true;
      module.enableBiometricAuthenticatorFeatureShouldCallSuper = false;
    });

    tearDown(() {
      module.reset();
    });

    test('on ActionSuccess() should call onRetrieveTokenSuccess()', () async {
      await module.handleBiometricsToken(ActionSuccess(mockBioToken));

      verify(() => callback.onRetrieveTokenSuccess()).called(1);
      expect(module.enableBiometricAuthenticatorFeatureCalled, 1);
    });

    test('on ActionChallenge() should call handle challenge', () async {
      when(() => challengeHandler.handle(challengeType: any(named: 'challengeType')))
          .thenAnswer((_) async {});

      final ChallengeType type = ChallengeType.pin;
      await module.handleBiometricsToken(ActionChallenge(type));

      verify(() => challengeHandler.handle(challengeType: type)).called(1);
    });

    test('on ActionError() should call onRetrieveTokenError()', () async {
      final BiometricTokenModuleErrorType errorType = BiometricTokenModuleErrorType.biometrics;
      await module.handleBiometricsToken(ActionError(errorType: errorType));

      verify(() => callback.onRetrieveTokenError(
            type: errorType,
            userMessage: any(named: 'userMessage', that: isA<String?>()),
          )).called(1);
    });

    test('on ActionApiError() should call onRetrieveTokenError()', () async {
      final ErrorUIModel error = ErrorUIModel();
      await module.handleBiometricsToken(ActionApiError(error: error));

      verify(() => callback.onRetrieveTokenError(
            type: BiometricTokenModuleErrorType.apiError,
            userMessage: any(named: 'userMessage', that: isA<String?>()),
            error: error,
          )).called(1);
    });

    test('on ActionBiometricError() should call onRetrieveTokenError()', () async {
      final BioAuthError bioError = BioAuthError.notEnrolled;
      await module.handleBiometricsToken(ActionBiometricError(error: bioError));

      verify(() => callback.onRetrieveTokenError(
            type: BiometricTokenModuleErrorType.biometrics,
            bioError: bioError,
          )).called(1);
    });
  });

  group('BiometricChallengeCallback', () {
    late SpyBiometricsTokenModule module;

    setUp(() {
      module = SpyBiometricsTokenModule(
        biometricsAuthenticate: MockBiometricsAuthenticate(),
        userRepo: MockUserRepo(),
        secureStorageHelper: MockEvoLocalStorageHelper(),
        bioDetectChanged: MockTsBioDetectChanged(),
        jwtHelper: MockJwtHelper(),
        challengeHandler: MockBiometricTokenChallengeHandler(),
      );

      module.handleBiometricsTokenShouldCallSuper = false;
    });

    tearDown(() {
      module.reset();
    });

    test('onBioChallengeError() should call handleBiometricsToken()', () async {
      module.onBioChallengeError(ErrorUIModel());

      expect(module.handleBiometricsTokenCalled, 1);
    });

    test('onBioChallengeSuccess() should call handleBiometricsToken()', () async {
      module.onBioChallengeSuccess('token');

      expect(module.handleBiometricsTokenCalled, 1);
    });

    test('onBioChallengeCancel() should set isProcessing to false', () async {
      module.onBioChallengeCancel();

      expect(module.isProcessing, isFalse);
    });
  });
}
