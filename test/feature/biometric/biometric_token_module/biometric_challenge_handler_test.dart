import 'package:evoapp/feature/biometric/base/ext_biometric_token_entity.dart';
import 'package:evoapp/feature/biometric/biometric_token_module/biometric_challenge_handler.dart';
import 'package:evoapp/feature/biometric/biometric_token_module/biometric_token_action.dart';
import 'package:evoapp/feature/biometric/biometric_token_module/biometrics_token_module.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';

class MockBiometricTokenModuleCallback extends Mock implements BiometricChallengeCallback {}

class MockBuildContext extends Mock implements BuildContext {}

class MockCommonNavigator extends Mock implements CommonNavigator {}

class MockOnErrorCallback extends Mock {
  void call(ActionError error);
}

void main() {
  late BiometricTokenChallengeHandler handler;
  late MockBiometricTokenModuleCallback mockCallback;
  late MockOnErrorCallback mockOnErrorCb;
  late BuildContext mockNavigatorContext;
  late CommonNavigator commonNavigator;

  setUpAll(() {
    getIt.registerLazySingleton<CommonNavigator>(() => MockCommonNavigator());
    registerFallbackValue(ActionError(errorType: BiometricTokenModuleErrorType.unknown));

    commonNavigator = getIt.get<CommonNavigator>();
    mockNavigatorContext = MockBuildContext();
    setUpMockGlobalKeyProvider(mockNavigatorContext);

    registerFallbackValue(mockNavigatorContext);
  });

  setUp(() {
    mockCallback = MockBiometricTokenModuleCallback();
    mockOnErrorCb = MockOnErrorCallback();

    handler = BiometricTokenChallengeHandler(
      callback: mockCallback,
      onError: mockOnErrorCb.call,
    );

    when(() => commonNavigator.pushNamed(any(), any(), extra: any(named: 'extra')))
        .thenAnswer((_) async {});
  });

  group('verify handle() method', () {
    test('should navigate [ConfirmPinScreen]  when challengeType = pin ', () {
      handler.handle(challengeType: ChallengeType.pin);

      verify(() => commonNavigator.pushNamed(any(), Screen.confirmPinScreen.name,
          extra: any(named: 'extra'))).called(1);
    });

    test('should call onError() method when challengeType is unsupported', () async {
      when(() => mockOnErrorCb.call(any())).thenAnswer((_) {});
      handler.handle(
        challengeType: ChallengeType.none,
      );

      final ActionError captured = verify(() => mockOnErrorCb.call(captureAny())).captured.first;

      expect(
        captured.errorType,
        BiometricTokenModuleErrorType.noSupportExtraChallenge,
      );
    });
  });
}
