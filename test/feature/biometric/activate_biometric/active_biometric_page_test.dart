import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/biometric/activate_biometric/active_biometric_cubit.dart';
import 'package:evoapp/feature/biometric/activate_biometric/active_biometric_page.dart';
import 'package:evoapp/feature/biometric/activate_biometric/active_biometric_state.dart';
import 'package:evoapp/feature/biometric/biometric_token_module/biometrics_token_module.dart';
import 'package:evoapp/feature/biometric/utils/bio_auth_result.dart';
import 'package:evoapp/feature/biometric/utils/biometric_functions.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/widget/appbar/evo_support_appbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';

class MockCb extends Mock {
  void call();
}

class MockActiveBiometricCubit extends MockCubit<ActiveBiometricState>
    implements ActiveBiometricCubit {}

class MockBiometricFunctions extends Mock implements BiometricFunctions {}

class MockEvoSnackBar extends Mock implements EvoSnackBar {}

void main() {
  late BiometricsTokenModule mockBioTokenModule;
  late ActiveBiometricCubit mockCubit;
  late MockCb mockCb;
  late CommonImageProvider mockImageProvider;
  late BiometricFunctions mockBioFn;
  final GlobalKey<ActiveBiometricScreenState> globalKey = GlobalKey<ActiveBiometricScreenState>();
  late EvoSnackBar mockEvoSnackBar;

  setUpAll(() {
    initConfigEvoPageStateBase();
    setUpMockConfigEvoPageStateBase();

    mockBioTokenModule = getIt.get<BiometricsTokenModule>();
    mockImageProvider = getIt.get<CommonImageProvider>();
    when(() => mockImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          fit: any(named: 'fit'),
        )).thenReturn(SizedBox());

    getIt.registerSingleton<BiometricFunctions>(MockBiometricFunctions());
    mockBioFn = getIt.get<BiometricFunctions>();

    getIt.registerSingleton<EvoSnackBar>(MockEvoSnackBar());
    mockEvoSnackBar = getIt.get<EvoSnackBar>();
    registerFallbackValue(SnackBarType.success);
  });

  setUp(() {
    mockCubit = MockActiveBiometricCubit();
    when(() => mockCubit.state).thenReturn(ActiveBiometricInitState());
    mockCb = MockCb();

    when(() => mockBioFn.handleBioError(
        bioError: any(named: 'bioError'),
        onActionWhenDismiss: any(
          named: 'onActionWhenDismiss',
        ))).thenAnswer((_) async {});

    when(() => mockEvoSnackBar.show(
          any(),
          typeSnackBar: any(named: 'typeSnackBar'),
          durationInMilliSec: any(named: 'durationInMilliSec'),
          description: any(named: 'description'),
          marginBottomRatio: any(named: 'marginBottomRatio'),
        )).thenAnswer((_) async {
      return null;
    });
  });

  test('ActiveBiometricScreenArg should hold correct data', () {
    final MockCb cb = MockCb();
    final ActiveBiometricScreenArg arg = ActiveBiometricScreenArg(
      onSuccess: cb.call,
    );

    expect(arg.onSuccess, cb.call);
  });

  test('ActiveBiometricScreen should have correct routeSettings', () {
    final ActiveBiometricScreen screen = ActiveBiometricScreen(onSuccess: () {});

    expect(screen.routeSettings.name, Screen.activeBiometric.routeName);
  });

  test('ActiveBiometricScreen should navigate correctly on pushNamed', () async {
    await ActiveBiometricScreen.pushNamed(onSuccess: mockCb.call);

    verify(() => mockNavigatorContext.pushNamed(
          Screen.activeBiometric.name,
          extra: any(named: 'extra'),
        )).called(1);
  });

  test('ActiveBiometricScreen should navigate correctly on goNamed', () async {
    final MockCb cb = MockCb();
    await ActiveBiometricScreen.goNamed(onSuccess: cb.call);

    verify(() => mockNavigatorContext.goNamed(
          Screen.activeBiometric.name,
          extra: any(named: 'extra'),
        )).called(1);
  });

  buildWidget(WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: BlocProvider<ActiveBiometricCubit>.value(
            value: mockCubit,
            child: ActiveBiometricScreen(
              key: globalKey,
              onSuccess: mockCb.call,
            )),
      ),
    );
  }

  testWidgets('test render UI ActiveBiometricScreen should render correct UI',
      (WidgetTester tester) async {
    await buildWidget(tester);
    expect(find.byType(EvoSupportAppbar), findsOneWidget);
    expect(find.text(EvoStrings.activeBiometricTitle), findsOneWidget);
    expect(find.text(EvoStrings.activeBiometricDesc), findsOneWidget);
    verify(() => mockImageProvider.asset(captureAny(), height: any(named: 'height'))).called(1);
    expect(find.text(EvoStrings.ctaYes), findsOneWidget);
    expect(find.text(EvoStrings.ctaSkip), findsOneWidget);

    await tester.tap(find.text(EvoStrings.ctaSkip));
    await tester.pump();

    verify(() => mockNavigatorContext.pop(result: any(named: 'result'))).called(1);
    verify(() => mockCb.call()).called(1);
  });

  group('test enable biometric', () {
    tapYes(WidgetTester tester) async {
      await tester.tap(find.text(EvoStrings.ctaYes));
      await tester.pump();
    }

    testWidgets('should enable biometricsTokenModule error hasEnrolledBiometrics is false',
        (WidgetTester tester) async {
      when(() => mockCubit.checkEnrolledBiometric()).thenAnswer((_) async {});
      whenListen(
          mockCubit,
          Stream.fromIterable(<ActiveBiometricState>[
            ActiveBiometricInitState(),
            EnrolledBiometricState(
              hasEnrolledBiometrics: false,
            ),
          ]));

      await buildWidget(tester);
      await tapYes(tester);

      verify(() => mockBioFn.handleBioError(
            bioError: BioAuthError.notEnrolled,
            onActionWhenDismiss: any(named: 'onActionWhenDismiss'),
          )).called(1);
    });

    testWidgets('should enable biometricTokenModule when hasEnrolledBiometrics',
        (WidgetTester tester) async {
      when(() => mockCubit.checkEnrolledBiometric()).thenAnswer((_) async {});
      when(() => mockBioTokenModule.enable(
            callback: any(named: 'callback'),
          )).thenAnswer((_) async {});

      whenListen(
          mockCubit,
          Stream.fromIterable(<ActiveBiometricState>[
            ActiveBiometricInitState(),
            EnrolledBiometricState(
              hasEnrolledBiometrics: true,
            ),
          ]));

      await buildWidget(tester);
      await tapYes(tester);

      verify(() => mockBioTokenModule.enable(callback: any(named: 'callback'))).called(1);
    });
  });

  group('test onRetrieve biometric token ', () {
    testWidgets('should handle correctly when onRetrieveTokenSuccess', (WidgetTester tester) async {
      await buildWidget(tester);

      globalKey.currentState?.onRetrieveTokenSuccess();

      verify(() => mockNavigatorContext.pop(result: any(named: 'result'))).called(1);
      verify(() => mockCb.call()).called(1);
    });

    testWidgets(
        'should handle correctly when onRetrieveTokenError with BiometricTokenModuleErrorType.biometrics',
        (WidgetTester tester) async {
      final BioAuthError bioError = BioAuthError.unknown;
      await buildWidget(tester);

      globalKey.currentState?.onRetrieveTokenError(
        type: BiometricTokenModuleErrorType.biometrics,
        bioError: bioError,
      );

      verify(() => mockBioFn.handleBioError(bioError: bioError)).called(1);
    });

    testWidgets(
        'should handle correctly when onRetrieveTokenError with BiometricTokenModuleErrorType.apiError',
        (WidgetTester tester) async {
      await buildWidget(tester);

      globalKey.currentState?.onRetrieveTokenError(
        type: BiometricTokenModuleErrorType.apiError,
      );

      verify(() => mockEvoSnackBar.show(
            any(),
            typeSnackBar: SnackBarType.error,
            durationInMilliSec: any(named: 'durationInMilliSec'),
            description: any(named: 'description'),
            marginBottomRatio: any(named: 'marginBottomRatio'),
          )).called(1);
    });

    testWidgets(
        'should handle correctly when onRetrieveTokenError with BiometricTokenModuleErrorType.unknown',
        (WidgetTester tester) async {
      await buildWidget(tester);

      globalKey.currentState?.onRetrieveTokenError(
        type: BiometricTokenModuleErrorType.unknown,
      );

      verify(() => mockEvoSnackBar.show(
            any(),
            typeSnackBar: SnackBarType.error,
            durationInMilliSec: any(named: 'durationInMilliSec'),
            description: any(named: 'description'),
            marginBottomRatio: any(named: 'marginBottomRatio'),
          )).called(1);
    });
  });
}
