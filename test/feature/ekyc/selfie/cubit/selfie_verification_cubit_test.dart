import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/request/activate_account_request.dart';
import 'package:evoapp/data/request/reset_pin_request.dart';
import 'package:evoapp/data/response/account_activation_entity.dart';
import 'package:evoapp/data/response/reset_pin_entity.dart';
import 'package:evoapp/feature/ekyc/selfie/cubit/selfie_error_factory.dart';
import 'package:evoapp/feature/ekyc/selfie/cubit/selfie_verification_cubit.dart';
import 'package:evoapp/feature/ekyc/selfie/model/selfie_error_ui_model.dart';
import 'package:evoapp/feature/ekyc/selfie/selfie_verification_screen.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_error_reason.dart';
import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_liveness_mode.dart';
import 'package:flutter_common_package/feature/ekyc/facial_verification/facial_verification_handler.dart';
import 'package:flutter_common_package/feature/ekyc/facial_verification/models/facial_verification_initialize_args.dart';
import 'package:flutter_common_package/feature/ekyc/facial_verification/models/facial_verification_initialize_result.dart';
import 'package:flutter_common_package/feature/ekyc/facial_verification/models/facial_verification_start_capturing_args.dart';
import 'package:flutter_common_package/feature/ekyc/facial_verification/models/facial_verification_start_capturing_result.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../constant.dart';

class MockFacialVerificationHandler extends Mock implements FacialVerificationHandler {}

class MockSelfieVerificationErrorFactory extends Mock implements SelfieVerificationErrorFactory {}

class MockAuthRepo extends Mock implements AuthenticationRepo {}

void main() {
  late SelfieVerificationCubit cubit;
  late FacialVerificationHandler mockFacialHandler;
  late SelfieVerificationErrorFactory mockErrorFactory;
  final String mockSessionToken = 'mock-session-token';
  late AuthenticationRepo mockAuthRepo;

  final List<String> mockImageIds = <String>['imageId'];
  final List<String> mockFileIds = <String>['videoId'];

  setUpAll(() {
    registerFallbackValue(FacialVerificationInitializeArgs(
      sessionToken: mockSessionToken,
      languageCode: 'en',
    ));
    registerFallbackValue(FacialVerificationStartCapturingArgs(
      livenessMode: EkycBridgeLivenessMode.none,
    ));
    registerFallbackValue(InitializeResetPinRequest(phoneNumber: ''));
    registerFallbackValue(ActivateAccountVerifySelfieRequest());
  });

  setUp(() {
    mockFacialHandler = MockFacialVerificationHandler();
    when(() => mockFacialHandler.initialize(args: any(named: 'args'))).thenAnswer((_) async {
      return FacialVerificationInitializeSuccessResult();
    });
    mockErrorFactory = MockSelfieVerificationErrorFactory();
    when(() => mockErrorFactory.create(
          entity: any(named: 'entity'),
          bridgeError: any(named: 'bridgeError'),
        )).thenReturn(SelfieErrorUiModel(
      actionType: ErrorActionType.ignore,
    ));
    mockAuthRepo = MockAuthRepo();

    cubit = SelfieVerificationCubit(
      selfieHandler: mockFacialHandler,
      errorFactory: mockErrorFactory,
      authRepo: mockAuthRepo,
    );
  });

  group('verify initialize()', () {
    blocTest<SelfieVerificationCubit, SelfieVerificationState>(
      'should emit [SelfieVerificationFailure] when sessionToken is null',
      build: () => cubit,
      act: (_) => cubit.initialize(null),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <TypeMatcher<SelfieVerificationFailure>>[
        isA<SelfieVerificationFailure>(),
      ],
    );

    blocTest<SelfieVerificationCubit, SelfieVerificationState>(
        'should emit [InitializeBridgeSuccess] when initialization is successful',
        build: () => cubit,
        act: (_) => cubit.initialize(mockSessionToken),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => [
              isA<SelfieVerificationLoading>(),
              isA<InitializeBridgeSuccess>(),
            ],
        verify: (_) {
          final dynamic captured =
              verify(() => mockFacialHandler.initialize(args: captureAny(named: 'args')))
                  .captured
                  .first;
          expect(
              captured,
              isA<FacialVerificationInitializeArgs>().having(
                  (FacialVerificationInitializeArgs arg) => (arg.sessionToken, arg.languageCode),
                  'verify arg value',
                  (mockSessionToken, 'en')));
        });

    blocTest<SelfieVerificationCubit, SelfieVerificationState>(
        'should emit [SelfieVerificationFailure] when there is a bridge error',
        setUp: () {
          when(() => mockFacialHandler.initialize(args: any(named: 'args'))).thenAnswer((_) async {
            return FacialVerificationInitializeErrorResult(
              bridgeErrorReason: EkycBridgeErrorReason.unknown,
            );
          });
        },
        wait: TestConstant.blocEmitStateDelayDuration,
        build: () => cubit,
        act: (_) => cubit.initialize(mockSessionToken),
        expect: () => <TypeMatcher<SelfieVerificationState>>[
              isA<SelfieVerificationLoading>(),
              isA<SelfieVerificationFailure>(),
            ],
        verify: (_) {
          final List<dynamic> captured = verify(() => mockErrorFactory.create(
                entity: captureAny(named: 'entity'),
                bridgeError: captureAny(named: 'bridgeError'),
              )).captured;
          expect(captured[0], isNull);
          expect(captured[1], isNotNull);
        });

    blocTest<SelfieVerificationCubit, SelfieVerificationState>(
        'should emit [SelfieVerificationFailure] when there is api error',
        setUp: () {
          when(() => mockFacialHandler.initialize(args: any(named: 'args'))).thenAnswer((_) async {
            return FacialVerificationInitializeErrorResult(
                apiErrorResponse: BaseEntity(
              statusCode: CommonHttpClient.UNKNOWN_ERRORS,
            ));
          });
        },
        wait: TestConstant.blocEmitStateDelayDuration,
        build: () => cubit,
        act: (_) => cubit.initialize(mockSessionToken),
        expect: () => <TypeMatcher<SelfieVerificationState>>[
              isA<SelfieVerificationLoading>(),
              isA<SelfieVerificationFailure>(),
            ],
        verify: (_) {
          final List<dynamic> captured = verify(() => mockErrorFactory.create(
                entity: captureAny(named: 'entity'),
                bridgeError: captureAny(named: 'bridgeError'),
              )).captured;
          expect(captured[0], isNotNull);
          expect(captured[1], isNull);
        });

    blocTest<SelfieVerificationCubit, SelfieVerificationState>(
        'should emit [SelfieVerificationFailure] when there initialize\'s result is unsuccessful',
        setUp: () {
          when(() => mockFacialHandler.initialize(args: any(named: 'args'))).thenAnswer((_) async {
            return FacialVerificationInitializeErrorResult();
          });
        },
        wait: TestConstant.blocEmitStateDelayDuration,
        build: () => cubit,
        act: (_) => cubit.initialize(mockSessionToken),
        expect: () => <TypeMatcher<SelfieVerificationState>>[
              isA<SelfieVerificationLoading>(),
              isA<SelfieVerificationFailure>()
            ],
        verify: (_) {
          final List<dynamic> captured = verify(() => mockErrorFactory.create(
                entity: captureAny(named: 'entity'),
                bridgeError: captureAny(named: 'bridgeError'),
              )).captured;
          expect(captured[0], isNull);
          expect(captured[1], isNull);
        });
  });

  group('verify captureSelfie()', () {
    final EkycBridgeLivenessMode mockLivenessMode = EkycBridgeLivenessMode.flash_16;
    final BaseEntity mockErrorEntity = BaseEntity();
    final EkycBridgeErrorReason mockBridgeError = EkycBridgeErrorReason.unknown;
    final SelfieErrorUiModel mockSelfieError = SelfieErrorUiModel(
      actionType: ErrorActionType.ignore,
    );

    setUp(() {
      when(() => mockFacialHandler.startCapturing(args: any(named: 'args'))).thenAnswer((_) async {
        return FacialVerificationStartCapturingSuccessResult(
          livenessMode: mockLivenessMode,
          imageIds: mockImageIds,
          videoIds: mockFileIds,
        );
      });
    });

    blocTest<SelfieVerificationCubit, SelfieVerificationState>(
      'should emit [SelfieVerificationProcessing, SelfieCapturingSuccess] when capturing is successful',
      build: () => cubit,
      act: (_) => cubit.captureSelfie(liveMode: mockLivenessMode),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <TypeMatcher<SelfieVerificationState>>[
        isA<SelfieVerificationProcessing>(),
        isA<SelfieCapturingSuccess>().having(
            (SelfieCapturingSuccess state) => (state.imageIds, state.videoIds),
            'verify capture data',
            (mockImageIds, mockFileIds)),
      ],
    );

    blocTest<SelfieVerificationCubit, SelfieVerificationState>(
        'should emit [SelfieVerificationProcessing, SelfieVerificationFailure] when capturing is [FacialVerificationStartCapturingErrorResult]',
        setUp: () {
          when(() => mockErrorFactory.create(
                entity: any(named: 'entity'),
                bridgeError: any(named: 'bridgeError'),
              )).thenReturn(mockSelfieError);
          when(() => mockFacialHandler.startCapturing(args: any(named: 'args')))
              .thenAnswer((_) async {
            return FacialVerificationStartCapturingErrorResult(
              livenessMode: mockLivenessMode,
              apiErrorResponse: mockErrorEntity,
              bridgeErrorReason: mockBridgeError,
            );
          });
        },
        build: () => cubit,
        act: (_) => cubit.captureSelfie(liveMode: mockLivenessMode),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <TypeMatcher<SelfieVerificationState>>[
              isA<SelfieVerificationProcessing>(),
              isA<SelfieVerificationFailure>().having(
                (SelfieVerificationFailure state) => state.error,
                'verify error',
                mockSelfieError,
              ),
            ],
        verify: (_) {
          final List<dynamic> captured = verify(() => mockErrorFactory.create(
                entity: captureAny(named: 'entity'),
                bridgeError: captureAny(named: 'bridgeError'),
              )).captured;
          expect(captured[0], mockErrorEntity);
          expect(captured[1], mockBridgeError);
        });

    blocTest<SelfieVerificationCubit, SelfieVerificationState>(
        'should emit [SelfieVerificationProcessing, SelfieVerificationFailure] when capturing is [FacialVerificationStartCapturingErrorNotInitialized]',
        setUp: () {
          when(() => mockErrorFactory.create(
                entity: any(named: 'entity'),
                bridgeError: any(named: 'bridgeError'),
              )).thenReturn(mockSelfieError);
          when(() => mockFacialHandler.startCapturing(args: any(named: 'args')))
              .thenAnswer((_) async {
            return FacialVerificationStartCapturingErrorNotInitialized(
              livenessMode: mockLivenessMode,
            );
          });
        },
        build: () => cubit,
        act: (_) => cubit.captureSelfie(liveMode: mockLivenessMode),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <TypeMatcher<SelfieVerificationState>>[
              isA<SelfieVerificationProcessing>(),
              isA<SelfieVerificationFailure>().having(
                (SelfieVerificationFailure state) => state.error,
                'verify error',
                mockSelfieError,
              ),
            ],
        verify: (_) {
          final List<dynamic> captured = verify(() => mockErrorFactory.create(
                entity: captureAny(named: 'entity'),
                bridgeError: captureAny(named: 'bridgeError'),
              )).captured;
          expect(captured[0], isNull);
          expect(captured[1], isNull);
        });
  });

  group('verify verifySelfie() ', () {
    final EkycBridgeLivenessMode mockLiveMode = EkycBridgeLivenessMode.flash_16;

    setUp(() {
      when(() => mockAuthRepo.resetPin(
          request: any(named: 'request'),
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async {
        return ResetPinEntity.fromBaseResponse(
            BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: {}));
      });
    });

    group('verify resetPin', () {
      final SelfieVerificationFlowType mockFlowType = SelfieVerificationFlowType.resetPin;
      verifyAuthRepoResetPinParams() {
        final dynamic captured = verify(() => mockAuthRepo.resetPin(
            request: captureAny(named: 'request'),
            mockConfig: any(named: 'mockConfig'))).captured.firstOrNull;

        expect(
            captured,
            isA<ResetPinFaceAuthRequest>().having(
                (ResetPinFaceAuthRequest request) => (
                      request.selfieType,
                      request.videoIds,
                      request.imageIds,
                      request.type,
                      request.sessionToken,
                    ),
                'verify request',
                (
                  mockLiveMode.value,
                  mockFileIds,
                  mockImageIds,
                  ResetPinType.faceAuth,
                  mockSessionToken,
                )));
      }

      blocTest<SelfieVerificationCubit, SelfieVerificationState>(
        'emits [SelfieVerificationSuccess] when verification is successful for resetPin flow type',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (SelfieVerificationCubit bloc) => bloc.verifySelfie(
          flowType: mockFlowType,
          liveMode: mockLiveMode,
          sessionToken: mockSessionToken,
          imageIds: mockImageIds,
          videoIds: mockFileIds,
        ),
        expect: () => <TypeMatcher<SelfieVerificationSuccess>>[
          isA<SelfieVerificationSuccess>(),
        ],
        verify: (_) {
          verifyAuthRepoResetPinParams();
        },
      );

      blocTest<SelfieVerificationCubit, SelfieVerificationState>(
        'emits [SelfieVerificationFailure] when verification fails for resetPin flow type',
        setUp: () {
          when(() => mockAuthRepo.resetPin(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async {
            return ResetPinEntity.fromBaseResponse(BaseResponse(
                statusCode: CommonHttpClient.BAD_REQUEST, response: <String, dynamic>{}));
          });
        },
        wait: TestConstant.blocEmitStateDelayDuration,
        build: () => cubit,
        act: (_) => cubit.verifySelfie(
          flowType: mockFlowType,
          liveMode: mockLiveMode,
          sessionToken: mockSessionToken,
          imageIds: mockImageIds,
          videoIds: mockFileIds,
        ),
        expect: () => [
          isA<SelfieVerificationFailure>(),
        ],
        verify: (_) {
          verifyAuthRepoResetPinParams();
        },
      );
    });

    group('verify activateAccount', () {
      final SelfieVerificationFlowType mockFlowType = SelfieVerificationFlowType.activeAccount;

      setUp(() {
        when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async {
          return AccountActivationEntity.fromBaseResponse(
            BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: <String, dynamic>{},
            ),
          );
        });
      });

      verifyRepoCalled() {
        verify(() => mockAuthRepo.activateAccount(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      }

      blocTest<SelfieVerificationCubit, SelfieVerificationState>(
        'emits [SelfieVerificationSuccess] when verification is successful for activeAccount flow type',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (SelfieVerificationCubit bloc) => bloc.verifySelfie(
          flowType: mockFlowType,
          liveMode: mockLiveMode,
          sessionToken: mockSessionToken,
          imageIds: mockImageIds,
          videoIds: mockFileIds,
        ),
        expect: () => <TypeMatcher<SelfieVerificationSuccess>>[
          isA<SelfieVerificationSuccess>(),
        ],
        verify: (_) {
          verifyRepoCalled();
        },
      );

      blocTest<SelfieVerificationCubit, SelfieVerificationState>(
        'emits [SelfieVerificationFailure] when verification fails for activeAccount flow type',
        setUp: () {
          when(() => mockAuthRepo.activateAccount(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async {
            return AccountActivationEntity.fromBaseResponse(BaseResponse(
                statusCode: CommonHttpClient.BAD_REQUEST, response: <String, dynamic>{}));
          });
        },
        wait: TestConstant.blocEmitStateDelayDuration,
        build: () => cubit,
        act: (_) => cubit.verifySelfie(
          flowType: mockFlowType,
          liveMode: mockLiveMode,
          sessionToken: mockSessionToken,
          imageIds: mockImageIds,
          videoIds: mockFileIds,
        ),
        expect: () => [
          isA<SelfieVerificationFailure>(),
        ],
        verify: (_) {
          verifyRepoCalled();
        },
      );
    });
  });
}
