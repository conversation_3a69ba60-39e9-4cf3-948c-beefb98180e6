import 'package:evoapp/feature/ekyc/selfie/cubit/selfie_error_factory.dart';
import 'package:evoapp/feature/ekyc/selfie/model/selfie_error_ui_model.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_error_reason.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  final SelfieVerificationErrorFactory factory = SelfieVerificationErrorFactory();

  group('SelfieVerificationErrorFactory', () {
    test('should create unknown error when no entity or bridge error is provided', () {
      final SelfieErrorUiModel errorModel = factory.create();

      expect(errorModel.title, EvoStrings.unknownError);
      expect(errorModel.description, EvoStrings.tryAgainLater);
      expect(errorModel.actionType, ErrorActionType.retry);
    });

    group('createApiError', () {
      test('should create API error for locked resource', () {
        final BaseEntity entity = BaseEntity(
          statusCode: CommonHttpClient.LOCKED_RESOURCE,
        );
        final SelfieErrorUiModel errorModel = factory.createApiError(entity);
        expect(
          errorModel.actionType,
          ErrorActionType.blocked,
        );
      });

      test('should create API error for limit exceeded', () {
        final BaseEntity entity = BaseEntity(statusCode: CommonHttpClient.LIMIT_EXCEEDED);
        final SelfieErrorUiModel errorModel = factory.createApiError(entity);

        expect(
          errorModel.actionType,
          ErrorActionType.blocked,
        );
      });

      test('should create API error for invalid token', () {
        final BaseEntity entity = BaseEntity(statusCode: CommonHttpClient.INVALID_TOKEN);
        final SelfieErrorUiModel errorModel = factory.createApiError(entity);

        expect(errorModel.actionType, ErrorActionType.blocked);
      });

      test('should create API error with retry action for other status codes', () {
        final BaseEntity entity = BaseEntity(statusCode: CommonHttpClient.BAD_REQUEST);
        final SelfieErrorUiModel errorModel = factory.createApiError(entity);

        expect(errorModel.actionType, ErrorActionType.retry);
      });
    });

    group('createUnknownError', () {
      test('should create unknown error', () {
        final SelfieErrorUiModel errorModel = factory.createUnknownError();

        expect(errorModel.title, EvoStrings.unknownError);
        expect(errorModel.description, EvoStrings.tryAgainLater);
        expect(errorModel.actionType, ErrorActionType.retry);
      });
    });

    group('createBridgeError', () {
      test('should create bridge error for userCancelled', () {
        final SelfieErrorUiModel errorModel =
            factory.createBridgeError(EkycBridgeErrorReason.userCancelled);

        expect(errorModel.actionType, ErrorActionType.ignore);
      });

      test('should create bridge error for exceedLimit', () {
        final SelfieErrorUiModel errorModel = factory.createBridgeError(
          EkycBridgeErrorReason.exceedLimit,
        );

        expect(errorModel.title, EvoStrings.maxTriesReached);
        expect(errorModel.description, EvoStrings.tryAgainLater);
        expect(errorModel.actionType, ErrorActionType.blocked);
      });

      test('should create bridge error for sessionExpired', () {
        final SelfieErrorUiModel errorModel = factory.createBridgeError(
          EkycBridgeErrorReason.sessionExpired,
        );

        expect(errorModel.title, EvoStrings.titleSessionTokenExpired);
        expect(errorModel.description, EvoStrings.contentSessionTokenExpiredSignIn);
        expect(errorModel.actionType, ErrorActionType.blocked);
      });

      test('should create bridge error for initWithInvalidSession', () {
        final SelfieErrorUiModel errorModel = factory.createBridgeError(
          EkycBridgeErrorReason.initWithInvalidSession,
        );

        expect(errorModel.title, EvoStrings.titleSessionTokenExpired);
        expect(errorModel.description, EvoStrings.contentSessionTokenExpiredSignIn);
        expect(errorModel.actionType, ErrorActionType.blocked);
      });
    });
  });
}
