import 'package:evoapp/feature/account_activation/activate_account_success_screen.dart';
import 'package:evoapp/feature/account_activation/activation_status/activation_status_screen.dart';
import 'package:evoapp/feature/account_activation/active_virtual_card/active_virtual_card_intro_screen.dart';
import 'package:evoapp/feature/account_activation/create_username/create_username_screen.dart';
import 'package:evoapp/feature/account_activation/mobile_number_check/mobile_number_check_screen.dart';
import 'package:evoapp/feature/account_activation/verify_email/input_email_screen.dart';
import 'package:evoapp/feature/account_activation/verify_email/verify_email_screen.dart';
import 'package:evoapp/feature/account_activation/virtual_card_activated/virtual_card_activated_screen.dart';
import 'package:evoapp/feature/biometric/activate_biometric/active_biometric_page.dart';
import 'package:evoapp/feature/biometric/activate_biometric/confirm_pin/confirm_pin_screen.dart';
import 'package:evoapp/feature/ekyc/intro/face_capture_check_screen.dart';
import 'package:evoapp/feature/ekyc/selfie/selfie_locked_screen.dart';
import 'package:evoapp/feature/ekyc/selfie/selfie_retry_screen.dart';
import 'package:evoapp/feature/ekyc/selfie/selfie_success_screen.dart';
import 'package:evoapp/feature/ekyc/selfie/selfie_verification_screen.dart';
import 'package:evoapp/feature/error_screen/error_screen.dart';
import 'package:evoapp/feature/logging/evo_navigator_observer.dart';
import 'package:evoapp/feature/login/old_device/login_on_old_device_screen.dart';
import 'package:evoapp/feature/login/old_device/re_auth_input_pin/re_auth_input_pin_screen.dart';
import 'package:evoapp/feature/login/verify_username/verify_username_screen.dart';
import 'package:evoapp/feature/main_screen/card_page/activate_card_success_screen.dart';
import 'package:evoapp/feature/main_screen/card_page/last_4_digits_check/last_4_digits_check_screen.dart';
import 'package:evoapp/feature/main_screen/card_page/widgets/card_widget.dart';
import 'package:evoapp/feature/main_screen/main_screen.dart';
import 'package:evoapp/feature/pin/change_pin/change_pin_arg.dart';
import 'package:evoapp/feature/pin/change_pin/confirm_new_pin_screen.dart';
import 'package:evoapp/feature/pin/change_pin/create_new_pin_flow.dart';
import 'package:evoapp/feature/pin/change_pin/create_new_pin_screen.dart';
import 'package:evoapp/feature/pin/change_pin/verify_current/current_pin_verification_screen.dart';
import 'package:evoapp/feature/pin/new_pin_success_screen.dart';
import 'package:evoapp/feature/privilege_action/privilege_action_handler/core/privilege_action_handler.dart';
import 'package:evoapp/feature/privilege_action/verify_pin_privilege_action/verify_pin_privilege_action_screen.dart';
import 'package:evoapp/feature/profile/profile_screen/biometric_enabled_success_screen.dart';
import 'package:evoapp/feature/profile/profile_screen/profile_page.dart';
import 'package:evoapp/feature/splash_screen/splash_screen.dart';
import 'package:evoapp/feature/transaction_details/transaction_details_screen.dart';
import 'package:evoapp/feature/verify_otp/verify_otp_page.dart';
import 'package:evoapp/feature/welcome/introduction/introduction_screen.dart';
import 'package:evoapp/feature/welcome/welcome_screen.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/navigator/evo_router.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo_impl.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/feature/server_logging/common_navigator_observer.dart';
import 'package:flutter_common_package/feature/webview/webview.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/global.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:go_router/go_router.dart';
import 'package:mocktail/mocktail.dart';

import '../flutter_test_config.dart';

class MockBuildContext extends Mock implements BuildContext {}

class MockCommonHttpClient extends Mock implements CommonHttpClient {}

/// Mock class for testing [VerifyPinPrivilegeActionScreen]
class MockPrivilegeActionHandler extends Mock implements PrivilegeActionHandler<String> {}

void main() {
  const String fakeLocation = 'fake_location';
  const String fakeName = 'fake_name';
  const String fakePhone = 'fake_phone';
  const String fakeSessionToken = 'fake_session_token';
  final PageBaseArg fakeArg = PageBaseArg();

  late BuildContext mockNavigatorContext;

  setUpAll(() {
    getIt.registerLazySingleton<LoggingRepo>(
        () => LoggingRepoImpl(commonHttpClient: MockCommonHttpClient()));
    TestWidgetsFlutterBinding.ensureInitialized();
    getItRegisterNavigatorObserver();

    getItRegisterColor();
    getItRegisterTextStyle();

    mockNavigatorContext = MockBuildContext();
    setUpMockGlobalKeyProvider(mockNavigatorContext);

    final GlobalKeyProvider globalKeyProvider = getIt.get<GlobalKeyProvider>();
    when(() => globalKeyProvider.navigatorKey).thenReturn(GlobalKey<NavigatorState>());
    when(() => globalKeyProvider.scaffoldMessengerKey)
        .thenReturn(GlobalKey<ScaffoldMessengerState>());
  });

  Widget? verifyScreenBuilder<T>(
      {required String screenName, required String routeName, Object? extra}) {
    final GoRouterState goRouteState = GoRouterState(
      evoRouter.configuration,
      name: screenName,
      extra: extra,
      pageKey: ValueKey<String>(screenName),
      uri: Uri(path: fakeLocation),
      matchedLocation: '',
      fullPath: '',
      pathParameters: const <String, String>{},
    );
    final List<RouteBase> routes = evoRouter.configuration.routes;
    final GoRoute? findGoRoute = routes
        .firstWhere((RouteBase element) => (element as GoRoute).name == screenName) as GoRoute?;

    // verify path of route
    expect(findGoRoute?.path, routeName);

    // verify builder of route
    final GoRouterWidgetBuilder? builder = findGoRoute?.builder;
    final Widget? findPage = builder?.call(globalKeyProvider.navigatorContext!, goRouteState);
    expect(findPage, isA<T>());
    return findPage;
  }

  group('test evoRouter', () {
    test('verify location and initialLocation', () {
      expect(
          evoRouter.routeInformationProvider.value.uri.toString(), Screen.splashScreen.routeName);
      expect(evoRouter.configuration.navigatorKey, globalKeyProvider.navigatorKey);
    });

    test('verify [errorBuilder]', () {
      final GoRouterState goRouteState = GoRouterState(
        evoRouter.configuration,
        uri: Uri(path: fakeLocation),
        name: fakeName,
        pageKey: const ValueKey<String>(fakeName),
        matchedLocation: '',
        fullPath: '',
        pathParameters: const <String, String>{},
      );
      final GoRouterWidgetBuilder? errorBuilder = evoRouter.routerDelegate.builder.errorBuilder;
      final Widget? page = errorBuilder?.call(globalKeyProvider.navigatorContext!, goRouteState);

      expect(page, isA<SplashScreen>());
    });

    test('verify observers', () {
      final List<NavigatorObserver> observers = evoRouter.routerDelegate.builder.observers;

      expect(observers.length, 3);

      expect(observers.first, isA<CommonNavigatorObserver>());
      expect(observers[1], isA<EvoNavigatorObserver>());
      expect(observers.last, isA<FirebaseAnalyticsObserver>());
    });
  });

  group('verify builder of [SplashScreen]', () {
    test('verify builder of SplashScreen return correct screen', () {
      verifyScreenBuilder<SplashScreen>(
        screenName: Screen.splashScreen.name,
        routeName: Screen.splashScreen.routeName,
      );
    });
  });

  group('verify builder of [IntroductionPage]', () {
    test('verify builder of IntroductionPage return correct screen', () {
      verifyScreenBuilder<IntroductionScreen>(
        screenName: Screen.introductionScreen.name,
        routeName: Screen.introductionScreen.routeName,
      );
    });
  });

  group('verify builder of [Welcome Screen]', () {
    test('verify builder of Welcome screen return correct screen', () {
      verifyScreenBuilder<WelcomeScreen>(
        screenName: Screen.welcomeScreen.name,
        routeName: Screen.welcomeScreen.routeName,
      );
    });
  });

  group('verify builder of [ProfileScreen]', () {
    test('verify builder of ProfileScreen return correct screen', () {
      verifyScreenBuilder<ProfileScreen>(
        screenName: Screen.profileScreen.name,
        routeName: Screen.profileScreen.routeName,
      );
    });
  });

  group('verify builder of [VerifyUsernameScreen]', () {
    test('verify builder of VerifyUsernameScreen return correct screen', () {
      verifyScreenBuilder<VerifyUsernameScreen>(
        screenName: Screen.verifyUsernameScreen.name,
        routeName: Screen.verifyUsernameScreen.routeName,
      );
    });
  });

  group('verify builder of [MainScreen]', () {
    test('verify builder of MainScreen return correct screen', () {
      verifyScreenBuilder<MainScreen>(
        screenName: Screen.mainScreen.name,
        routeName: Screen.mainScreen.routeName,
        extra: MainScreenArg(isLoggedIn: true),
      );
    });
    test('verify builder of MainScreen return [ErrorPage] when [extra] is null', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.mainScreen.name,
        routeName: Screen.mainScreen.routeName,
      );
    });
    test('verify builder of MainScreen return [ErrorPage] when [extra] is wrong type', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.mainScreen.name,
        routeName: Screen.mainScreen.routeName,
        extra: fakeArg,
      );
    });
  });

  group('verify builder of [VerifyOtpPage]', () {
    test('verify builder of VerifyOtpPage return correct screen', () {
      verifyScreenBuilder<VerifyOtpPage>(
        screenName: Screen.verifyOtpScreen.name,
        routeName: Screen.verifyOtpScreen.routeName,
        extra: VerifyOtpPageArg(
          verifyOtpType: VerifyOtpType.signIn,
        ),
      );
    });
    test('verify builder of VerifyOtpPage return [ErrorPage] when [extra] is null', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.verifyOtpScreen.name,
        routeName: Screen.verifyOtpScreen.routeName,
      );
    });
    test('verify builder of VerifyOtpPage return [ErrorPage] when [extra] is wrong type', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.verifyOtpScreen.name,
        routeName: Screen.verifyOtpScreen.routeName,
        extra: fakeArg,
      );
    });
  });

  group('verify builder of [InputPinScreen]', () {
    test('verify builder of InputPinScreen return correct screen', () {
      verifyScreenBuilder<ReAuthInputPinScreen>(
        screenName: Screen.reAuthInputPinScreen.name,
        routeName: Screen.reAuthInputPinScreen.routeName,
        extra: ReAuthInputPinArg(
          phoneNumber: fakePhone,
          sessionToken: fakeSessionToken,
        ),
      );
    });
    test('verify builder of InputPinScreen return [ErrorPage] when [extra] is null', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.reAuthInputPinScreen.name,
        routeName: Screen.reAuthInputPinScreen.routeName,
      );
    });
    test('verify builder of InputPinScreen return [ErrorPage] when [extra] is wrong type', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.reAuthInputPinScreen.name,
        routeName: Screen.reAuthInputPinScreen.routeName,
        extra: fakeArg,
      );
    });
  });

  group('verify builder of [LoginOnOldDeviceScreen]', () {
    test('verify builder of LoginOnOldDeviceScreen return correct screen', () {
      verifyScreenBuilder<LoginOnOldDeviceScreen>(
        screenName: Screen.loginOnOldDeviceScreen.name,
        routeName: Screen.loginOnOldDeviceScreen.routeName,
      );
    });
  });
  group('verify builder of ActiveBiometricScreen', () {
    test('return correct screen', () {
      verifyScreenBuilder<ActiveBiometricScreen>(
        screenName: Screen.activeBiometric.name,
        routeName: Screen.activeBiometric.routeName,
        extra: ActiveBiometricScreenArg(
          onSuccess: () {},
        ),
      );
    });

    test('return [ErrorPage] when [extra] is null', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.activeBiometric.name,
        routeName: Screen.activeBiometric.routeName,
      );
    });

    test('return [ErrorPage] when [extra] is wrong type', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.activeBiometric.name,
        routeName: Screen.activeBiometric.routeName,
        extra: fakeArg,
      );
    });
  });

  group('verify builder of [CommonWebView]', () {
    test('verify builder of CommonWebView return correct screen', () {
      verifyScreenBuilder<CommonWebView>(
        screenName: CommonScreen.webViewPage.name,
        routeName: CommonScreen.webViewPage.routeName,
        extra: CommonWebViewArg(
          title: '',
          url: '',
        ),
      );
    });

    test('verify builder of CommonWebView return [ErrorPage] when [extra] is null', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: CommonScreen.webViewPage.name,
        routeName: CommonScreen.webViewPage.routeName,
      );
    });

    test('verify builder of CommonWebView return [ErrorPage] when [extra] is wrong type', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: CommonScreen.webViewPage.name,
        routeName: CommonScreen.webViewPage.routeName,
        extra: fakeArg,
      );
    });
  });

  group('verify builder of CurrentPINVerificationScreen', () {
    test('return correct screen', () {
      verifyScreenBuilder<CurrentPINVerificationScreen>(
          screenName: Screen.currentPinVerificationScreen.name,
          routeName: Screen.currentPinVerificationScreen.routeName,
          extra: CurrentPINVerificationArg(sessionToken: ''));
    });

    test('verify builder of CurrentPINVerificationScreen return [ErrorPage] when [extra] is null',
        () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.currentPinVerificationScreen.name,
        routeName: Screen.currentPinVerificationScreen.routeName,
      );
    });

    test(
        'verify builder of CurrentPINVerificationScreen return [ErrorPage] when [extra] is wrong type',
        () {
      verifyScreenBuilder<ErrorPage>(
          screenName: Screen.currentPinVerificationScreen.name,
          routeName: Screen.currentPinVerificationScreen.routeName,
          extra: fakeArg);
    });
  });

  test('verify builder of CreateNewPinScreen return correct screen', () {
    verifyScreenBuilder<CreateNewPinScreen>(
      screenName: Screen.createNewPinScreen.name,
      routeName: Screen.createNewPinScreen.routeName,
      extra: CreateNewPinArgs(
        sessionToken: fakeSessionToken,
        onSuccess: (BaseEntity entity) {},
        flow: CreateNewPinFlow.resetPin,
      ),
    );
  });

  group('verify builder of [ConfirmPinScreen]', () {
    test('verify builder of ConfirmPinScreen return correct screen', () {
      verifyScreenBuilder<ConfirmPinScreen>(
        screenName: Screen.confirmPinScreen.name,
        routeName: Screen.confirmPinScreen.routeName,
        extra: ConfirmPinScreenArgs(callback: null),
      );
    });

    test('verify builder of ConfirmPinScreen return [ErrorPage] when [extra] is null', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.confirmPinScreen.name,
        routeName: Screen.confirmPinScreen.routeName,
      );
    });

    test('verify builder of ConfirmPinScreen return [ErrorPage] when [extra] is wrong type', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.confirmPinScreen.name,
        routeName: Screen.confirmPinScreen.routeName,
        extra: fakeArg,
      );
    });
  });

  group('verify builder of [ConfirmResetPinScreen]', () {
    test('verify builder of ConfirmResetPinScreen return correct screen', () {
      verifyScreenBuilder<ConfirmNewPinScreen>(
          screenName: Screen.confirmNewPinScreen.name,
          routeName: Screen.confirmNewPinScreen.routeName,
          extra: ConfirmNewPinArgs(
            pin: '',
            sessionToken: '',
            onSuccess: (BaseEntity entity) {},
            flow: CreateNewPinFlow.resetPin,
          ));
    });

    test('verify builder of ConfirmResetPinScreen return [ErrorPage] when [extra] is null', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.confirmNewPinScreen.name,
        routeName: Screen.confirmNewPinScreen.routeName,
      );
    });

    test('verify builder of ConfirmResetPinScreen return [ErrorPage] when [extra] is wrong type',
        () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.confirmNewPinScreen.name,
        routeName: Screen.confirmNewPinScreen.routeName,
        extra: fakeArg,
      );
    });
  });

  test('verify builder of BiometricEnabledSuccessScreen return correct screen', () {
    verifyScreenBuilder<BiometricEnabledSuccessScreen>(
      screenName: Screen.biometricEnabledSuccessScreen.name,
      routeName: Screen.biometricEnabledSuccessScreen.routeName,
    );
  });

  test('verify builder of MobileNumberCheckScreen return correct screen', () {
    verifyScreenBuilder<MobileNumberCheckScreen>(
      screenName: Screen.mobileNumberCheckScreen.name,
      routeName: Screen.mobileNumberCheckScreen.routeName,
    );
  });

  group('verify builder of FaceCaptureCheckScreen return correct screen', () {
    test('return correct screen', () {
      verifyScreenBuilder<FaceCaptureCheckScreen>(
          screenName: Screen.faceCaptureCheckScreen.name,
          routeName: Screen.faceCaptureCheckScreen.routeName,
          extra: FaceCaptureCheckScreenArgs(onPopSuccess: (BaseEntity entity) {}));
    });

    test('verify builder of FaceCaptureCheckScreen return [ErrorPage] when [extra] is null', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.faceCaptureCheckScreen.name,
        routeName: Screen.faceCaptureCheckScreen.routeName,
      );
    });

    test('verify builder of FaceCaptureCheckScreen return [ErrorPage] when [extra] is mismatch',
        () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.faceCaptureCheckScreen.name,
        routeName: Screen.faceCaptureCheckScreen.routeName,
        extra: fakeArg,
      );
    });
  });

  group('verify builder of CreateUsernameScreen return correct screen', () {
    test('return correct screen', () {
      verifyScreenBuilder<CreateUsernameScreen>(
        screenName: Screen.createUsernameScreen.name,
        routeName: Screen.createUsernameScreen.routeName,
        extra: CreateUsernameScreenArgs(onPopSuccess: (BaseEntity entity) {}, sessionToken: fakeSessionToken),
      );
    });

    test('return [ErrorPage] when [extra] is null', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.createUsernameScreen.name,
        routeName: Screen.createUsernameScreen.routeName,
      );
    });

    test('return [ErrorPage] when [extra] is wrong type', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.createUsernameScreen.name,
        routeName: Screen.createUsernameScreen.routeName,
        extra: fakeArg,
      );
    });
  });

  test('verify builder of ActivateAccountSuccessScreen return correct screen', () {
    verifyScreenBuilder<ActivateAccountSuccessScreen>(
      screenName: Screen.activateAccountSuccessScreen.name,
      routeName: Screen.activateAccountSuccessScreen.routeName,
    );
  });

  group('verify builder of SelfieVerificationScreen', () {
    test('verify builder of SelfieVerificationScreen return correct screen', () {
      verifyScreenBuilder<SelfieVerificationScreen>(
          screenName: Screen.selfieVerificationScreen.name,
          routeName: Screen.selfieVerificationScreen.routeName,
          extra: SelfieVerificationScreenArgs(
            onPopSuccess: (BaseEntity entity) {},
            flowType: SelfieVerificationFlowType.resetPin,
          ));
    });

    test('verify builder of SelfieVerificationScreen return [ErrorPage] when [extra] is null', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.selfieVerificationScreen.name,
        routeName: Screen.selfieVerificationScreen.routeName,
      );
    });

    test('verify builder of SelfieVerificationScreen return [ErrorPage] when [extra] is wrong type',
        () {
      verifyScreenBuilder<ErrorPage>(
          screenName: Screen.selfieVerificationScreen.name,
          routeName: Screen.selfieVerificationScreen.routeName,
          extra: fakeArg);
    });
  });

  group('verify builder of [ActiveVirtualCardIntroScreen]', () {
    test('verify builder of ActiveVirtualCardIntroScreen return correct screen', () {
      verifyScreenBuilder<ActiveVirtualCardIntroScreen>(
        screenName: Screen.activeVirtualCardIntroScreen.name,
        routeName: Screen.activeVirtualCardIntroScreen.routeName,
        extra: ActiveVirtualCardArg(cardHolderName: 'fake_name'),
      );
    });

    test('verify builder of ActiveVirtualCardIntroScreen return [ErrorPage] when [extra] is null',
        () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.activeVirtualCardIntroScreen.name,
        routeName: Screen.activeVirtualCardIntroScreen.routeName,
      );
    });

    test(
        'verify builder of ActiveVirtualCardIntroScreen return [ErrorPage] when [extra] is wrong type',
        () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.activeVirtualCardIntroScreen.name,
        routeName: Screen.activeVirtualCardIntroScreen.routeName,
        extra: fakeArg,
      );
    });
  });

  group('verify builder of [VirtualCardActivatedScreen]', () {
    test('verify builder of VirtualCardActivatedScreen return correct screen', () {
      verifyScreenBuilder<VirtualCardActivatedScreen>(
        screenName: Screen.virtualCardActivatedScreen.name,
        routeName: Screen.virtualCardActivatedScreen.routeName,
        extra: VirtualCardActivatedArg(userName: 'fake_name'),
      );
    });

    test('verify builder of VirtualCardActivatedScreen return [ErrorPage] when [extra] is null',
        () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.virtualCardActivatedScreen.name,
        routeName: Screen.virtualCardActivatedScreen.routeName,
      );
    });

    test(
        'verify builder of VirtualCardActivatedScreen return [ErrorPage] when [extra] is wrong type',
        () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.virtualCardActivatedScreen.name,
        routeName: Screen.virtualCardActivatedScreen.routeName,
        extra: fakeArg,
      );
    });
  });

  group('verify builder of [ActivateCardSuccessScreen]', () {
    test('verify builder of ActivateCardSuccessScreen return correct screen', () {
      verifyScreenBuilder<ActivateCardSuccessScreen>(
        screenName: Screen.activateCardSuccessScreen.name,
        routeName: Screen.activateCardSuccessScreen.routeName,
        extra: ActivateCardSuccessArg(CardType.virtual),
      );
    });

    test('verify builder of ActivateCardSuccessScreen return [ErrorPage] when [extra] is null', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.activateCardSuccessScreen.name,
        routeName: Screen.activateCardSuccessScreen.routeName,
      );
    });

    test(
        'verify builder of ActivateCardSuccessScreen return [ErrorPage] when [extra] is wrong type',
        () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.activateCardSuccessScreen.name,
        routeName: Screen.activateCardSuccessScreen.routeName,
        extra: fakeArg,
      );
    });
  });

  group('verify builder of [verifyPinPrivilegeActionScreen]', () {
    test('verify builder of verifyPinPrivilegeActionScreen return correct screen', () {
      verifyScreenBuilder<VerifyPinPrivilegeActionScreen>(
        screenName: Screen.verifyPinPrivilegeActionScreen.name,
        routeName: Screen.verifyPinPrivilegeActionScreen.routeName,
        extra: VerifyPinPrivilegeActionScreenArgs(
            handler: MockPrivilegeActionHandler(),
            onComplete: (PrivilegeActionResponse<dynamic> result) {}),
      );
    });

    test('verify builder of ActivateCardSuccessScreen return [ErrorPage] when [extra] is null', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.verifyPinPrivilegeActionScreen.name,
        routeName: Screen.verifyPinPrivilegeActionScreen.routeName,
      );
    });

    test(
        'verify builder of ActivateCardSuccessScreen return [ErrorPage] when [extra] is wrong type',
        () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.verifyPinPrivilegeActionScreen.name,
        routeName: Screen.verifyPinPrivilegeActionScreen.routeName,
        extra: fakeArg,
      );
    });
  });

  group('verify builder of [SelfieRetryScreen]', () {
    test('verify builder of SelfieRetryScreen return correct screen', () {
      verifyScreenBuilder<SelfieRetryScreen>(
        screenName: Screen.selfieRetryScreen.name,
        routeName: Screen.selfieRetryScreen.routeName,
        extra: SelfieRetryScreenArg(onRetry: () {}),
      );
    });

    test('verify builder of SelfieRetryScreen return [ErrorPage] when [extra] is null', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.selfieRetryScreen.name,
        routeName: Screen.selfieRetryScreen.routeName,
      );
    });

    test('verify builder of SelfieRetryScreen return [ErrorPage] when [extra] is wrong type', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.selfieRetryScreen.name,
        routeName: Screen.selfieRetryScreen.routeName,
        extra: fakeArg,
      );
    });
  });

  group('verify builder of [SelfieLockedScreen]', () {
    test('verify builder of SelfieLockedScreen return correct screen', () {
      verifyScreenBuilder<SelfieLockedScreen>(
        screenName: Screen.selfieLockedScreen.name,
        routeName: Screen.selfieLockedScreen.routeName,
        extra: SelfieLockedScreenArg(title: 'title', subtitle: 'subtitle'),
      );
    });

    test('verify builder of SelfieLockedScreen return [ErrorPage] when [extra] is null', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.selfieLockedScreen.name,
        routeName: Screen.selfieLockedScreen.routeName,
      );
    });

    test('verify builder of SelfieLockedScreen return [ErrorPage] when [extra] is wrong type', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.selfieLockedScreen.name,
        routeName: Screen.selfieLockedScreen.routeName,
        extra: fakeArg,
      );
    });
  });

  group('verify builder of [SelfieSuccessScreen]', () {
    test('verify builder of SelfieSuccessScreen return correct screen', () {
      verifyScreenBuilder<SelfieSuccessScreen>(
        screenName: Screen.selfieSuccessScreen.name,
        routeName: Screen.selfieSuccessScreen.routeName,
        extra: SelfieSuccessScreenArg(onProceed: () {}),
      );
    });

    test('verify builder of SelfieSuccessScreen return [ErrorPage] when [extra] is null', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.selfieSuccessScreen.name,
        routeName: Screen.selfieSuccessScreen.routeName,
      );
    });

    test('verify builder of SelfieSuccessScreen return [ErrorPage] when [extra] is wrong type', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.selfieSuccessScreen.name,
        routeName: Screen.selfieSuccessScreen.routeName,
        extra: fakeArg,
      );
    });
  });

  group('verify builder of [VerifyEmailScreen]', () {
    test('verify builder of VerifyEmailScreen return correct screen', () {
      verifyScreenBuilder<VerifyEmailScreen>(
        screenName: Screen.verifyEmailScreen.name,
        routeName: Screen.verifyEmailScreen.routeName,
        extra: VerifyEmailArg(email: 'email', sessionToken: 'token', onPopSuccess: (_) {}),
      );
    });

    test('verify builder of VerifyEmailScreen return [ErrorPage] when [extra] is null', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.verifyEmailScreen.name,
        routeName: Screen.verifyEmailScreen.routeName,
      );
    });

    test('verify builder of VerifyEmailScreen return [ErrorPage] when [extra] is wrong type', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.verifyEmailScreen.name,
        routeName: Screen.verifyEmailScreen.routeName,
        extra: fakeArg,
      );
    });
  });

  group('verify builder of [InputEmailScreen]', () {
    test('verify builder of InputEmailScreen return correct screen', () {
      verifyScreenBuilder<InputEmailScreen>(
        screenName: Screen.inputEmailScreen.name,
        routeName: Screen.inputEmailScreen.routeName,
        extra: InputEmailArg(email: 'email', sessionToken: 'token', onPopSuccess: (_) {}),
      );
    });

    test('verify builder of InputEmailScreen return [ErrorPage] when [extra] is null', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.inputEmailScreen.name,
        routeName: Screen.inputEmailScreen.routeName,
      );
    });

    test('verify builder of InputEmailScreen return [ErrorPage] when [extra] is wrong type', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.inputEmailScreen.name,
        routeName: Screen.inputEmailScreen.routeName,
        extra: fakeArg,
      );
    });
  });

  group('verify builder of [Last4DigitsCheckScreen]', () {
    test('verify builder of Last4DigitsCheckScreen return correct screen', () {
      verifyScreenBuilder<Last4DigitsCheckScreen>(
        screenName: Screen.last4DigitsCheckScreen.name,
        routeName: Screen.last4DigitsCheckScreen.routeName,
        extra: Last4DigitsCheckScreenArg(onSuccess: () {}),
      );
    });
    test('verify builder of [Last4DigitsCheckScreen] return [ErrorPage] when [extra] is null', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.last4DigitsCheckScreen.name,
        routeName: Screen.last4DigitsCheckScreen.routeName,
        extra: null,
      );
    });
    test('verify builder of [Last4DigitsCheckScreen] return [ErrorPage] when [extra] is wrong type',
        () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.last4DigitsCheckScreen.name,
        routeName: Screen.last4DigitsCheckScreen.routeName,
        extra: fakeArg,
      );
    });
  });

  group('verify builder of [NewPinSuccessScreen]', () {
    test('verify builder of NewPinSuccessScreen return correct screen', () {
      verifyScreenBuilder<NewPinSuccessScreen>(
        screenName: Screen.newPinSuccessScreen.name,
        routeName: Screen.newPinSuccessScreen.routeName,
        extra: NewPinSuccessScreenArg(buttonText: '', onNext: () {}),
      );
    });
    test('verify builder of [NewPinSuccessScreen] return [ErrorPage] when [extra] is null', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.newPinSuccessScreen.name,
        routeName: Screen.newPinSuccessScreen.routeName,
        extra: null,
      );
    });
    test('verify builder of [NewPinSuccessScreen] return [ErrorPage] when [extra] is wrong type',
        () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.newPinSuccessScreen.name,
        routeName: Screen.newPinSuccessScreen.routeName,
        extra: fakeArg,
      );
    });
  });

  group('verify builder of [TransactionDetailsScreen]', () {
    test('verify builder of TransactionDetailsScreen return correct screen', () {
      verifyScreenBuilder<TransactionDetailsScreen>(
        screenName: Screen.transactionDetailsScreen.name,
        routeName: Screen.transactionDetailsScreen.routeName,
        extra: TransactionDetailsScreenArg(transactionId: 'id'),
      );
    });
    test('verify builder of [TransactionDetailsScreen] return [ErrorPage] when [extra] is null',
        () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.transactionDetailsScreen.name,
        routeName: Screen.transactionDetailsScreen.routeName,
        extra: null,
      );
    });
    test(
        'verify builder of [TransactionDetailsScreen] return [ErrorPage] when [extra] is wrong type',
        () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.transactionDetailsScreen.name,
        routeName: Screen.transactionDetailsScreen.routeName,
        extra: fakeArg,
      );
    });
  });

  group('verify builder of [ActivationStatusScreen]', () {
    test('verify builder of ActivationStatusScreen return correct screen', () {
      verifyScreenBuilder<ActivationStatusScreen>(
        screenName: Screen.activationStatusScreen.name,
        routeName: Screen.activationStatusScreen.routeName,
        extra: ActivationStatusScreenArg(status: ActivationStatus.rejected),
      );
    });

    test('verify builder of [ActivationStatusScreen] return [ErrorPage] when [extra] is null', () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.activationStatusScreen.name,
        routeName: Screen.activationStatusScreen.routeName,
        extra: null,
      );
    });

    test('verify builder of [ActivationStatusScreen] return [ErrorPage] when [extra] is wrong type',
        () {
      verifyScreenBuilder<ErrorPage>(
        screenName: Screen.activationStatusScreen.name,
        routeName: Screen.activationStatusScreen.routeName,
        extra: fakeArg,
      );
    });
  });
}
