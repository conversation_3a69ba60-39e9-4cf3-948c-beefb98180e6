import 'package:evoapp/data/constants.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/dio_http_client_impl.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../flutter_test_config.dart';

void main() {
  setUpAll(() {
    getIt.registerLazySingleton<EvoFlutterWrapper>(() => EvoFlutterWrapper());
    getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());
    getIt.registerLazySingleton<CommonUtilFunction>(() => CommonUtilFunction());
    getIt.registerLazySingleton<AppState>(() => AppState());

    testClearAllNotificationExecutable();
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('test method clearEvoDataOnLogout() clear dio header', () {
    late MockOneSignal mockOneSignal;
    late FlutterSecureStorage secureStorage;
    late DioClientImpl dioClientImpl;

    setUpAll(() {
      secureStorage = testFlutterSecureStorageExecutable();
      testSecureDataSourceExecutable(secureStorage: secureStorage);
      testEvoSecureStorageHelperExecutable(secureStorage: secureStorage);
      dioClientImpl = testDioClientImplExecutable();
    });

    setUp(() {
      mockOneSignal = testOneSignalExecutable();
    });

    tearDown(() {
      reset(mockOneSignal);
    });

    test('should_call_method_clearAllUserData_clear_dio_header_correctly ', () async {
      await evoUtilFunction.clearAllUserData(oneSignal: mockOneSignal);
      final Map<String, dynamic> getHeader = dioClientImpl.getHeaders();
      if (getHeader.containsKey(HeaderKey.authorization)) {
        expect(getHeader[HeaderKey.authorization], null);
      }
    });
  });
}
