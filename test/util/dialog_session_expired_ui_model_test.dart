// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/dialog_functions.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockDialogFunction extends Mock implements DialogFunction {}

void main() {
  setUpAll(() {
    // Register fallback values for mocktail
    registerFallbackValue(EvoDialogId.common);
    registerFallbackValue(DialogAlertType.error);
  });

  group('DialogSessionExpiredUiModel', () {
    test('signIn constructor should create model with correct values', () {
      final DialogSessionExpiredUiModel model = DialogSessionExpiredUiModel.signIn();

      expect(model.title, EvoStrings.titleSessionTokenExpired);
      expect(model.content, EvoStrings.contentSessionTokenExpiredSignIn);
      expect(model.textPositive, EvoStrings.textSubmitSessionTokenExpiredSignIn);
      expect(model.dialogId, EvoDialogId.signInSessionTokenExpiredErrorDialog);
    });

    test('resetPin constructor should create model with correct values', () {
      final DialogSessionExpiredUiModel model = DialogSessionExpiredUiModel.resetPin();

      expect(model.title, EvoStrings.titleSessionTokenExpired);
      expect(model.content, EvoStrings.contentSessionTokenExpiredResetPin);
      expect(model.textPositive, EvoStrings.ok);
      expect(model.dialogId, EvoDialogId.resetPinSessionTokenExpiredErrorDialog);
    });

    test('activateAccount constructor should create model with correct values', () {
      final DialogSessionExpiredUiModel model = DialogSessionExpiredUiModel.activateAccount();

      expect(model.title, EvoStrings.titleSessionTokenExpired);
      expect(model.content, EvoStrings.contentActivationAccountSessionTokenExpired);
      expect(model.textPositive, EvoStrings.ctaBackToActivationAccount);
      expect(model.dialogId, EvoDialogId.activateAccountSessionTokenExpiredErrorDialog);
    });
  });
}
