import 'dart:async';

import 'package:evoapp/data/constants.dart' as constants;
import 'package:evoapp/data/repository/user_repo.dart';
import 'package:evoapp/feature/biometric/biometric_token_module/biometrics_token_module.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/feature/logging/evo_navigator_observer.dart';
import 'package:evoapp/resources/button_dimensions.dart';
import 'package:evoapp/resources/button_styles.dart';
import 'package:evoapp/resources/colors.dart';
import 'package:evoapp/resources/text_styles.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/navigator/evo_router_navigator.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper_impl.dart';
import 'package:evoapp/util/token_utils/jwt_helper.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/dio_http_client_impl.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/feature/onesignal/onesignal.dart';
import 'package:flutter_common_package/feature/server_logging/common_navigator_observer.dart';
import 'package:flutter_common_package/feature/server_logging/firebase_analytics.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/resources/button_dimensions.dart';
import 'package:flutter_common_package/resources/button_styles.dart';
import 'package:flutter_common_package/resources/colors.dart';
import 'package:flutter_common_package/resources/text_styles.dart';
import 'package:flutter_common_package/util/clear_all_notifications_wrapper.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_common_package/util/local_storage_helper.dart';
import 'package:flutter_common_package/util/secure_storage_helper.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:ts_bio_detect_changed/ts_bio_detect_changed.dart';

Future<void> testExecutable(FutureOr<void> Function() testMain) async {
  setUp(() {
    //
  });

  tearDown(() {
    //
  });

  await testMain();
}

// FlutterSecureStorage
FlutterSecureStorage testFlutterSecureStorageExecutable({Map<String, String>? mockInitialValue}) {
  const FlutterSecureStorage secureStorage = FlutterSecureStorage();
  FlutterSecureStorage.setMockInitialValues(mockInitialValue ?? <String, String>{});

  return secureStorage;
}

// SecureDataSource
CommonSecureStorageHelperImpl testSecureDataSourceExecutable(
    {required FlutterSecureStorage secureStorage}) {
  final CommonSecureStorageHelperImpl dataSource =
      CommonSecureStorageHelperImpl(secureStorage: secureStorage);
  getIt.registerLazySingleton<CommonLocalStorageHelper>(() => dataSource);

  return dataSource;
}

// Dio
DioClientImpl testDioClientImplExecutable(
    {Map<String, String?> initValueHeader = const <String, String?>{
      constants.HeaderKey.authorization: 'Bearer init_access_token_value_test'
    }}) {
  final Dio dio = Dio(BaseOptions(headers: initValueHeader));
  final DioClientImpl dioClientImpl = DioClientImpl(dio);
  getIt.registerLazySingleton<CommonHttpClient>(() => dioClientImpl);

  return dioClientImpl;
}

class MockOneSignal extends Mock implements OneSignal {}

// OneSignal
MockOneSignal testOneSignalExecutable(
    {Future<void>? valueSetAppId, Future<Map<String, dynamic>>? valueRemoveExternalUserId}) {
  final MockOneSignal mockOneSignal = MockOneSignal();
  when(() => mockOneSignal.setAppId(any()))
      .thenAnswer((_) => valueSetAppId ?? Future<void>.value());
  when(() => mockOneSignal.removeExternalUserId()).thenAnswer((_) =>
      valueRemoveExternalUserId ??
      Future<Map<String, dynamic>>.value(<String, dynamic>{'success': true}));
  when(() => mockOneSignal.clearOneSignalNotifications()).thenAnswer((_) => Future<void>.value());
  return mockOneSignal;
}

// EvoSecureDataSource
EvoSecureStorageHelperImpl testEvoSecureStorageHelperExecutable(
    {required FlutterSecureStorage secureStorage}) {
  final EvoSecureStorageHelperImpl dataSource =
      EvoSecureStorageHelperImpl(secureStorage: secureStorage);
  getIt.registerLazySingleton<EvoLocalStorageHelper>(() => dataSource);

  return dataSource;
}

/// Biometric token module
BiometricsTokenModule testBiometricTokenModuleExecutable({
  required BiometricsAuthenticate bioAuth,
  required UserRepo userRepo,
  required TsBioDetectChanged bioDetectChanged,
  required EvoLocalStorageHelper storageHelper,
  required JwtHelper jwtHelper,
}) {
  final BiometricsTokenModule biometricsTokenModule = BiometricsTokenModule(
    biometricsAuthenticate: bioAuth,
    userRepo: userRepo,
    secureStorageHelper: storageHelper,
    bioDetectChanged: bioDetectChanged,
    jwtHelper: jwtHelper,
  );
  getIt.registerLazySingleton<BiometricsTokenModule>(() => biometricsTokenModule);
  return biometricsTokenModule;
}

class MockClearAllNotificationsWrapper extends Mock implements ClearAllNotificationsWrapper {}

// clear all notifications
MockClearAllNotificationsWrapper testClearAllNotificationExecutable() {
  final MockClearAllNotificationsWrapper mockClearAllNotificationsWrapper =
      MockClearAllNotificationsWrapper();

  getIt.registerLazySingleton<ClearAllNotificationsWrapper>(() => mockClearAllNotificationsWrapper);

  when(() => mockClearAllNotificationsWrapper.clear()).thenAnswer((_) => Future<void>.value());

  return mockClearAllNotificationsWrapper;
}

class MockGlobalKeyProvider extends Mock implements GlobalKeyProvider {}

void setUpMockGlobalKeyProvider(BuildContext mockNavigatorContext) {
  getIt.registerSingleton<GlobalKeyProvider>(MockGlobalKeyProvider());
  final GlobalKeyProvider globalKeyProvider = getIt.get<GlobalKeyProvider>();
  when(() => globalKeyProvider.navigatorContext).thenReturn(mockNavigatorContext);
  when(() => globalKeyProvider.navigatorKey).thenReturn(GlobalKey<NavigatorState>());
  when(() => globalKeyProvider.scaffoldMessengerKey)
      .thenReturn(GlobalKey<ScaffoldMessengerState>());
}

class MockEvoImageProvider extends Mock implements CommonImageProvider {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

class MockCommonUtilFunction extends Mock implements CommonUtilFunction {}

void getItRegisterMockCommonUtilFunctionAndImageProvider() {
  getIt.registerLazySingleton<CommonUtilFunction>(() => MockCommonUtilFunction());
  getIt.registerLazySingleton<EvoUtilFunction>(() => MockEvoUtilFunction());
  getIt.registerLazySingleton<CommonImageProvider>(() => MockEvoImageProvider());
}

void getItUnRegisterMockCommonUtilFunctionAndImageProvider() {
  getIt.unregister<CommonUtilFunction>();
  getIt.unregister<EvoUtilFunction>();
  getIt.unregister<CommonImageProvider>();
}

void getItRegisterColor() {
  getIt.registerLazySingleton<CommonColors>(() => EvoColors());
  getIt.registerLazySingleton<EvoColors>(() => EvoColors());
}

void initConfigChangeScreenSize(WidgetTester widgetTester, {required Size size}) {
  widgetTester.view.physicalSize = size;
  widgetTester.view.devicePixelRatio = 1;
}

void resetConfigChangeScreenSize(WidgetTester widgetTester) {
  // resets the screen to its original size after the test end
  addTearDown(widgetTester.view.resetPhysicalSize);
  addTearDown(widgetTester.view.resetDevicePixelRatio);
}

void getItUnregisterColor() {
  getIt.unregister<CommonColors>();
  getIt.unregister<EvoColors>();
}

void getItRegisterTextStyle() {
  getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
  getIt.registerLazySingleton<EvoTextStyles>(() => EvoTextStyles());
}

void getItUnRegisterTextStyle() {
  getIt.unregister<CommonTextStyles>();
  getIt.unregister<EvoTextStyles>();
}

void getItRegisterButtonStyle() {
  getIt.registerLazySingleton<CommonButtonStyles>(() => EvoButtonStyles());
  getIt.registerLazySingleton<CommonButtonDimensions>(() => EvoButtonDimensions());
}

void getItUnRegisterButtonStyle() {
  getIt.unregister<CommonButtonStyles>();
  getIt.unregister<CommonButtonDimensions>();
}

class MockFirebaseAnalytics extends Mock implements FirebaseAnalytics {}

class MockFirebaseAnalyticsObserver extends Mock implements FirebaseAnalyticsObserver {}

void setupFirebaseForTest() {
  if (getIt.isRegistered<FirebaseAnalyticsWrapper>()) {
    getIt.unregister<FirebaseAnalyticsWrapper>();
  }
  final MockFirebaseAnalytics mockFirebaseAnalytics = MockFirebaseAnalytics();
  final MockFirebaseAnalyticsObserver mockFirebaseAnalyticsObserver =
      MockFirebaseAnalyticsObserver();
  when(() => mockFirebaseAnalytics.logEvent(
        name: any(named: 'name'),
        parameters: any(named: 'parameters'),
      )).thenAnswer((_) async {});
  when(() => mockFirebaseAnalyticsObserver.analytics).thenReturn(mockFirebaseAnalytics);

  getIt.registerSingleton<FirebaseAnalyticsWrapper>(FirebaseAnalyticsWrapper(
    mockFirebaseAnalytics,
    mockFirebaseAnalyticsObserver,
  ));
}

class MockCommonObserver extends Mock implements CommonNavigatorObserver {}

void setupNavigatorObserversForTest() {
  if (getIt.isRegistered<CommonNavigatorObserver>()) {
    getIt.unregister<CommonNavigatorObserver>();
  }
  final MockCommonObserver mockCommonObserver = MockCommonObserver();
  getIt.registerLazySingleton<CommonNavigatorObserver>(() => mockCommonObserver);

  setupFirebaseForTest();
}

void getItRegisterNavigatorObserver() {
  if (getIt.isRegistered<EvoNavigatorObserver>()) {
    getIt.unregister<EvoNavigatorObserver>();
  }
  getIt.registerLazySingleton<EvoNavigatorObserver>(() => EvoNavigatorObserver());

  setupNavigatorObserversForTest();
}

void getItUnRegisterNavigatorObserver() {
  getIt.unregister<EvoNavigatorObserver>();
}

class MockContext extends Mock implements BuildContext {}

class MockEvoNavigator extends Mock implements EvoRouterNavigator {}

void getItRegisterNavigator({required BuildContext context}) {
  getIt.registerLazySingleton<CommonNavigator>(() => MockEvoNavigator());
  setUpMockGlobalKeyProvider(context);
}

void getItUnregisterNavigator() {
  getIt.unregister<CommonNavigator>();
  getIt.unregister<GlobalKeyProvider>();
}

class MockLoggingRepo extends Mock implements LoggingRepo {}

void mockLoggingRepo() {
  final LoggingRepo repo = getIt.registerSingleton(MockLoggingRepo());
  when(() => repo.logErrorEvent(errorType: any(named: 'errorType'), args: any(named: 'args')))
      .thenAnswer((_) async {});
}
