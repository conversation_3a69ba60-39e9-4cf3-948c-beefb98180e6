import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/repository/common_repo.dart';
import 'package:evoapp/data/repository/user_repo.dart';
import 'package:evoapp/data/response/user_information_entity.dart';
import 'package:evoapp/feature/authorization_session_expired/authorization_session_expired_handler.dart';
import 'package:evoapp/feature/authorization_session_expired/authorization_session_expired_popup.dart';
import 'package:evoapp/feature/authorization_session_expired/force_logout_popup.dart';
import 'package:evoapp/feature/biometric/biometric_token_module/biometrics_token_module.dart';
import 'package:evoapp/feature/biometric/model/biometric_status_change_notifier.dart';
import 'package:evoapp/feature/biometric/model/biometric_ui_model.dart';
import 'package:evoapp/feature/biometric/request_user_active_biometric/request_user_active_biometric_handler.dart';
import 'package:evoapp/feature/biometric/request_user_active_biometric/request_user_active_biometric_util.dart';
import 'package:evoapp/feature/biometric/utils/biometric_functions.dart';
import 'package:evoapp/feature/biometric/utils/biometric_status_helper.dart';
import 'package:evoapp/feature/biometric/utils/biometric_type_helper.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/logging/event_tracking_shared_data.dart';
import 'package:evoapp/feature/logging/evo_event_tracking_utils/evo_event_tracking_utils.dart';
import 'package:evoapp/feature/logging/evo_navigator_observer.dart';
import 'package:evoapp/feature/login/utils/login_old_device_utils.dart';
import 'package:evoapp/feature/pin/models/change_pin_status.dart';
import 'package:evoapp/feature/pin/reset_pin/reset_pin_handler.dart';
import 'package:evoapp/feature/privilege_action/privilege_access_guard_module.dart';
import 'package:evoapp/feature/privilege_action/verify_biometric_privilege_action/verify_biometric_privilege_action.dart';
import 'package:evoapp/feature/splash_screen/utils/exit_app_feature/exit_app_feature.dart';
import 'package:evoapp/feature/splash_screen/utils/secure_detection_utils/secure_detection.dart';
import 'package:evoapp/flavors/factory/evo_flavor_factory.dart';
import 'package:evoapp/flavors/flavors_type.dart';
import 'package:evoapp/model/user_info_notifier.dart';
import 'package:evoapp/model/user_token.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/input_borders.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/app_settings_wrapper.dart';
import 'package:evoapp/util/dialog_functions.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/interceptor/log_event_interceptor.dart';
import 'package:evoapp/util/interceptor/unauthorized_interceptor.dart';
import 'package:evoapp/util/navigator/evo_router_navigator.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/token_utils/jwt_helper.dart';
import 'package:evoapp/util/url_launcher_uri_wrapper.dart' as app_url_launcher;
import 'package:evoapp/util/validator/evo_validator.dart';
import 'package:evoapp/util/validator/mpin_validator.dart';
import 'package:firebase_core_platform_interface/firebase_core_platform_interface.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/common_package/intl.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/dio_log_interceptor.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/feature/data_collection/data_collector.dart';
import 'package:flutter_common_package/feature/ekyc/bridges/ekyc_bridge.dart';
import 'package:flutter_common_package/feature/server_logging/common_navigator_observer.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_utils.dart';
import 'package:flutter_common_package/feature/server_logging/firebase_analytics.dart';
import 'package:flutter_common_package/feature/server_logging/firebase_crashlytics.dart';
import 'package:flutter_common_package/feature/webview/webview_utils.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/button_dimensions.dart';
import 'package:flutter_common_package/resources/colors.dart';
import 'package:flutter_common_package/resources/global.dart';
import 'package:flutter_common_package/resources/text_styles.dart';
import 'package:flutter_common_package/util/alert_manager.dart';
import 'package:flutter_common_package/util/clear_all_notifications_wrapper.dart';
import 'package:flutter_common_package/util/clipboard_wrapper.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/device_info_plugin_wrapper/device_info_plugin_wrapper.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_common_package/util/flutter_downloader/common_flutter_downloader.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_common_package/util/local_storage_helper.dart';
import 'package:flutter_common_package/util/network_manager.dart';
import 'package:flutter_common_package/util/otp_auto_fill/otp_auto_fill.dart';
import 'package:flutter_common_package/util/share_preference_helper.dart';
import 'package:flutter_common_package/util/url_launcher_wrapper.dart' as common_url_launcher;
import 'package:flutter_common_package/util/uuid/uuid_generator.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_common_package/widget/default_widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:ts_bio_detect_changed/ts_bio_detect_changed.dart';

class MockFirebasePlatform extends Mock implements FirebasePlatform {}

class MockFirebaseOptions extends Mock implements FirebaseOptions {}

class MockBuildContext extends Mock implements BuildContext {}

class MockDio extends Mock implements Dio {}

class MockAuthorizationSessionExpiredHandler extends Mock
    implements AuthorizationSessionExpiredHandler {}

class MockAppState extends Mock implements AppState {}

class MockJWTHelper extends Mock implements JwtHelper {}

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

class MockPackageInfo extends Mock implements PackageInfo {}

class MockFirebaseCrashlytics extends Mock implements FirebaseCrashlytics {}

void main() {
  group('verify AppState', () {
    late AppState appState;

    setUp(() {
      appState = AppState();
    });

    test('get/set value of locale correctly', () {
      expect(appState.locale, const Locale('vi'));

      const Locale engLocale = Locale('en');
      appState.locale = engLocale;

      expect(appState.locale, engLocale);
    });

    test('get/set value of appVersion correctly', () {
      expect(appState.appVersion, isNull);

      const String newAppVersion = 'v1.1.0';
      appState.appVersion = newAppVersion;

      expect(appState.appVersion, newAppVersion);

      appState.appVersion = null;

      expect(appState.appVersion, isNull);
    });

    test('get/set value of userInfo correctly', () {
      expect(appState.userInfo.value, null);

      final UserInfoNotifier newValue = UserInfoNotifier(null);
      const String fullName = 'Evo';
      newValue.value = const UserInformationEntity(fullName: fullName);
      appState.userInfo = newValue;

      expect(appState.userInfo.value?.fullName, fullName);
    });

    test('get/set value of isCheckShowUpdateOnce correctly', () {
      expect(appState.isCheckShowUpdateOnce, null);

      const bool newValueCheckShowUpdateOnce = true;
      appState.isCheckShowUpdateOnce = newValueCheckShowUpdateOnce;

      expect(appState.isCheckShowUpdateOnce, newValueCheckShowUpdateOnce);

      appState.isCheckShowUpdateOnce = null;
      expect(appState.isCheckShowUpdateOnce, isNull);
    });

    test('get/set value of isUserLogIn correctly', () {
      expect(appState.isUserLogIn, false);

      const bool newValueIsUserLogIn = true;
      appState.isUserLogIn = newValueIsUserLogIn;

      expect(appState.isUserLogIn, newValueIsUserLogIn);

      appState.isUserLogIn = false;
      expect(appState.isUserLogIn, false);
    });

    test('get/set value of bioTypeInfo correctly', () {
      expect(appState.bioTypeInfo, isNotNull);
      expect(appState.bioTypeInfo.biometricTypeName, EvoStrings.faceFingerText);
      expect(appState.bioTypeInfo.iconPath, EvoImages.icFaceFingerId);
      expect(appState.bioTypeInfo.iconSettingPath, EvoImages.icSettingFaceFingerId);

      appState.bioTypeInfo = BiometricTypeUIModel.face();

      expect(appState.bioTypeInfo.biometricTypeName, EvoStrings.faceText);
      expect(appState.bioTypeInfo.iconPath, EvoImages.icFaceId);
      expect(appState.bioTypeInfo.iconSettingPath, EvoImages.icSettingFaceId);
    });

    test('get/set value of biometricStatusChangeNotifier correctly', () {
      expect(appState.biometricStatusChangeNotifier.value, BiometricStatus.usable);

      const BiometricStatus newStatus = BiometricStatus.notSetup;
      final BiometricStatusChangeNotifier newValue = BiometricStatusChangeNotifier(newStatus);
      appState.biometricStatusChangeNotifier = newValue;

      expect(appState.biometricStatusChangeNotifier.value, newStatus);
    });

    test('get/set value of actionAfterLogin correctly', () {
      expect(appState.actionAfterLogin, isNull);

      bool hasCallActionAfterLogin = false;
      appState.actionAfterLogin = () {
        hasCallActionAfterLogin = true;
      };

      appState.actionAfterLogin?.call();

      expect(hasCallActionAfterLogin, true);
    });

    test('get/set value of appLifecycleState', () {
      expect(appState.appLifecycleState, null);

      const AppLifecycleState newAppLifecycleState = AppLifecycleState.resumed;
      appState.appLifecycleState = newAppLifecycleState;
      expect(appState.appLifecycleState, newAppLifecycleState);
    });

    test('get/set value of changePinStatusNotifier correctly', () {
      expect(appState.changePinStatusNotifier.value, ChangePinStatus.available);

      const ChangePinStatus newStatus = ChangePinStatus.locked;
      final ChangePinStatusNotifier newValue = ChangePinStatusNotifier()..value = newStatus;
      appState.changePinStatusNotifier = newValue;

      expect(appState.changePinStatusNotifier.value, newStatus);
    });

    test('should return the same EventTrackingSharedData', () {
      expect(identical(appState.eventTrackingSharedData, appState.eventTrackingSharedData), isTrue);
    });

    test('should return EventTrackingScreenId from EventTrackingSharedData.currentScreenId', () {
      final EventTrackingSharedData data = appState.eventTrackingSharedData;
      data.currentScreenId = null;
      expect(appState.currentScreenId, EventTrackingScreenId.undefined);

      final EventTrackingScreenId screenId = EventTrackingScreenId.webViewScreen;
      data.currentScreenId = screenId;
      expect(appState.currentScreenId, screenId);
    });

    test('get/set value of userToken', () {
      expect(appState.userToken, isNotNull);

      final UserToken token = UserToken();
      appState.userToken = token;
      expect(appState.userToken, token);
    });
  });

  group('verify setUpDioInterceptor', () {
    late Dio dio;

    setUpAll(() {
      getIt.registerLazySingleton<Dio>(() => Dio());
      getIt.registerLazySingleton<AuthorizationSessionExpiredHandler>(
          () => MockAuthorizationSessionExpiredHandler());
      getIt.registerLazySingleton<AppState>(() => MockAppState());
      getIt.registerLazySingleton<JwtHelper>(() => MockJWTHelper());
      getIt.registerLazySingleton<AuthenticationRepo>(() => MockAuthenticationRepo());

      dio = getIt.get<Dio>();
    });

    tearDownAll(() {
      getIt.unregister<Dio>();
      getIt.unregister<AuthorizationSessionExpiredHandler>();
      getIt.unregister<AppState>();
      getIt.unregister<JwtHelper>();
      getIt.unregister<AuthenticationRepo>();
    });

    test('interceptor should matched order', () {
      setUpDioInterceptor();

      final Interceptors interceptors = dio.interceptors;

      /// Default Interceptors[0] is [ImplyContentTypeInterceptor]
      expect(interceptors[1], isA<DioLogInterceptor>());
      expect(interceptors[2], isA<LogEventInterceptor>());
      expect(interceptors[3], isA<UnauthorizedInterceptor>());
    });
  });

  group('initAppState', () {
    late PackageInfo packageInfo;

    setUpAll(() async {
      getIt.registerSingletonAsync<PackageInfo>(() async => MockPackageInfo());
      packageInfo = await getIt.getAsync<PackageInfo>();
      when(() => packageInfo.version).thenReturn('version');
    });

    tearDownAll(() {
      getIt.unregister<PackageInfo>();
      getIt.unregister<AppState>();
    });

    setUp(() {
      if (getIt.isRegistered<AppState>()) {
        getIt.unregister<AppState>();
      }
    });

    test('should register AppState', () async {
      final Locale locale = Locale('vi');
      await initAppState(locale: locale);

      expect(getIt.isRegistered<AppState>(), isTrue);
    });

    test('should set values', () async {
      final Locale locale = Locale('vi');
      await initAppState(locale: locale);

      final AppState appState = getIt.get<AppState>();
      expect(appState.locale, locale);
      expect(appState.appVersion, 'version');
      expect(Intl.defaultLocale, appState.locale.languageCode);
    });
  });

  group('prepareForAppInitiation', () {
    setUpAll(() async {
      TestWidgetsFlutterBinding.ensureInitialized();
      FlavorConfig(
        flavor: FlavorType.stag.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.stag).getFlavorValue(),
      );
      _setupMockData();
      await prepareForAppInitiation();
    });

    tearDownAll(() {
      _tearDownMockData();
    });

    test('should register dependencies from common package', () {
      expect(getIt.isRegistered<DevicePlatform>(), isTrue);
      expect(getIt.isRegistered<PackageInfo>(), isTrue);

      expect(getIt.isRegistered<DeviceInfoPlugin>(), isTrue);
      expect(getIt.isRegistered<DeviceInfoPluginWrapper>(), isTrue);

      expect(getIt.isRegistered<FirebaseAnalyticsWrapper>(), isTrue);

      expect(getIt.isRegistered<Connectivity>(), isTrue);
      expect(getIt.isRegistered<NetworkManager>(), isTrue);

      expect(getIt.isRegistered<FlutterSecureStorage>(), isTrue);
      expect(getIt.isRegistered<CommonLocalStorageHelper>(), isTrue);
      expect(getIt.isRegistered<CommonSharedPreferencesHelper>(), isTrue);

      expect(getIt.isRegistered<CommonUtilFunction>(), isTrue);

      expect(getIt.isRegistered<Dio>(), isTrue);
      expect(getIt.isRegistered<CommonHttpClient>(), isTrue);

      expect(getIt.isRegistered<LoggingRepo>(), isTrue);

      expect(getIt.isRegistered<UUIDGenerator>(), isTrue);

      expect(getIt.isRegistered<EventTrackingUtils>(), isTrue);

      expect(getIt.isRegistered<DataCollector>(), isTrue);

      // OneSignalListenerHandler is declared but not registered
      // expect(getIt.isRegistered<OneSignalListenerHandler>(), isTrue);

      expect(getIt.isRegistered<AlertManager>(), isTrue);
      expect(getIt.isRegistered<CommonImageProvider>(), isTrue);
      expect(getIt.isRegistered<CommonNavigatorObserver>(), isTrue);

      expect(getIt.isRegistered<OtpAutoFill>(), isTrue);

      expect(getIt.isRegistered<ClipboardWrapper>(), isTrue);

      expect(getIt.isRegistered<common_url_launcher.UrlLauncherWrapper>(), isTrue);

      expect(getIt.isRegistered<ClearAllNotificationsWrapper>(), isTrue);

      expect(getIt.isRegistered<CommonFlutterDownloader>(), isTrue);

      expect(getIt.isRegistered<GlobalKeyProvider>(), isTrue);

      expect(getIt.isRegistered<CommonWebViewUtils>(), isTrue);

      expect(getIt.isRegistered<EkycBridge>(), isTrue);
    });

    test('should register dependencies ordered by function calls', () {
      expect(getIt.isRegistered<EvoFlutterWrapper>(), isTrue);

      expect(getIt.isRegistered<EvoUtilFunction>(), isTrue);

      expect(getIt.isRegistered<EvoValidator>(), isTrue);
      expect(getIt.isRegistered<MpinValidator>(), isTrue);

      expect(getIt.isRegistered<DialogFunction>(), isTrue);

      expect(getIt.isRegistered<SecureDetection>(), isTrue);

      expect(getIt.isRegistered<ExitAppFeature>(), isTrue);

      expect(getIt.isRegistered<AppState>(), isTrue);

      expect(getIt.isRegistered<DialogFunction>(), isTrue);

      expect(getIt.isRegistered<Dio>(instanceName: nonAuthenticationHttpClientInstance), isTrue);
      expect(
        getIt.isRegistered<CommonHttpClient>(instanceName: nonAuthenticationHttpClientInstance),
        isTrue,
      );

      expect(getIt.isRegistered<CommonFlutterDownloader>(), isTrue);

      expect(getIt.isRegistered<AuthorizationSessionExpiredHandler>(), isTrue);
      expect(getIt.isRegistered<AuthorizationSessionExpiredPopup>(), isTrue);
      expect(getIt.isRegistered<ForceLogoutPopup>(), isTrue);

      expect(getIt.isRegistered<EvoSnackBar>(), isTrue);

      expect(getIt.isRegistered<EvoEventTrackingUtils>(), isTrue);
      expect(getIt.isRegistered<EvoNavigatorObserver>(), isTrue);

      expect(getIt.isRegistered<EvoNavigatorTypeFactory>(), isTrue);
      expect(getIt.isRegistered<CommonNavigator>(), isTrue);

      expect(getIt.isRegistered<CommonTextStyles>(), isTrue);
      expect(getIt.isRegistered<CommonColors>(), isTrue);
      expect(getIt.isRegistered<CommonButtonDimensions>(), isTrue);
      expect(getIt.isRegistered<CommonDefaultWidgets>(), isTrue);
      expect(getIt.isRegistered<EvoInputBorders>(), isTrue);
      expect(getIt.isRegistered<DialogFunction>(), isTrue);

      expect(getIt.isRegistered<EvoLocalStorageHelper>(), isTrue);

      expect(getIt.isRegistered<JwtHelper>(), isTrue);

      expect(getIt.isRegistered<UserRepo>(), isTrue);
      expect(getIt.isRegistered<AuthenticationRepo>(), isTrue);
      expect(getIt.isRegistered<CommonRepo>(), isTrue);

      expect(getIt.isRegistered<BiometricsAuthenticate>(), isTrue);
      expect(getIt.isRegistered<TsBioDetectChanged>(), isTrue);
      expect(getIt.isRegistered<BiometricsTokenModule>(), isTrue);
      expect(getIt.isRegistered<BiometricTypeHelper>(), isTrue);

      expect(getIt.isRegistered<FeatureToggle>(), isTrue);

      expect(getIt.isRegistered<RequestUserActiveBiometricUtil>(), isTrue);
      expect(getIt.isRegistered<RequestUserActivateBiometricHandler>(), isTrue);

      expect(getIt.isRegistered<BiometricStatusHelper>(), isTrue);
      expect(getIt.isRegistered<BiometricFunctions>(), isTrue);

      expect(getIt.isRegistered<ResetPinHandler>(), isTrue);

      expect(getIt.isRegistered<app_url_launcher.UrlLauncherWrapper>(), isTrue);

      expect(getIt.isRegistered<AppSettingsWrapper>(), isTrue);

      expect(getIt.isRegistered<LoginOldDeviceUtils>(), isTrue);

      expect(getIt.isRegistered<VerifyBiometricForPrivilegeAction>(), isTrue);
      expect(getIt.isRegistered<PrivilegeAccessGuardModule>(), isTrue);
    });

    test('should be able to get registered dependencies', () {
      expect(getIt.get<EvoFlutterWrapper>(), isA<EvoFlutterWrapper>());

      expect(getIt.get<EvoUtilFunction>(), isA<EvoUtilFunction>());

      expect(getIt.get<EvoValidator>(), isA<EvoValidator>());
      expect(getIt.get<MpinValidator>(), isA<MpinValidator>());

      expect(getIt.get<DialogFunction>(), isA<DialogFunction>());

      expect(getIt.get<SecureDetection>(), isA<SecureDetection>());

      expect(getIt.get<ExitAppFeature>(), isA<ExitAppFeature>());

      expect(getIt.get<AppState>(), isA<AppState>());

      expect(getIt.get<DialogFunction>(), isA<DialogFunction>());

      expect(getIt.get<Dio>(instanceName: nonAuthenticationHttpClientInstance), isA<Dio>());
      expect(
        getIt.get<CommonHttpClient>(instanceName: nonAuthenticationHttpClientInstance),
        isA<CommonHttpClient>(),
      );

      expect(getIt.get<CommonFlutterDownloader>(), isA<CommonFlutterDownloader>());

      expect(getIt.get<AuthorizationSessionExpiredHandler>(),
          isA<AuthorizationSessionExpiredHandler>());
      expect(
          getIt.get<AuthorizationSessionExpiredPopup>(), isA<AuthorizationSessionExpiredPopup>());
      expect(getIt.get<ForceLogoutPopup>(), isA<ForceLogoutPopup>());

      expect(getIt.get<EvoSnackBar>(), isA<EvoSnackBar>());

      expect(getIt.get<EvoEventTrackingUtils>(), isA<EvoEventTrackingUtils>());
      expect(getIt.get<EvoNavigatorObserver>(), isA<EvoNavigatorObserver>());

      expect(getIt.get<EvoNavigatorTypeFactory>(), isA<EvoNavigatorTypeFactory>());
      expect(getIt.get<CommonNavigator>(), isA<CommonNavigator>());

      expect(getIt.get<CommonTextStyles>(), isA<CommonTextStyles>());
      expect(getIt.get<CommonColors>(), isA<CommonColors>());
      expect(getIt.get<CommonButtonDimensions>(), isA<CommonButtonDimensions>());
      expect(getIt.get<CommonDefaultWidgets>(), isA<CommonDefaultWidgets>());
      expect(getIt.get<EvoInputBorders>(), isA<EvoInputBorders>());
      expect(getIt.get<DialogFunction>(), isA<DialogFunction>());

      expect(getIt.get<EvoLocalStorageHelper>(), isA<EvoLocalStorageHelper>());

      expect(getIt.get<JwtHelper>(), isA<JwtHelper>());

      expect(getIt.get<UserRepo>(), isA<UserRepo>());
      expect(getIt.get<AuthenticationRepo>(), isA<AuthenticationRepo>());
      expect(getIt.get<CommonRepo>(), isA<CommonRepo>());

      expect(getIt.get<BiometricsAuthenticate>(), isA<BiometricsAuthenticate>());
      expect(getIt.get<TsBioDetectChanged>(), isA<TsBioDetectChanged>());
      expect(getIt.get<BiometricsTokenModule>(), isA<BiometricsTokenModule>());
      expect(getIt.get<BiometricTypeHelper>(), isA<BiometricTypeHelper>());

      expect(getIt.get<FeatureToggle>(), isA<FeatureToggle>());

      expect(getIt.get<RequestUserActiveBiometricUtil>(), isA<RequestUserActiveBiometricUtil>());
      expect(getIt.get<RequestUserActivateBiometricHandler>(),
          isA<RequestUserActivateBiometricHandler>());

      expect(getIt.get<BiometricStatusHelper>(), isA<BiometricStatusHelper>());
      expect(getIt.get<BiometricFunctions>(), isA<BiometricFunctions>());

      expect(getIt.get<ResetPinHandler>(), isA<ResetPinHandler>());

      expect(getIt.get<app_url_launcher.UrlLauncherWrapper>(),
          isA<app_url_launcher.UrlLauncherWrapper>());

      expect(getIt.get<AppSettingsWrapper>(), isA<AppSettingsWrapper>());

      expect(getIt.get<LoginOldDeviceUtils>(), isA<LoginOldDeviceUtils>());

      expect(
          getIt.get<VerifyBiometricForPrivilegeAction>(), isA<VerifyBiometricForPrivilegeAction>());
      expect(getIt.get<PrivilegeAccessGuardModule>(), isA<PrivilegeAccessGuardModule>());
    });
  });
}

void _setupMockData() {
  // firebase
  final FirebasePlatform mockFirebase = MockFirebasePlatform();
  Firebase.delegatePackingProperty = mockFirebase;
  when(() => mockFirebase.initializeApp(name: any(named: 'name'), options: any(named: 'options')))
      .thenAnswer((_) async => FirebaseAppPlatform(defaultFirebaseAppName, MockFirebaseOptions()));
  when(() => mockFirebase.app(any()))
      .thenAnswer((_) => FirebaseAppPlatform(defaultFirebaseAppName, MockFirebaseOptions()));

  // crashlytics
  final FirebaseCrashlytics mockFirebaseCrashlytics = MockFirebaseCrashlytics();
  FirebaseCrashlyticsWrapper.instanceForTesting = mockFirebaseCrashlytics;

  when(
    () => mockFirebaseCrashlytics.setCustomKey(HeaderKey.deviceId, any()),
  ).thenAnswer((_) async {});

  // mock channels
  for (final _MockChannel mockChannel in _MockChannel.values) {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      MethodChannel(mockChannel.channelName),
      (MethodCall methodCall) async {
        return mockChannel.data;
      },
    );
  }
}

void _tearDownMockData() {
  FirebaseCrashlyticsWrapper.resetToOriginalInstance();

  Firebase.delegatePackingProperty = null;
  //mock channels
  for (final _MockChannel mockChannel in _MockChannel.values) {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      MethodChannel(mockChannel.channelName),
      null,
    );
  }
}

enum _MockChannel {
  oneSignal('OneSignal'),
  downloader('vn.hunghd/downloader'),
  packageInfo('dev.fluttercommunity.plus/package_info', <String, dynamic>{}),
  appsFlyerCallBacks('callbacks'),
  appsFlyerAfApi('af-api'),
  firebasePerformance('plugins.flutter.io/firebase_performance'),
  sensorPlusMethod('dev.fluttercommunity.plus/sensors/method'),
  sensorPlusAccelerometer('dev.fluttercommunity.plus/sensors/accelerometer'),
  connectivity('dev.fluttercommunity.plus/connectivity'),
  connectivityStatus('dev.fluttercommunity.plus/connectivity_status'),
  deviceInfo('dev.fluttercommunity.plus/device_info', <String, dynamic>{
    'version': <String, dynamic>{
      'sdkInt': 30,
      'baseOS': 'baseOS',
      'codename': 'codename',
      'incremental': 'incremental',
      'previewSdkInt': 30,
      'release': 'release',
      'securityPatch': 'securityPatch',
    },
    'board': '.board',
    'bootloader': '.bootloader',
    'brand': '.brand',
    'device': '.device',
    'display': '.display',
    'fingerprint': '.fingerprint',
    'hardware': '.hardware',
    'host': '.host',
    'id': '.id',
    'manufacturer': '.manufacturer',
    'model': '.model',
    'product': '.product',
    'supported32BitAbis': <String>[],
    'supported64BitAbis': <String>[],
    'supportedAbis': <String>[],
    'tags': '.tags',
    'type': '.type',
    'isPhysicalDevice': false,
    'systemFeatures': <String>[],
    'displayMetrics': <String, dynamic>{
      'widthPx': 0.0,
      'heightPx': 0.0,
      'xDpi': 0.0,
      'yDpi': 0.0,
    },
    'serialNumber': 'serialNumber',
  });

  final String channelName;
  final dynamic data;

  const _MockChannel(this.channelName, [this.data]);
}
