import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/util/utils.dart';

class LogEventInterceptor extends QueuedInterceptor {
  final String eventLogPath;

  LogEventInterceptor({
    required this.eventLogPath,
  });

  /// Ignore error if path equals `eventLogPath` by [handler.reject]
  /// otherwise pass to next interceptors.
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    final String path = err.requestOptions.path;

    if (path == eventLogPath) {
      commonLog('ignore event error', methodName: 'onError');
      handler.reject(err);
      return;
    }
    handler.next(err);
  }
}
