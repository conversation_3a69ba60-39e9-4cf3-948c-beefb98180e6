import 'dart:math';

import 'package:flutter/widgets.dart';

/// inspired by https://pub.dev/packages/flutter_screenutil
/// this class is used for adapting screen and font size.Let your UI display a reasonable layout on different screen sizes!
/// references class: https://github.com/OpenFlutter/flutter_screenutil/blob/master/lib/src/screen_util.dart
class ScreenUtil {
  ScreenUtil._();

  static final ScreenUtil _instance = ScreenUtil._();

  factory ScreenUtil() => _instance;

  late double _screenWidth;
  late double _screenHeight;
  Size? _uiDesign;
  late double _textScaleFactor;
  late double _scaleWidth;
  late double _scaleHeight;

  void init({required BuildContext context, required Size design}) {
    _screenWidth = MediaQuery.of(context).size.width;
    _screenHeight = MediaQuery.of(context).size.height;
    _uiDesign = design;

    _scaleWidth = _screenWidth / design.width;
    _scaleHeight = _screenHeight / design.height;

    _textScaleFactor = min(_scaleWidth, _scaleHeight);
  }

  double get screenWidth => _screenWidth;

  double get screenHeight => _screenHeight;

  double get scaleWidth => _scaleWidth;

  double get scaleHeight => _scaleHeight;

  double get textScaleFactor => _textScaleFactor;

  double getHeight(num height) => _uiDesign == null ? height.toDouble() : height * _scaleHeight;

  double getWidth(num width) => _uiDesign == null ? width.toDouble() : width * _scaleWidth;

  double getTextScale(num fontSize) =>
      _uiDesign == null ? fontSize.toDouble() : fontSize * _textScaleFactor;
}

extension ScreenUtilExtension on num {
  double get h => ScreenUtil().getHeight(this);

  double get w => ScreenUtil().getWidth(this);

  double get sp => ScreenUtil().getTextScale(this);
}
