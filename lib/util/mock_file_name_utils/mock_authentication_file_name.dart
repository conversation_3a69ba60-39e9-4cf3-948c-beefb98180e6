String getRequestResetPinMockFileName({
  String? phoneNumber,
  String? nationalId,
  String? otp,
  String? pin,
}) {
  if (phoneNumber != null) {
    return 'request_reset_pin_with_national_id.json';
  } else if (nationalId != null) {
    return 'request_reset_pin_with_otp.json';
  } else if (otp != null) {
    return 'request_reset_pin_change_pin.json';
  }

  // case : pin != null
  return 'request_reset_pin_success.json';
}
