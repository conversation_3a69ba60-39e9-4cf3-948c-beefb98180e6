import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../resources/resources.dart';
import '../widget/custom_snackbar_widget.dart';

enum SnackBarType {
  success,
  error,
  neutral,
  warning;
}

enum SnackBarDuration {
  short(3000),
  medium(5000),
  long(10000);

  final int value;

  const SnackBarDuration(this.value);
}

class EvoSnackBar {
  final SnackBarWrapper snackBarWrapper;

  EvoSnackBar(this.snackBarWrapper);

  @visibleForTesting
  DateTime? latestTimeShowToast;
  @visibleForTesting
  String? latestToastMessage;
  @visibleForTesting
  bool enable = true;

  /// Prevent showing SnackBar multiple times
  Future<bool?> show(
    String message, {
    SnackBarType typeSnackBar = SnackBarType.success,
    int? durationInMilliSec,
    String? description,
    double? marginBottomRatio,
  }) {
    if (!shouldShowSnackBar(message, minIntervalInSec: durationInMilliSec)) {
      return Future<bool>.value(false);
    }
    commonLog('showSnackBar : $message');

    latestToastMessage = message;
    latestTimeShowToast = DateTime.now();

    snackBarWrapper.getSnackBar(
      message,
      typeSnackBar: typeSnackBar,
      durationInMilliSec: durationInMilliSec,
      description: description,
      marginBottomRatio: marginBottomRatio,
      onCancelSnackBar: () {
        // Reset the latest message and time when the SnackBar is closed
        // Refer: https://trustingsocial1.atlassian.net/browse/EMA-1790
        latestToastMessage = null;
        latestTimeShowToast = null;
      },
    );
    return Future<bool>.value(true);
  }

  bool shouldShowSnackBar(String message, {int? minIntervalInSec}) {
    return enable &&
        !isSameAsPreviousMessageShownRecently(message, minIntervalInSec: minIntervalInSec);
  }

  /// Prevent the same message showing multiple times consecutively in a short amount of time
  bool isSameAsPreviousMessageShownRecently(String message, {int? minIntervalInSec}) {
    minIntervalInSec ??= SnackBarDuration.long.value;
    return latestTimeShowToast != null &&
        DateTime.now().difference(latestTimeShowToast!).inMilliseconds < minIntervalInSec &&
        message == latestToastMessage;
  }

  set setEnable(bool enableValue) => enable = enableValue;
}

class SnackBarWrapper {
  static const double defaultMarginBottomRatio = 0.08;

  void getSnackBar(
    String message, {
    SnackBarType typeSnackBar = SnackBarType.success,
    int? durationInMilliSec,
    String? description,
    double? marginBottomRatio,
    VoidCallback? onCancelSnackBar,
  }) {
    Color? backgroundColor, borderColor;
    Widget? leadingIcon;
    switch (typeSnackBar) {
      case SnackBarType.success:
        backgroundColor = evoColors.positive70;
        borderColor = evoColors.positive70;
        leadingIcon = evoImageProvider.asset(EvoImages.icSnackBarSuccess, width: 20, height: 20);
        break;
      case SnackBarType.error:
        backgroundColor = evoColors.error70;
        borderColor = evoColors.error70;
        leadingIcon = evoImageProvider.asset(EvoImages.icSnackBarError, width: 20, height: 20);
        break;
      case SnackBarType.warning:
        backgroundColor = evoColors.warning70;
        borderColor = evoColors.warning70;
        leadingIcon = evoImageProvider.asset(EvoImages.icSnackBarWarning, width: 20, height: 20);
        break;
      case SnackBarType.neutral:
        backgroundColor = evoColors.info70;
        borderColor = evoColors.info70;
        leadingIcon = evoImageProvider.asset(EvoImages.icSnackBarNeutral, width: 20, height: 20);
        break;
    }

    /// If [margin] is null, then [SnackBarThemeData.insetPadding] of
    /// [ThemeData.snackBarTheme] is used. If that is also null, then the default is
    /// `EdgeInsets.fromLTRB(15.0, 5.0, 15.0, 10.0)`.
    EdgeInsets? margin;
    final double? marginBottom = getMarginBottom(marginBottomRatio: marginBottomRatio);
    if (marginBottom != null) {
      margin = EdgeInsets.only(bottom: marginBottom);
    }

    final SnackBar snackBar = SnackBar(
      backgroundColor: Colors.transparent,
      behavior: SnackBarBehavior.floating,
      dismissDirection: DismissDirection.none,
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      margin: margin,
      elevation: 0,
      duration: Duration(milliseconds: durationInMilliSec ?? SnackBarDuration.long.value),
      content: CustomSnackBarWidget(
        text: message,
        background: backgroundColor,
        borderColor: borderColor,
        leadingIcon: leadingIcon,
        description: description,
        onClose: () {
          cancelSnackBar();
          onCancelSnackBar?.call();
        },
      ),
    );
    globalKeyProvider.scaffoldMessengerKey.currentState?.showSnackBar(snackBar);
  }

  void cancelSnackBar() {
    globalKeyProvider.scaffoldMessengerKey.currentState?.removeCurrentSnackBar();
  }

  /// The [marginBottomRatio] is the ratio of the screen height.
  ///
  /// The [view] is the [FlutterView] that used to get the screen height.
  /// It is visible for testing only. Real code should not use it.
  /// When `view` is null, the [navigatorContext] will be used to get the [FlutterView].
  @visibleForTesting
  double? getMarginBottom({double? marginBottomRatio, FlutterView? view}) {
    double? flutterViewHeight;

    if (view == null) {
      final BuildContext? context = navigatorContext;
      if (context == null) {
        return null;
      }
      view = View.of(context);
    }

    flutterViewHeight = _getFlutterViewHeight(view);
    return flutterViewHeight * (marginBottomRatio ?? defaultMarginBottomRatio);
  }

  double _getFlutterViewHeight(FlutterView view) => MediaQueryData.fromView(view).size.height;
}
