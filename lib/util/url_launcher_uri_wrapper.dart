import 'package:flutter_common_package/common_package/url_launcher.dart' as url_launcher;

import '../prepare_for_app_initiation.dart';

UrlLauncherWrapper urlLauncherWrapper = getIt.get<UrlLauncherWrapper>();

class UrlLauncherWrapper {
  Future<bool> launchUrl(
    Uri url, {
    url_launcher.LaunchMode mode = url_launcher.LaunchMode.platformDefault,
    url_launcher.WebViewConfiguration webViewConfiguration =
        const url_launcher.WebViewConfiguration(),
    String? webOnlyWindowName,
  }) {
    return url_launcher.launchUrl(
      url,
      mode: mode,
      webViewConfiguration: webViewConfiguration,
      webOnlyWindowName: webOnlyWindowName,
    );
  }
}
