import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/util/utils.dart';

abstract class EvoLocalStorageHelper implements CommonLocalStorageHelper {
  Future<Locale> setLocale(String languageCode);

  Future<Locale> getLocale();

  Future<void> setLatestVersionIgnore(String? latestVersion);

  Future<String?> getLatestVersionIgnore();

  Future<void> setDeviceId(String? deviceId);

  Future<String?> getDeviceId();

  Future<void> setUserPhoneNumber(String? userPhoneNumber);

  Future<String?> getUserPhoneNumber();

  Future<void> setUserFullName(String? userFullName);

  Future<String?> getUserFullName();

  Future<String?> getBiometricToken();

  Future<void> setBiometricToken(String? biometricToken);

  Future<bool> isEnableBiometricAuthenticator();

  Future<void> setBiometricAuthenticator(bool enable);

  Future<void> clearAllUserData();

  Future<String?> getTimeShowBiometric();

  Future<void> saveTimeShowBiometric(String timeShow);

  Future<void> setDeviceToken(String? deviceToken);

  Future<String?> getDeviceToken();

  Future<void> setNewDevice(bool status);

  Future<bool> isNewDevice();

  /// Delete all keys except the [EvoSecureStorageHelperImpl.deviceId] key
  /// Because we need this key to check whether the user deleted the application and reinstalled it or not
  /// to delete all data in Keychain on iOS
  /// So, we do not use [await delete(key: deviceId)]
  Future<void> deleteAllSecureStorageData();
}
