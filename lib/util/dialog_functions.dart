import 'package:flutter/material.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/widgets.dart';

import '../feature/verify_otp/verify_otp_page.dart';
import '../model/evo_dialog_id.dart';
import '../prepare_for_app_initiation.dart';
import '../resources/resources.dart';
import '../widget/evo_dialog/dialog_confirm.dart';
import '../widget/evo_dialog/kyko_bottom_sheet.dart';
import '../widget/evo_dialog/kyko_bottom_sheet_action.dart';
import 'evo_flutter_wrapper.dart';

part 'dialog_session_expired_ui_model.dart';

DialogFunction get evoDialogFunction => getIt.get<DialogFunction>();

enum DialogAlertType {
  error,
  warning,
  unsuccessful,
}

enum SessionDialogType {
  resetPin,
  signIn,
  activateAccount;

  static SessionDialogType? fromVerifyOtpType(VerifyOtpType? type) {
    return switch (type) {
      VerifyOtpType.signIn => SessionDialogType.signIn,
      VerifyOtpType.resetPin => SessionDialogType.resetPin,
      VerifyOtpType.activateAccount => SessionDialogType.activateAccount,
      _ => null,
    };
  }
}

class DialogFunction {
  Future<void> showDialogConfirm({
    required String textPositive,
    required EvoDialogId dialogId,
    String? content,
    String? title,
    String? textNegative,
    Widget? footer,
    VoidCallback? onClickPositive,
    VoidCallback? onClickNegative,
    Widget? imageHeader,
    DialogAlertType? alertType,
    bool isDismissible = true,
    ButtonStyle? positiveButtonStyle,
    ButtonStyle? negativeButtonStyle,
    TextStyle? titleTextStyle,
    TextStyle? contentTextStyle,
    Map<String, dynamic>? loggingEventMetaData,
    Map<String, dynamic>? loggingEventOnShowMetaData,
    bool isShowButtonClose = false,
    TextAlign? titleTextAlign,
    TextAlign? contentTextAlign,
    Key? key,
    ButtonListOrientation? buttonListOrientation,
    EdgeInsets? headerPadding,
    EdgeInsets? contentPadding,
    EdgeInsets? ctaPadding,
    double? contentSpacing,
    double? ctaSpacing,
    double? dialogHorizontalPadding,
    VoidCallback? onDismiss,
    bool? autoClosePopupWhenClickCTA = false,
  }) async {
    final Widget? imageHeaderWidget =
        alertType != null ? getImageHeaderByAlertType(alertType) : imageHeader;
    bool isCTAClicked = false;

    await evoFlutterWrapper.showDialog<void>(
      barrierDismissible: isDismissible,
      builder: (_) => PopScope(
        canPop: isDismissible,
        child: EvoDialogConfirm(
          key: key,
          content: content,
          textPositive: textPositive,
          title: title,
          textNegative: textNegative,
          footer: footer,
          onClickPositive: () {
            if (autoClosePopupWhenClickCTA ?? false) {
              navigatorContext?.pop();
            }
            isCTAClicked = true;
            onClickPositive?.call();
          },
          onClickNegative: () {
            if (autoClosePopupWhenClickCTA ?? false) {
              navigatorContext?.pop();
            }
            isCTAClicked = true;
            onClickNegative?.call();
          },
          imageHeader: imageHeaderWidget,
          positiveButtonStyle: positiveButtonStyle,
          negativeButtonStyle: negativeButtonStyle,
          titleTextStyle: titleTextStyle,
          contentTextStyle: contentTextStyle,
          dialogId: dialogId.id,
          loggingEventMetaData: loggingEventMetaData,
          loggingEventOnShowMetaData: loggingEventOnShowMetaData,
          isShowButtonClose: isShowButtonClose,
          titleTextAlign: titleTextAlign,
          contentTextAlign: contentTextAlign,
          buttonListOrientation: buttonListOrientation,
          headerPadding: headerPadding,
          contentPadding: contentPadding,
          ctaPadding: ctaPadding,
          contentSpacing: contentSpacing,
          ctaSpacing: ctaSpacing,
          dialogHorizontalPadding: dialogHorizontalPadding,
        ),
      ),
    );

    if (!isCTAClicked) {
      onDismiss?.call();
    }
  }

  @visibleForTesting
  Widget getImageHeaderByAlertType(DialogAlertType type) {
    final String icon = switch (type) {
      DialogAlertType.error => EvoImages.icAlertError,
      DialogAlertType.warning => EvoImages.icAlertWarning,
      DialogAlertType.unsuccessful => EvoImages.icAlertUnsuccessful,
    };
    return evoImageProvider.asset(icon);
  }

  Future<void> showDialogSessionTokenExpired({
    VoidCallback? onClickPositive,
    SessionDialogType? type = SessionDialogType.signIn,
  }) async {
    final DialogSessionExpiredUiModel? uiModel = switch (type) {
      SessionDialogType.resetPin => DialogSessionExpiredUiModel.resetPin(),
      SessionDialogType.signIn => DialogSessionExpiredUiModel.signIn(),
      SessionDialogType.activateAccount => DialogSessionExpiredUiModel.activateAccount(),
      _ => null,
    };

    if (uiModel == null) {
      commonLog(
        'session dialog type is not supported',
      );
      return;
    }

    await evoDialogFunction.showDialogConfirm(
      alertType: DialogAlertType.error,
      title: uiModel.title,
      content: uiModel.content,
      textPositive: uiModel.textPositive,
      dialogId: uiModel.dialogId,
      isDismissible: false,
      onClickPositive: onClickPositive ??
          () {
            final BuildContext? navigatorCtx = navigatorContext;

            ///dismiss popup
            navigatorCtx?.pop();

            ///pop screen
            navigatorCtx?.pop();
          },
    );
  }

  Future<void> showDialogBottomSheet({
    required String title,
    required Widget content,
    List<KykoBottomSheetAction>? actions,
    bool hasCloseButton = false,
  }) async {
    return evoFlutterWrapper.showBottomSheet<void>(builder: (_) {
      return KykoBottomSheet(
        hasCloseButton: hasCloseButton,
        title: title,
        content: content,
        actions: actions,
      );
    });
  }
}
