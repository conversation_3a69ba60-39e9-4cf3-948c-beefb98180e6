import '../../resources/resources.dart';

class UsernameValidator {
  static const int defaultUsernameMaxLength = 30;

  /// It can only contain letters, numbers, and periods;
  /// It can\'t contain symbols or punctuation marks;
  static const String regexUsername = r'[^a-zA-Z0-9.]';

  static UsernameValidator? _instance;

  UsernameValidator._();

  factory UsernameValidator() {
    return _instance ??= UsernameValidator._();
  }


  String? validate(String? username) {
    if (username == null || username.isEmpty) {
      return EvoStrings.errorUsernameEmpty;
    }

    if (username.length > defaultUsernameMaxLength) {
      return EvoStrings.errorUsernameMaxLength;
    }

    if (RegExp(regexUsername).hasMatch(username)) {
      return EvoStrings.errorUsernameContainsSymbols;
    }

    return null;
  }
}