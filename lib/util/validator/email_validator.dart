// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

abstract final class EmailValidator {
  static const String _emailPattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';

  static bool validate(String? email) {
    if (email == null || email.isEmpty) {
      return false;
    }

    return RegExp(_emailPattern).hasMatch(email);
  }
}
