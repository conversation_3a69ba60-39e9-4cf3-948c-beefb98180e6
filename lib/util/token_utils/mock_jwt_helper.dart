// coverage:ignore-file
// remove after Demo phase
import 'jwt_helper.dart';

/// Thanks to this mock, tokens wouldn't expire.
class MockJwtHelper extends JwtHelper {
  @override
  Map<String, dynamic>? decode(String token) {
    return <String, String>{};
  }

  @override
  bool isExpired(String token) {
    return false;
  }

  @override
  bool isCanUse(String? token, {int bufferTimeExpiredInSec = 60}) {
    return true;
  }
}
