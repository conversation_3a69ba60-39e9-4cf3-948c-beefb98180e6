// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

part of 'dialog_functions.dart';

class DialogSessionExpiredUiModel {
  final String title;
  final String content;
  final String textPositive;
  final EvoDialogId dialogId;

  DialogSessionExpiredUiModel._({
    required this.title,
    required this.content,
    required this.textPositive,
    required this.dialogId,
  });

  DialogSessionExpiredUiModel.signIn()
      : this._(
          title: EvoStrings.titleSessionTokenExpired,
          content: EvoStrings.contentSessionTokenExpiredSignIn,
          textPositive: EvoStrings.textSubmitSessionTokenExpiredSignIn,
          dialogId: EvoDialogId.signInSessionTokenExpiredErrorDialog,
        );

  DialogSessionExpiredUiModel.resetPin()
      : this._(
          title: EvoStrings.titleSessionTokenExpired,
          content: EvoStrings.contentSessionTokenExpiredResetPin,
          textPositive: EvoStrings.ok,
          dialogId: EvoDialogId.resetPinSessionTokenExpiredErrorDialog,
        );

  DialogSessionExpiredUiModel.activateAccount()
      : this._(
          title: EvoStrings.titleSessionTokenExpired,
          content: EvoStrings.contentActivationAccountSessionTokenExpired,
          textPositive: EvoStrings.ctaBackToActivationAccount,
          dialogId: EvoDialogId.activateAccountSessionTokenExpiredErrorDialog,
        );
}
