import 'dart:async';
import 'dart:developer' as developer;
import 'dart:io';

import 'memory_monitor.dart';

/// Implementation of memory monitoring functionality.
///
/// This class provides memory monitoring capabilities to help
/// optimize app performance and detect memory leaks.
class MemoryMonitorImpl implements MemoryMonitor {
  Timer? _monitoringTimer;
  final List<MemoryUsageSample> _memoryHistory = [];
  int _peakMemoryUsage = 0;
  bool _isMonitoring = false;

  @override
  int getCurrentMemoryUsage() {
    try {
      // Try to get memory usage from developer service
      final info = developer.Service.getInfo();
      if (info.serverUri != null) {
        // In debug mode, we can get more detailed memory info
        return ProcessInfo.currentRss;
      }
      
      // Fallback to process RSS
      return ProcessInfo.currentRss;
    } catch (e) {
      // If we can't get memory info, return -1
      return -1;
    }
  }

  @override
  int getPeakMemoryUsage() {
    return _peakMemoryUsage;
  }

  @override
  void startMonitoring({Duration interval = const Duration(seconds: 5)}) {
    if (_isMonitoring) {
      return; // Already monitoring
    }

    _isMonitoring = true;
    _monitoringTimer = Timer.periodic(interval, (_) {
      _recordMemoryUsage();
    });

    // Record initial sample
    _recordMemoryUsage();
  }

  @override
  void stopMonitoring() {
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
    _isMonitoring = false;
  }

  @override
  List<MemoryUsageSample> getMemoryHistory() {
    return List.unmodifiable(_memoryHistory);
  }

  @override
  void clearHistory() {
    _memoryHistory.clear();
    _peakMemoryUsage = 0;
  }

  @override
  Map<String, dynamic> getMemoryStats() {
    if (_memoryHistory.isEmpty) {
      return {
        'current': getCurrentMemoryUsage(),
        'peak': _peakMemoryUsage,
        'average': 0,
        'min': 0,
        'max': 0,
        'samples': 0,
      };
    }

    final memoryValues = _memoryHistory.map((sample) => sample.memoryUsage).toList();
    final sum = memoryValues.reduce((a, b) => a + b);
    final average = sum / memoryValues.length;
    final min = memoryValues.reduce((a, b) => a < b ? a : b);
    final max = memoryValues.reduce((a, b) => a > b ? a : b);

    return {
      'current': getCurrentMemoryUsage(),
      'peak': _peakMemoryUsage,
      'average': average.round(),
      'min': min,
      'max': max,
      'samples': _memoryHistory.length,
      'is_monitoring': _isMonitoring,
    };
  }

  @override
  bool triggerGarbageCollection() {
    try {
      // Force garbage collection
      // Note: This is not guaranteed to work in all environments
      developer.Service.getInfo();
      return true;
    } catch (e) {
      return false;
    }
  }

  void _recordMemoryUsage() {
    final currentUsage = getCurrentMemoryUsage();
    if (currentUsage > 0) {
      final sample = MemoryUsageSample(
        timestamp: DateTime.now(),
        memoryUsage: currentUsage,
      );

      _memoryHistory.add(sample);
      
      // Update peak usage
      if (currentUsage > _peakMemoryUsage) {
        _peakMemoryUsage = currentUsage;
      }

      // Keep only last 100 samples to avoid memory growth
      if (_memoryHistory.length > 100) {
        _memoryHistory.removeAt(0);
      }
    }
  }
}
