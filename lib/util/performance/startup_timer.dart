/// Abstract interface for startup time tracking functionality.
///
/// This interface provides methods for tracking startup times
/// and performance metrics.
abstract class StartupTimer {
  /// Starts a timer with the given name.
  ///
  /// [timerName] Unique name for the timer
  void startTimer(String timerName);

  /// Stops a timer with the given name.
  ///
  /// [timerName] Name of the timer to stop
  /// Returns the elapsed time in milliseconds, or -1 if timer not found
  int stopTimer(String timerName);

  /// Gets the elapsed time for a timer without stopping it.
  ///
  /// [timerName] Name of the timer
  /// Returns the elapsed time in milliseconds, or -1 if timer not found
  int getElapsedTime(String timerName);

  /// Gets all timer results.
  ///
  /// Returns a map of timer names to their elapsed times in milliseconds
  Map<String, int> getAllTimings();

  /// Clears all timers and results.
  void clearAllTimers();

  /// Gets startup performance metrics.
  ///
  /// Returns a map containing various startup performance metrics
  Map<String, dynamic> getStartupMetrics();

  /// Marks a milestone in the startup process.
  ///
  /// [milestoneName] Name of the milestone
  /// [description] Optional description of the milestone
  void markMilestone(String milestoneName, {String? description});

  /// Gets all recorded milestones.
  ///
  /// Returns a list of milestones with timestamps
  List<StartupMilestone> getMilestones();
}

/// Represents a startup milestone.
class StartupMilestone {
  final String name;
  final String? description;
  final DateTime timestamp;
  final int elapsedSinceStart;

  const StartupMilestone({
    required this.name,
    this.description,
    required this.timestamp,
    required this.elapsedSinceStart,
  });

  Map<String, dynamic> toJson() => {
        'name': name,
        'description': description,
        'timestamp': timestamp.toIso8601String(),
        'elapsed_since_start': elapsedSinceStart,
      };
}
