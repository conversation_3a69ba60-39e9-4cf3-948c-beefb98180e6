/// Abstract interface for memory monitoring functionality.
///
/// This interface provides methods for monitoring memory usage
/// to help optimize app performance.
abstract class MemoryMonitor {
  /// Gets the current memory usage in bytes.
  ///
  /// Returns the current memory usage or -1 if unavailable
  int getCurrentMemoryUsage();

  /// Gets the peak memory usage since monitoring started.
  ///
  /// Returns the peak memory usage in bytes
  int getPeakMemoryUsage();

  /// Starts monitoring memory usage.
  ///
  /// [interval] How often to check memory usage
  void startMonitoring({Duration interval = const Duration(seconds: 5)});

  /// Stops monitoring memory usage.
  void stopMonitoring();

  /// Gets memory usage history.
  ///
  /// Returns a list of memory usage samples with timestamps
  List<MemoryUsageSample> getMemoryHistory();

  /// Clears memory usage history.
  void clearHistory();

  /// Gets memory usage statistics.
  ///
  /// Returns a map containing average, min, max memory usage
  Map<String, dynamic> getMemoryStats();

  /// Triggers garbage collection (if possible).
  ///
  /// Returns true if GC was triggered successfully
  bool triggerGarbageCollection();
}

/// Represents a memory usage sample at a specific time.
class MemoryUsageSample {
  final DateTime timestamp;
  final int memoryUsage;

  const MemoryUsageSample({
    required this.timestamp,
    required this.memoryUsage,
  });

  Map<String, dynamic> toJson() => {
        'timestamp': timestamp.toIso8601String(),
        'memory_usage': memoryUsage,
      };
}
