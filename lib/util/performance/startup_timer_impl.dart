import 'startup_timer.dart';

/// Implementation of startup time tracking functionality.
///
/// This class provides startup time tracking capabilities to help
/// measure and optimize app startup performance.
class StartupTimerImpl implements StartupTimer {
  final Map<String, DateTime> _startTimes = {};
  final Map<String, int> _completedTimings = {};
  final List<StartupMilestone> _milestones = [];
  late final DateTime _appStartTime;

  StartupTimerImpl() {
    _appStartTime = DateTime.now();
    markMilestone('app_start', description: 'Application started');
  }

  @override
  void startTimer(String timerName) {
    _startTimes[timerName] = DateTime.now();
  }

  @override
  int stopTimer(String timerName) {
    final startTime = _startTimes[timerName];
    if (startTime == null) {
      return -1; // Timer not found
    }

    final elapsed = DateTime.now().difference(startTime).inMilliseconds;
    _completedTimings[timerName] = elapsed;
    _startTimes.remove(timerName);

    return elapsed;
  }

  @override
  int getElapsedTime(String timerName) {
    final startTime = _startTimes[timerName];
    if (startTime == null) {
      // Check if it's a completed timer
      return _completedTimings[timerName] ?? -1;
    }

    return DateTime.now().difference(startTime).inMilliseconds;
  }

  @override
  Map<String, int> getAllTimings() {
    final Map<String, int> allTimings = Map.from(_completedTimings);

    // Add currently running timers
    for (final entry in _startTimes.entries) {
      allTimings[entry.key] = DateTime.now().difference(entry.value).inMilliseconds;
    }

    return allTimings;
  }

  @override
  void clearAllTimers() {
    _startTimes.clear();
    _completedTimings.clear();
    _milestones.clear();
  }

  @override
  Map<String, dynamic> getStartupMetrics() {
    final currentTime = DateTime.now();
    final totalStartupTime = currentTime.difference(_appStartTime).inMilliseconds;

    final timings = getAllTimings();
    final milestoneData = _milestones.map((m) => m.toJson()).toList();

    // Calculate some basic statistics
    final timingValues = timings.values.where((v) => v > 0).toList();
    final averageTime = timingValues.isEmpty ? 0 : timingValues.reduce((a, b) => a + b) / timingValues.length;
    final maxTime = timingValues.isEmpty ? 0 : timingValues.reduce((a, b) => a > b ? a : b);
    final minTime = timingValues.isEmpty ? 0 : timingValues.reduce((a, b) => a < b ? a : b);

    return {
      'total_startup_time': totalStartupTime,
      'app_start_time': _appStartTime.toIso8601String(),
      'current_time': currentTime.toIso8601String(),
      'timings': timings,
      'milestones': milestoneData,
      'statistics': {
        'total_timers': timings.length,
        'completed_timers': _completedTimings.length,
        'running_timers': _startTimes.length,
        'average_time': averageTime.round(),
        'max_time': maxTime,
        'min_time': minTime,
      },
    };
  }

  @override
  void markMilestone(String milestoneName, {String? description}) {
    final now = DateTime.now();
    final elapsedSinceStart = now.difference(_appStartTime).inMilliseconds;

    final milestone = StartupMilestone(
      name: milestoneName,
      description: description,
      timestamp: now,
      elapsedSinceStart: elapsedSinceStart,
    );

    _milestones.add(milestone);
  }

  @override
  List<StartupMilestone> getMilestones() {
    return List.unmodifiable(_milestones);
  }
}
