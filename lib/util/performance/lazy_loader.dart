/// Abstract interface for lazy loading functionality.
///
/// This interface provides methods for lazy loading modules and features
/// to improve app startup performance and memory usage.
abstract class LazyLoader {
  /// Loads a module lazily when it's first needed.
  ///
  /// [moduleName] The name of the module to load
  /// [priority] Loading priority (higher numbers load first)
  /// Returns true if the module was loaded successfully
  Future<bool> loadModule(String moduleName, {int priority = 0});

  /// Loads multiple modules lazily in order of priority.
  ///
  /// [moduleNames] List of module names to load
  /// Returns a map of module names to their loading success status
  Future<Map<String, bool>> loadModules(List<String> moduleNames);

  /// Preloads modules that are likely to be needed soon.
  ///
  /// [moduleNames] List of module names to preload
  /// [delay] Delay before starting preload (to avoid blocking startup)
  Future<void> preloadModules(List<String> moduleNames, {Duration? delay});

  /// Checks if a module is currently loaded.
  ///
  /// [moduleName] The name of the module to check
  /// Returns true if the module is loaded
  bool isModuleLoaded(String moduleName);

  /// Gets the loading status of all modules.
  ///
  /// Returns a map of module names to their loading status
  Map<String, bool> getLoadingStatus();

  /// Unloads a module to free memory.
  ///
  /// [moduleName] The name of the module to unload
  /// Returns true if the module was unloaded successfully
  Future<bool> unloadModule(String moduleName);

  /// Gets performance metrics for lazy loading.
  ///
  /// Returns a map containing loading times, memory usage, etc.
  Map<String, dynamic> getPerformanceMetrics();
}
