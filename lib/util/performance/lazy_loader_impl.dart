import 'dart:async';

import 'package:flutter_common_package/init_common_package.dart';

import 'lazy_loader.dart';
import 'startup_timer.dart';
import 'memory_monitor.dart';
import '../../base/modules/module_names.dart';

/// Implementation of lazy loading functionality.
///
/// This class provides lazy loading capabilities for modules to improve
/// app startup performance and memory usage.
class LazyLoaderImpl implements LazyLoader {
  final StartupTimer _startupTimer;
  final MemoryMonitor _memoryMonitor;
  final Map<String, bool> _loadedModules = {};
  final Map<String, DateTime> _loadingTimes = {};
  final Map<String, int> _loadingPriorities = {};

  LazyLoaderImpl({
    required StartupTimer startupTimer,
    required MemoryMonitor memoryMonitor,
  })  : _startupTimer = startupTimer,
        _memoryMonitor = memoryMonitor;

  @override
  Future<bool> loadModule(String moduleName, {int priority = 0}) async {
    if (_loadedModules[moduleName] == true) {
      return true; // Already loaded
    }

    try {
      _startupTimer.startTimer('load_module_$moduleName');
      _loadingPriorities[moduleName] = priority;

      // Check if module is already initialized by the modular system
      if (isModuleInitialized(moduleName)) {
        _loadedModules[moduleName] = true;
        _loadingTimes[moduleName] = DateTime.now();
        _startupTimer.stopTimer('load_module_$moduleName');
        return true;
      }

      // Initialize the module using the modular system
      await initializeSpecificModules([moduleName]);

      _loadedModules[moduleName] = true;
      _loadingTimes[moduleName] = DateTime.now();
      _startupTimer.stopTimer('load_module_$moduleName');

      return true;
    } catch (e) {
      _loadedModules[moduleName] = false;
      _startupTimer.stopTimer('load_module_$moduleName');
      return false;
    }
  }

  @override
  Future<Map<String, bool>> loadModules(List<String> moduleNames) async {
    final Map<String, bool> results = {};

    // Sort by priority (higher priority first)
    final sortedModules = List<String>.from(moduleNames);
    sortedModules.sort((a, b) {
      final priorityA = _loadingPriorities[a] ?? 0;
      final priorityB = _loadingPriorities[b] ?? 0;
      return priorityB.compareTo(priorityA);
    });

    for (final moduleName in sortedModules) {
      results[moduleName] = await loadModule(moduleName);
    }

    return results;
  }

  @override
  Future<void> preloadModules(List<String> moduleNames, {Duration? delay}) async {
    final preloadDelay = delay ?? const Duration(milliseconds: 500);

    // Wait for the specified delay to avoid blocking startup
    await Future.delayed(preloadDelay);

    // Preload modules in background
    unawaited(_preloadInBackground(moduleNames));
  }

  Future<void> _preloadInBackground(List<String> moduleNames) async {
    for (final moduleName in moduleNames) {
      if (!isModuleLoaded(moduleName)) {
        await loadModule(moduleName, priority: -1); // Low priority for preload

        // Small delay between preloads to avoid overwhelming the system
        await Future.delayed(const Duration(milliseconds: 100));
      }
    }
  }

  @override
  bool isModuleLoaded(String moduleName) {
    return _loadedModules[moduleName] == true || isModuleInitialized(moduleName);
  }

  @override
  Map<String, bool> getLoadingStatus() {
    final Map<String, bool> status = Map.from(_loadedModules);

    // Add status for all known modules
    for (final moduleName in _getAllKnownModules()) {
      if (!status.containsKey(moduleName)) {
        status[moduleName] = isModuleInitialized(moduleName);
      }
    }

    return status;
  }

  @override
  Future<bool> unloadModule(String moduleName) async {
    // Note: The current modular system doesn't support unloading modules
    // This is a placeholder for future implementation
    _loadedModules[moduleName] = false;
    return true;
  }

  @override
  Map<String, dynamic> getPerformanceMetrics() {
    final loadingTimes = <String, int>{};
    final currentTime = DateTime.now();

    for (final entry in _loadingTimes.entries) {
      loadingTimes[entry.key] = currentTime.difference(entry.value).inMilliseconds;
    }

    return {
      'loaded_modules': _loadedModules.length,
      'total_modules': _getAllKnownModules().length,
      'loading_times': loadingTimes,
      'memory_usage': _memoryMonitor.getCurrentMemoryUsage(),
      'startup_times': _startupTimer.getAllTimings(),
    };
  }

  List<String> _getAllKnownModules() {
    return [
      // Core modules
      EvoAppModuleNames.storage,
      EvoAppModuleNames.auth,
      EvoAppModuleNames.biometric,
      EvoAppModuleNames.navigation,

      // Data modules
      EvoAppModuleNames.api,
      EvoAppModuleNames.repository,
      EvoAppModuleNames.cache,

      // UI modules
      EvoAppModuleNames.theme,
      EvoAppModuleNames.components,

      // Utility modules
      EvoAppModuleNames.validation,
      EvoAppModuleNames.logging,
      EvoAppModuleNames.privilegeAction,
      EvoAppModuleNames.performance,

      // Feature modules
      EvoAppModuleNames.login,
      EvoAppModuleNames.mainScreen,
      EvoAppModuleNames.profile,
      EvoAppModuleNames.pin,
      EvoAppModuleNames.ekyc,
      EvoAppModuleNames.transactionDetails,
      EvoAppModuleNames.accountActivation,
      EvoAppModuleNames.verifyOtp,
    ];
  }
}
