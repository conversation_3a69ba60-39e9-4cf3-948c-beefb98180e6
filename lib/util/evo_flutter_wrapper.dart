import 'package:flutter/material.dart' as material;
import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/device_platform.dart';

import '../prepare_for_app_initiation.dart';

EvoFlutterWrapper get evoFlutterWrapper => getIt.get<EvoFlutterWrapper>();

class EvoFlutterWrapper {
  final DevicePlatform _platform;

  EvoFlutterWrapper({DevicePlatform? platform}) : _platform = platform ?? DevicePlatformImp();

  bool isAndroid() {
    return _platform.isAndroid();
  }

  bool isIOS() {
    return _platform.isIOS();
  }

  Future<T?> showDialog<T>({
    required material.WidgetBuilder builder,
    bool barrierDismissible = true,
    bool useSafeArea = true,
  }) {
    final BuildContext? context = navigatorContext;
    if (context == null) {
      return Future<T?>.value();
    }
    return material.showDialog<T>(
      context: context,
      builder: builder,
      useSafeArea: useSafeArea,
      barrierDismissible: barrierDismissible,
    );
  }

  Future<T?> showBottomSheet<T>({
    required material.WidgetBuilder builder,
    bool isDismissible = true,
    bool enableDrag = false,
  }) {
    final BuildContext? context = navigatorContext;
    if (context == null) {
      return Future<T?>.value();
    }

    return material.showModalBottomSheet(
      enableDrag: enableDrag,
      context: context,
      useSafeArea: true,
      isScrollControlled: true,
      builder: builder,
      isDismissible: isDismissible,
    );
  }
}
