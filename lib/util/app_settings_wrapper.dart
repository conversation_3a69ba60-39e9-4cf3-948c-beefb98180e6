import 'package:app_settings/app_settings.dart';

import '../prepare_for_app_initiation.dart';

AppSettingsWrapper get appSettingsWrapper => getIt.get<AppSettingsWrapper>();

class AppSettingsWrapper {
  Future<void> openAppSettings({
    AppSettingsType type = AppSettingsType.settings,
    bool asAnotherTask = false,
  }) {
    return AppSettings.openAppSettings(
      type: type,
      asAnotherTask: asAnotherTask,
    );
  }
}
