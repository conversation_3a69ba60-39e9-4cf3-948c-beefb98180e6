import 'package:flutter_common_package/flavors/flavor_config.dart';

import '../evo_flavor_value_config.dart';
import 'evo_flavor.dart';

class EvoFlavorStag extends EvoFlavor {
  @override
  CommonFlavorValues getFlavorValue() {
    return CommonFlavorValues(
      baseUrl: EvoFlavorValueConfig.baseUrlStag,
      initializeFirebaseSdk: true,
      // Staging - Onesignal account management: <EMAIL>
      oneSignalAppId: EvoFlavorValueConfig.oneSignalAppIdStag,
    );
  }
}
