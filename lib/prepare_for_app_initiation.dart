import 'package:evoapp/util/token_utils/mock_jwt_helper.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/common_package/intl.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/dio_http_client_impl.dart';
import 'package:flutter_common_package/data/http_client/dio_log_interceptor.dart';
import 'package:flutter_common_package/data/repository/ekyc/ekyc_repo.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo_impl.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/resources/button_dimensions.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_common_package/util/flutter_downloader/common_flutter_downloader.dart';
import 'package:flutter_common_package/widget/default_widgets.dart';
import 'package:local_auth/local_auth.dart';
import 'package:ts_bio_detect_changed/ts_bio_detect_changed.dart';

import 'data/repository/authentication_repo.dart';
import 'data/repository/authentication_repo_impl.dart';
import 'data/repository/common_repo.dart';
import 'data/repository/common_repo_impl.dart';
import 'data/repository/mock_ekyc_repo.dart';
import 'data/repository/user_repo.dart';
import 'data/repository/user_repo_impl.dart';
import 'data/response/reset_pin_entity.dart';
import 'feature/authorization_session_expired/authorization_session_expired.dart';
import 'feature/authorization_session_expired/authorization_session_expired_popup.dart';
import 'feature/authorization_session_expired/force_logout_popup.dart';
import 'feature/biometric/biometric_token_module/biometrics_token_module.dart';
import 'feature/biometric/model/biometric_status_change_notifier.dart';
import 'feature/biometric/model/biometric_ui_model.dart';
import 'feature/biometric/request_user_active_biometric/request_user_active_biometric_handler.dart';
import 'feature/biometric/request_user_active_biometric/request_user_active_biometric_handler_impl.dart';
import 'feature/biometric/request_user_active_biometric/request_user_active_biometric_util.dart';
import 'feature/biometric/utils/biometric_functions.dart';
import 'feature/biometric/utils/biometric_status_helper.dart';
import 'feature/biometric/utils/biometric_status_helper_impl.dart';
import 'feature/biometric/utils/biometric_type_helper.dart';
import 'feature/biometric/utils/biometrics_authenticate.dart';
import 'feature/biometric/utils/biometrics_authenticate_impl.dart';
import 'feature/feature_toggle.dart';
import 'feature/inactive_detector/inactive_detector.dart';
import 'feature/logging/event_tracking_shared_data.dart';
import 'feature/logging/evo_event_tracking_utils/evo_event_tracking_utils.dart';
import 'feature/logging/evo_event_tracking_utils/evo_event_tracking_utils_impl.dart';
import 'feature/logging/evo_navigator_observer.dart';
import 'feature/login/utils/login_old_device_utils.dart';
import 'feature/pin/models/change_pin_status.dart';
import 'feature/pin/reset_pin/reset_pin_handler.dart';
import 'feature/pin/reset_pin/reset_pin_handler_impl.dart';
import 'feature/pin/reset_pin/reset_pin_ui_handler.dart';
import 'feature/privilege_action/privilege_access_guard_module.dart';
import 'feature/privilege_action/privilege_action_handler/privilege_action_factory.dart';
import 'feature/privilege_action/verify_biometric_privilege_action/verify_biometric_privilege_action.dart';
import 'feature/splash_screen/utils/exit_app_feature/exit_app_feature.dart';
import 'feature/splash_screen/utils/exit_app_feature/exit_app_feature_impl.dart';
import 'feature/splash_screen/utils/secure_detection_utils/secure_detection.dart';
import 'feature/splash_screen/utils/secure_detection_utils/secure_detection_impl.dart';
import 'model/user_info_notifier.dart';
import 'model/user_status.dart';
import 'model/user_token.dart';
import 'resources/button_dimensions.dart';
import 'resources/button_styles.dart';
import 'resources/input_borders.dart';
import 'resources/resources.dart';
import 'util/app_settings_wrapper.dart';
import 'util/dialog_functions.dart';
import 'util/evo_flutter_wrapper.dart';
import 'util/evo_snackbar.dart';
import 'util/functions.dart';
import 'util/interceptor/log_event_interceptor.dart';
import 'util/interceptor/unauthorized_interceptor.dart';
import 'util/navigator/evo_router_navigator.dart';
import 'util/secure_storage_helper/secure_storage_helper.dart';
import 'util/secure_storage_helper/secure_storage_helper_impl.dart';
import 'util/token_utils/evo_jwt_helper_impl.dart';
import 'util/token_utils/jwt_helper.dart';
import 'util/url_launcher_uri_wrapper.dart';
import 'util/validator/evo_validator.dart';
import 'util/validator/mpin_validator.dart';
import 'widget/evo_default_widget.dart';

class AppState {
  AppState._();

  static AppState? _singleton;

  factory AppState() {
    _singleton ??= AppState._();
    return _singleton!;
  }

  Locale locale = const Locale('vi');

  String? appVersion;

  UserInfoNotifier userInfo = UserInfoNotifier(null);

  bool? isCheckShowUpdateOnce;

  bool isUserLogIn = false;

  BiometricTypeUIModel bioTypeInfo = BiometricTypeUIModel.faceAndFinger();

  BiometricStatusChangeNotifier biometricStatusChangeNotifier =
      BiometricStatusChangeNotifier(BiometricStatus.usable);

  /// When a user taps a notification, an action may need to be executed that requires the user to log in first.
  /// If the user is not logged in (e.g: Token is expired, ...), we will save the action in this field [actionAfterLogin] and redirect the user to the login screen.
  /// After the user has successfully logged in, we will execute this action in the [Screen.mainScreen].
  /// Reference: https://trustingsocial1.atlassian.net/browse/EMA-1211
  void Function()? actionAfterLogin;

  /// Event Tracking
  EventTrackingSharedData? _eventTrackingSharedData;

  EventTrackingSharedData get eventTrackingSharedData {
    return _eventTrackingSharedData ??= EventTrackingSharedData();
  }

  // return the tracking screen id or undefined
  EventTrackingScreenId get currentScreenId =>
      eventTrackingSharedData.currentScreenId ?? EventTrackingScreenId.undefined;

  /// The [appLifecycleState] variable stores the app's life cycle state when didChangeAppLifecycleState is called in EvoPageStateBase.
  /// It can be used to check if the app is in the background OR if some system dialogs are showing.
  AppLifecycleState? appLifecycleState;

  /// The [_userToken] variable stores the user's token when they login.
  UserToken? _userToken;

  UserToken get userToken => _userToken ??= UserToken();

  set userToken(UserToken? userToken) {
    _userToken = userToken;
  }

  /// Determine if the user account have just activated or not
  UserStatus? userStatus;

  InactiveDetectorController inactiveDetectorController = InactiveDetectorController();

  /// When user process change mpin and being locked by exceeded limit rate
  /// [changePinStatusNotifier] will update ui of `Change Pin` tile in [ProfileScreen]
  /// Refer: https://trustingsocial1.atlassian.net/browse/EMA-3722
  ChangePinStatusNotifier changePinStatusNotifier = ChangePinStatusNotifier();
}

GetIt getIt = GetIt.instance;
const String nonAuthenticationHttpClientInstance = 'NonAuthenticationHttpClient';

Future<void> prepareForAppInitiation() async {
  await initCommonPackage(locale: defaultLocale);

  _registerFlutterWrapper();

  _registerEvoUtilFunction();

  _registerValidator();

  _registerDialogFunction();

  _registerSecureDetection();

  _registerExitAppFeature();

  await initAppState(locale: defaultLocale);

  await _registerNonAuthenticationHttpClient();

  await _initializeFlutterDownloader();

  _setUpUnauthorizedSessionHandler();

  _registerNotifyMessage();

  _registerLoggingFeatures();

  //init navigator
  getIt.registerLazySingleton<EvoNavigatorTypeFactory>(() => EvoNavigatorTypeFactory());
  getIt.registerLazySingleton<CommonNavigator>(() => EvoRouterNavigator());

  _registerUiComponents();

  _registerLocalStorages();

  _registerJwtHelper();

  /// this method needs to be called after the [initCommonPackage] method is already called.
  await _registerRepositories();

  await registerBiometrics();

  setUpDioInterceptor();

  _registerFeatureToggle();

  _setUpActiveBiometricHandler();

  await _setUpBiometricUtilities();

  _registerResetPin();

  _registerUrlLauncherWrapper();

  _registerAppSettingsWrapper();

  _registerLoginOldDeviceUtils();

  _registerPrivilegeActionModule();
}

void _registerEvoUtilFunction() {
  getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());
}

void _registerSecureDetection() {
  getIt.registerLazySingleton<SecureDetection>(() => SecureDetectionImpl());
}

void _registerExitAppFeature() {
  getIt.registerLazySingleton<ExitAppFeature>(() => ExitAppFeatureImpl());
}

Future<void> initAppState({Locale locale = const Locale('vi')}) async {
  final PackageInfo packageInfo = await getIt.getAsync<PackageInfo>();

  final AppState appState = AppState();
  appState.locale = locale;
  appState.appVersion = packageInfo.version;

  getIt.registerLazySingleton<AppState>(() => appState);

  Intl.defaultLocale = appState.locale.languageCode;
}

void _registerUiComponents() {
  getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());

  getIt.registerLazySingleton<CommonColors>(() => EvoColors());

  getIt.registerLazySingleton<CommonButtonDimensions>(() => EvoButtonDimensions());
  getIt.registerLazySingleton<CommonButtonStyles>(() => EvoButtonStyles());
  getIt.registerLazySingleton<CommonDefaultWidgets>(() => EvoDefaultWidget());
  getIt.registerLazySingleton<EvoInputBorders>(() => EvoInputBorders());
}

void _registerNotifyMessage() {
  getIt.registerLazySingleton<EvoSnackBar>(() => EvoSnackBar(SnackBarWrapper()));
}

void _registerLocalStorages() {
  // Local storage
  getIt.registerLazySingleton<EvoLocalStorageHelper>(
      () => EvoSecureStorageHelperImpl(secureStorage: getIt.get<FlutterSecureStorage>()));
}

void _registerJwtHelper() {
  // TODO: switch to EvoJwtHelperImpl after Demo phase
  getIt.registerLazySingleton<JwtHelper>(() => MockJwtHelper());
  // getIt.registerLazySingleton<JwtHelper>(() => EvoJwtHelperImpl());
}

/// Because this method is using instances that provide by [initCommonPackage] method,
/// it needs to be called after the [initCommonPackage] method is already called.
Future<void> _registerRepositories() async {
  // User Repo
  getIt.registerLazySingleton<UserRepo>(() => UserRepoImpl(
        getIt.get<CommonHttpClient>(),
        getIt.get<EvoLocalStorageHelper>(),
        getIt.get<AppState>(),
      ));

  getIt.registerLazySingleton<AuthenticationRepo>(
    () => AuthenticationRepoImpl(
      evoHttpClient: getIt.get<CommonHttpClient>(),
      nonAuthenticationEvoHttpClient:
          getIt.get<CommonHttpClient>(instanceName: nonAuthenticationHttpClientInstance),
      evoLocalStorageHelper: getIt.get<EvoLocalStorageHelper>(),
      appState: getIt.get<AppState>(),
    ),
  );

  getIt.registerLazySingleton<CommonRepo>(() => CommonRepoImpl(getIt.get<CommonHttpClient>()));

  /// TODO: hoang-nguyen remove mock
  if (getIt.isRegistered<EkycRepo>()) {
    getIt.unregister<EkycRepo>();
    getIt.registerSingleton<EkycRepo>(MockEkycRepoImpl(
      getIt.get<CommonHttpClient>(),
    ));
  }
}

void _setUpUnauthorizedSessionHandler() {
  getIt.registerLazySingleton<AuthorizationSessionExpiredHandler>(
      () => AuthorizationSessionExpiredHandlerImpl());

  getIt.registerLazySingleton<AuthorizationSessionExpiredPopup>(
      () => AuthorizationSessionExpiredPopup());

  getIt.registerLazySingleton<ForceLogoutPopup>(() => ForceLogoutPopup());
}

void _setUpActiveBiometricHandler() {
  getIt.registerLazySingleton<RequestUserActiveBiometricUtil>(
      () => RequestUserActiveBiometricUtil(localStorageHelper: getIt.get<EvoLocalStorageHelper>()));

  getIt.registerFactory<RequestUserActivateBiometricHandler>(() =>
      RequestUserActiveBiometricHandlerImp(
          requestUserActiveBiometricUtil: getIt.get<RequestUserActiveBiometricUtil>(),
          biometricsAuthenticate: getIt.get<BiometricsAuthenticate>()));
}

Future<void> _setUpBiometricUtilities() async {
  /// BiometricStatusHelper helps to check the biometric status of the device.
  /// Status can be [BiometricStatus.notSetup], [BiometricStatus.deviceSettingChanged], [BiometricStatus.biometricTokenUnusable], [BiometricStatus.usable]
  getIt.registerLazySingleton<BiometricStatusHelper>(() => BiometricStatusHelperImpl());

  /// BiometricUtilityFunctions provides utility function for feature related to biometric
  getIt.registerLazySingleton<BiometricFunctions>(() => BiometricFunctions());
}

Future<void> _registerNonAuthenticationHttpClient() async {
  /// We need a separate Dio to handle refresh token which will not apply [UnauthorizedInterceptor].
  /// Use can use it to make api call which don't need authentication (don't put access token to header)
  final Dio nonAuthenticationDio = Dio();

  if (kDebugMode) {
    nonAuthenticationDio.interceptors.add(
      DioLogInterceptor(
        responseHeaderKeyLogged: <String>[CommonHttpClient.xRequestIdHeader],
      ),
    );
  }

  getIt.registerLazySingleton<Dio>(
    () => nonAuthenticationDio,
    instanceName: nonAuthenticationHttpClientInstance,
  );

  getIt.registerLazySingleton<CommonHttpClient>(
    () => DioClientImpl(
      getIt.get<Dio>(
        instanceName: nonAuthenticationHttpClientInstance,
      ),
    ),
    instanceName: nonAuthenticationHttpClientInstance,
  );
}

/// This function allow you to add interceptors to Dio [https://pub.dev/packages/dio#interceptors]
/// Interceptor sequentially handle event
/// So that interceptors order does matter
/// 1. [LogInterceptor] for logging api called (debug only)
/// 2. [LogEventInterceptor] will skipped onError if path is `events`
/// 3. [UnauthorizedInterceptor] final touch for handling access_token and refresh token
@visibleForTesting
void setUpDioInterceptor() {
  final Dio dio = getIt.get<Dio>();
  dio.interceptors.addAll(
    <Interceptor>[
      if (kDebugMode)
        DioLogInterceptor(
          responseHeaderKeyLogged: <String>[CommonHttpClient.xRequestIdHeader],
        ),
      LogEventInterceptor(eventLogPath: LoggingRepoImpl.logUrl),
      UnauthorizedInterceptor(
        getIt<AuthorizationSessionExpiredHandler>(),
        getIt<AuthenticationRepo>(),
        getIt<AppState>(),
        getIt<JwtHelper>(),
        ignoredRefreshTokenApiPath: <String>[
          AuthenticationRepoImpl.signInUrl,
        ],
        ignoredVerdictEmitUnauthorized: <String>[
          ResetPinEntity.verdictExpiredResetPinSession,
          ResetPinEntity.verdictInvalidResetPinSession,
          ResetPinEntity.verdictMissingResetPinSession,
        ],
      ),
    ],
  );
}

Future<void> registerBiometrics() async {
  final BiometricsAuthenticate biometricAuthenticate =
      BiometricAuthenticateImpl(localAuth: LocalAuthentication(), isForceStrongBiometric: true);
  getIt.registerLazySingleton<BiometricsAuthenticate>(() => biometricAuthenticate);

  getIt.registerLazySingleton<TsBioDetectChanged>(() => TsBioDetectChanged());

  getIt.registerFactory<BiometricsTokenModule>(
    () => BiometricsTokenModule(
      biometricsAuthenticate: getIt.get<BiometricsAuthenticate>(),
      userRepo: getIt.get<UserRepo>(),
      secureStorageHelper: getIt.get<EvoLocalStorageHelper>(),
      bioDetectChanged: getIt.get<TsBioDetectChanged>(),
      jwtHelper: getIt.get<JwtHelper>(),
    ),
  );

  getIt.registerLazySingleton<BiometricTypeHelper>(() => BiometricTypeHelper(
      getIt.get<BiometricsAuthenticate>(), getIt.get<DeviceInfoPlugin>(), DevicePlatformImp()));
}

void _registerFeatureToggle() {
  getIt.registerLazySingleton<FeatureToggle>(() => FeatureToggle());
}

void _registerResetPin() {
  getIt.registerFactory<ResetPinHandler>(
    () => ResetPinHandlerImpl(
      resetPinUiHandler: ResetPinUiHandler(),
      authRepo: getIt.get<AuthenticationRepo>(),
    ),
  );
}

void _registerFlutterWrapper() {
  getIt.registerLazySingleton<EvoFlutterWrapper>(() => EvoFlutterWrapper());
}

void _registerUrlLauncherWrapper() {
  getIt.registerLazySingleton<UrlLauncherWrapper>(() => UrlLauncherWrapper());
}

void _registerAppSettingsWrapper() {
  getIt.registerLazySingleton<AppSettingsWrapper>(() => AppSettingsWrapper());
}

Future<void> _initializeFlutterDownloader() {
  final CommonFlutterDownloader flutterDownloaderWrapper = getIt.get<CommonFlutterDownloader>();
  return flutterDownloaderWrapper.initialize();
}

void _registerLoggingFeatures() {
  getIt.registerLazySingleton<EvoEventTrackingUtils>(() => EvoEventTrackingUtilsImpl());
  getIt.registerLazySingleton<EvoNavigatorObserver>(() => EvoNavigatorObserver());
}

void _registerLoginOldDeviceUtils() {
  getIt.registerLazySingleton<LoginOldDeviceUtils>(() => LoginOldDeviceUtils());
}

void _registerDialogFunction() {
  getIt.registerLazySingleton<DialogFunction>(() => DialogFunction());
}

void _registerValidator() {
  getIt.registerLazySingleton<EvoValidator>(() => EvoValidator());
  getIt.registerLazySingleton<MpinValidator>(() => MpinValidator());
}

void _registerPrivilegeActionModule() {
  getIt.registerFactory(() => VerifyBiometricForPrivilegeAction(
        biometricsAuthenticate: getIt.get<BiometricsAuthenticate>(),
        secureStorageHelper: getIt.get<EvoLocalStorageHelper>(),
        jwtHelper: getIt.get<JwtHelper>(),
        evoUtilFunction: getIt.get<EvoUtilFunction>(),
      ));

  getIt.registerFactory<PrivilegeAccessGuardModule>(
    () => PrivilegeAccessGuardModule(
      biometricForPrivilegeAction: getIt.get<VerifyBiometricForPrivilegeAction>(),
      privilegeActionFactory: PrivilegeActionHandlerFactory(),
    ),
  );
}
