import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/feature/data_collection/data_collector.dart';
import 'package:flutter_common_package/util/share_preference_helper.dart';

import '../../data/repository/authentication_repo.dart';
import '../../main.dart';
import '../../prepare_for_app_initiation.dart';
import '../../util/functions.dart';
import '../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../util/token_utils/jwt_helper.dart';
import '../biometric/utils/biometric_functions.dart';
import '../biometric/utils/biometric_status_helper.dart';
import '../biometric/utils/biometric_type_helper.dart';
import '../biometric/utils/biometrics_authenticate.dart';
import 'utils/secure_detection_utils/secure_detection.dart';

part 'splash_screen_state.dart';

class SplashScreenCubit extends CommonCubit<SplashScreenState> {
  final AuthenticationRepo authenticationRepo;
  final EvoLocalStorageHelper localStorageHelper;
  final JwtHelper jwtHelper;
  final BiometricTypeHelper biometricTypeHelper;
  final AppState appState;
  final BiometricStatusHelper biometricStatusHelper;
  final SecureDetection secureDetection;
  final DataCollector dataCollector;

  SplashScreenCubit({
    required this.authenticationRepo,
    required this.localStorageHelper,
    required this.jwtHelper,
    required this.biometricTypeHelper,
    required this.appState,
    required this.biometricStatusHelper,
    required this.secureDetection,
    required this.dataCollector,
  }) : super(SplashScreenInitialState());

  Future<void> initData() async {
    final bool isSecureDevice = await secureDetection.isSecureDevice();
    if (!isSecureDevice) {
      // delete all data of local storage
      localStorageHelper.deleteAllData();
      emit(SplashScreenCompletedState(statusApp: StatusApp.insecureDevice));
      return;
    }

    /// The order of the following function calls is **IMPORTANT** because.
    /// for iOS device:
    ///   * [getAppStatus] will check if app which is **NEW** installation or not.
    ///   * if YES, [handleStatusApp] will clear all OLD data in SecureStorage. (reason: **iOS KeyChain** used in FlutterSecureStorage,
    ///   it still keep old data after uninstall app)
    /// After that, we will invoke [localStorageHelper.loadAllDataIntoMemory()] & [biometricStatusHelper.updateBiometricStatus()].
    /// to ensure data is loaded correctly
    StatusApp statusApp = await getAppStatus();
    await handleStatusApp(statusApp);

    await localStorageHelper.loadAllDataIntoMemory();

    await biometricStatusHelper.updateBiometricStatus();

    if (statusApp == StatusApp.tutorial) {
      emit(SplashScreenCompletedState(statusApp: statusApp));
    } else {
      /// Get status app, If the tutorial is passed.
      statusApp = await getAppStatusIfUserPassedTutorial();

      /// Clear user data if the status app is not logged in.
      await clearOldDataIfNeed(statusApp);

      emit(SplashScreenCompletedState(statusApp: statusApp));
    }

    final TsBiometricType biometricType = await biometricTypeHelper.getTsBiometricType();
    appState.bioTypeInfo = biometricFunctions.getBiometricUIModel(biometricType);
  }

  @visibleForTesting
  Future<void> clearOldDataIfNeed(StatusApp statusApp) async {
    if (statusApp != StatusApp.hadLoggedIn) {
      await evoUtilFunction.clearDataOnTokenInvalid();
    }
  }

  void startMobileDataCollecting() {
    dataCollector.logMobileDataCollection();
  }

  @visibleForTesting
  Future<StatusApp> getAppStatus() async {
    final CommonSharedPreferencesHelper sharedPreferencesHelper =
        getIt.get<CommonSharedPreferencesHelper>();

    final bool isPassedTutorial = await sharedPreferencesHelper.isPassedTutorial() ?? false;
    final bool isUserReInstallApp = await evoUtilFunction.detectReinstallAppOnIOSDevice();
    if (isUserReInstallApp || !isPassedTutorial) {
      return StatusApp.tutorial;
    }

    final StatusApp statusApp = await getAppStatusIfUserPassedTutorial();
    return statusApp;
  }

  @visibleForTesting
  Future<StatusApp> getAppStatusIfUserPassedTutorial() async {
    final String? deviceToken = await localStorageHelper.getDeviceToken();
    final String? userPhoneNumber = await localStorageHelper.getUserPhoneNumber();
    if (hadUserLoggedIn(phoneNumber: userPhoneNumber, deviceToken: deviceToken)) {
      return StatusApp.hadLoggedIn;
    }

    return StatusApp.nonUser;
  }

  @visibleForTesting
  Future<void> handleStatusApp(StatusApp statusApp) async {
    switch (statusApp) {
      case StatusApp.hadLoggedIn:

        /// do nothing
        return;
      case StatusApp.nonUser:
        handleWhenUserNonUser();
        return;
      case StatusApp.tutorial:
      default:
        await handleWhenUserReInstallApp();
    }
  }

  @visibleForTesting
  Future<void> handleWhenUserReInstallApp() async {
    await evoUtilFunction.deleteAllData();
    await evoUtilFunction.setNewDeviceId();
  }

  @visibleForTesting
  Future<void> handleWhenUserNonUser() {
    return evoUtilFunction.clearAllUserData();
  }

  @visibleForTesting
  bool hadUserLoggedIn({String? phoneNumber, String? deviceToken}) {
    if (phoneNumber == null || phoneNumber.isEmpty) {
      return false;
    }

    if (deviceToken == null || deviceToken.isEmpty || jwtHelper.isExpired(deviceToken)) {
      return false;
    }

    return true;
  }
}
