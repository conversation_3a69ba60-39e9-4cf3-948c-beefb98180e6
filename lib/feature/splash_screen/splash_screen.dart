import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/data_collection/data_collector.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';

import '../../base/evo_page_state_base.dart';
import '../../data/repository/authentication_repo.dart';
import '../../main.dart';
import '../../model/evo_dialog_id.dart';
import '../../prepare_for_app_initiation.dart';
import '../../resources/resources.dart';
import '../../util/dialog_functions.dart';
import '../../util/screen_util.dart';
import '../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../util/token_utils/jwt_helper.dart';
import '../../widget/app_loading_indicator.dart';
import '../biometric/utils/biometric_status_helper.dart';
import '../biometric/utils/biometric_type_helper.dart';
import '../login/old_device/login_on_old_device_screen.dart';
import '../welcome/introduction/introduction_screen.dart';
import '../welcome/welcome_screen.dart';
import 'splash_screen_cubit.dart';
import 'utils/exit_app_feature/exit_app_feature.dart';
import 'utils/secure_detection_utils/secure_detection.dart';

class SplashScreen extends PageBase {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.splashScreen.routeName);
}

class _SplashScreenState extends EvoPageStateBase<SplashScreen> {
  late final SplashScreenCubit _cubit = context.read<SplashScreenCubit?>() ??
      SplashScreenCubit(
        authenticationRepo: getIt.get<AuthenticationRepo>(),
        localStorageHelper: getIt.get<EvoLocalStorageHelper>(),
        jwtHelper: getIt.get<JwtHelper>(),
        biometricTypeHelper: getIt.get<BiometricTypeHelper>(),
        appState: getIt.get<AppState>(),
        biometricStatusHelper: getIt.get<BiometricStatusHelper>(),
        secureDetection: getIt.get<SecureDetection>(),
        dataCollector: getIt.get<DataCollector>(),
      );

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _cubit.initData();
      _cubit.startMobileDataCollecting();
    });
    super.initState();
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<SplashScreenCubit>(
        create: (_) => _cubit,
        child: BlocConsumer<SplashScreenCubit, SplashScreenState>(
            listener: (_, SplashScreenState state) => _handleStateChange(state),
            buildWhen: (_, SplashScreenState state) => state is SplashScreenCompletedState,
            builder: (_, SplashScreenState state) {
              return Stack(
                children: <Widget>[
                  Positioned.fill(
                    child: imageProvider.asset(
                      EvoImages.imgSplashBackgroundImage,
                      fit: BoxFit.fill,
                    ),
                  ),
                  Center(
                    child: imageProvider.asset(
                      EvoImages.imgBrandName,
                      color: evoColors.primaryBase,
                      width: 163.w,
                      height: 64.w,
                      fit: BoxFit.contain,
                    ),
                  ),
                  _loadingWidget(state),
                ],
              );
            }));
  }

  Widget _loadingWidget(SplashScreenState state) {
    return _isInsecureDeviceStatusApp(state)
        ? const SizedBox.shrink()
        : Center(
            child: SizedBox(
              height: 256.w,
              child: Align(
                alignment: Alignment.bottomCenter,
                child: const AppLoadingIndicator(),
              ),
            ),
          );
  }

  bool _isInsecureDeviceStatusApp(SplashScreenState state) =>
      state is SplashScreenCompletedState && state.statusApp == StatusApp.insecureDevice;

  void _handleStateChange(SplashScreenState state) {
    if (state is SplashScreenCompletedState) {
      FPAppStartMetricRecorder().recordMetric(AppStartPhase.splashScreenInit.toFPMetricName());
      FPAppStartMetricRecorder().stopAppStartUpRecord();

      switch (state.statusApp) {
        case StatusApp.tutorial:
          IntroductionScreen.goNamed();
        case StatusApp.hadLoggedIn:
          LoginOnOldDeviceScreen.goNamed();
        case StatusApp.nonUser:
          WelcomeScreen.goNamed();
        case StatusApp.insecureDevice:
          _showDialogBlockInsecureDevice();
      }
    }
  }

  void _showDialogBlockInsecureDevice() {
    evoDialogFunction.showDialogConfirm(
      dialogId: EvoDialogId.blockInsecureDeviceDialog,
      title: EvoStrings.titleBlockInsecureDeviceDialog,
      content: EvoStrings.descriptionBlockInsecureDeviceDialog,
      isDismissible: false,
      textPositive: EvoStrings.close,
      onClickPositive: () {
        getIt.get<ExitAppFeature>().closeApp();
      },
    );
  }
}
