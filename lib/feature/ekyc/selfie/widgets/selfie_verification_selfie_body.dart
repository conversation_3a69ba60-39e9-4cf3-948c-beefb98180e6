import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_liveness_mode.dart';
import 'package:flutter_common_package/feature/ekyc/facial_verification/facial_verification_handler.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../../data/repository/authentication_repo.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/global.dart';
import '../cubit/mock_selfie_verification_cubit.dart';
import '../cubit/selfie_error_factory.dart';
import '../cubit/selfie_verification_cubit.dart';
import '../model/selfie_error_ui_model.dart';
import '../selfie_locked_screen.dart';
import '../selfie_retry_screen.dart';
import '../selfie_success_screen.dart';
import '../selfie_verification_screen.dart';
import 'selfie_verification_state_widget.dart';

class SelfieVerificationSelfieBody extends StatefulWidget {
  final String? sessionToken;
  final void Function(BaseEntity entity) onPopSuccess;
  final SelfieVerificationFlowType flowType;

  const SelfieVerificationSelfieBody({
    required this.onPopSuccess,
    required this.flowType,
    super.key,
    this.sessionToken,
  });

  @override
  State<SelfieVerificationSelfieBody> createState() => _SelfieVerificationSelfieBodyState();
}

class _SelfieVerificationSelfieBodyState extends State<SelfieVerificationSelfieBody> {
  final EkycBridgeLivenessMode liveMode = EkycBridgeLivenessMode.getFlashLivenessModeByPlatform();

  @visibleForTesting
  late SelfieVerificationCubit selfieCubit;

  @override
  void initState() {
    super.initState();

    /// TODO: hoang-nguyen2 replace MockSelfieVerificationCubit
    selfieCubit = context.read<SelfieVerificationCubit?>() ??
        MockSelfieVerificationCubit(
          selfieHandler: FacialVerificationHandler(),
          errorFactory: SelfieVerificationErrorFactory(),
          authRepo: getIt.get<AuthenticationRepo>(),
        );
    WidgetsBinding.instance.addPostFrameCallback((_) {
      selfieCubit.initialize(widget.sessionToken);
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<SelfieVerificationCubit>(
      create: (_) => selfieCubit,
      child: BlocListener<SelfieVerificationCubit, SelfieVerificationState>(
        listener: (_, SelfieVerificationState state) => _onSelfieVerificationStateChanged(state),
        child: _buildContent(),
      ),
    );
  }

  Widget _buildContent() {
    return BlocBuilder<SelfieVerificationCubit, SelfieVerificationState>(
        buildWhen: (_, SelfieVerificationState state) => state is! SelfieVerificationSuccess,
        builder: (_, SelfieVerificationState selfieState) {
          final (VerificationStatus status, bool canPop) = _getStatusAndCanPop(selfieState);
          return PopScope(
            canPop: canPop,
            child: SelfieVerificationStateWidget(
              status: status,
            ),
          );
        });
  }

  (VerificationStatus, bool) _getStatusAndCanPop(SelfieVerificationState state) {
    VerificationStatus status = VerificationStatus.none;
    bool canPop = true;
    switch (state) {
      case SelfieVerificationInitial() || SelfieVerificationLoading():
        status = VerificationStatus.initializing;
        canPop = false;
      case InitializeBridgeSuccess() || SelfieVerificationProcessing():
        status = VerificationStatus.none;
        canPop = true;
      case SelfieCapturingSuccess():
        status = VerificationStatus.processing;
        canPop = false;
      case SelfieVerificationSuccess() || SelfieVerificationFailure():
        status = VerificationStatus.none;
        canPop = true;
    }
    return (status, canPop);
  }

  void _onSelfieVerificationStateChanged(SelfieVerificationState state) {
    switch (state) {
      case InitializeBridgeSuccess():
        selfieCubit.captureSelfie(
          liveMode: liveMode,
        );
        return;
      case final SelfieVerificationSuccess state:
        SelfieSuccessScreen.pushReplacementNamed(onProceed: () {
          widget.onPopSuccess(state.entity);
        });
        return;
      case SelfieCapturingSuccess(
          imageIds: final List<String>? imageIds,
          videoIds: final List<String>? videoIds
        ):
        selfieCubit.verifySelfie(
          flowType: widget.flowType,
          liveMode: liveMode,
          sessionToken: widget.sessionToken,
          imageIds: imageIds,
          videoIds: videoIds,
        );
        return;

      case SelfieVerificationFailure(error: final SelfieErrorUiModel error):
        _handleSelfieError(error);
        return;

      default:
        return;
    }
  }

  void _handleSelfieError(SelfieErrorUiModel error) {
    switch (error.actionType) {
      case ErrorActionType.retry:
        SelfieRetryScreen.pushNamed(
            onRetry: () {
              selfieCubit.initialize(widget.sessionToken);
            });
        return;
      case ErrorActionType.blocked:
        SelfieLockedScreen.pushReplacementNamed(
          title: error.title,
          subtitle: error.description,
        );
        return;
      case ErrorActionType.ignore:
        navigatorContext?.pop();
        return;
    }
  }
}
