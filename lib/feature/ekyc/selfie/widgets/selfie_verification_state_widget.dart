import 'package:flutter/material.dart';

import '../../../../resources/resources.dart';
import '../../../../util/screen_util.dart';
import '../../../../widget/app_loading_indicator.dart';
import '../../widget/safe_info_widget.dart';

enum VerificationStatus {
  processing,
  initializing,
  none,
}

class SelfieVerificationStateWidget extends StatelessWidget {
  final VerificationStatus status;

  const SelfieVerificationStateWidget({required this.status, super.key});

  @override
  Widget build(BuildContext context) {
    return _buildContent();
  }

  Widget _buildContent() {
    final List<Widget> children = <Widget>[];

    if (status != VerificationStatus.none) {
      final String desc = switch (status) {
        VerificationStatus.processing => EvoStrings.selfieProcessingDesc,
        VerificationStatus.initializing => EvoStrings.selfieInitializingDesc,
        VerificationStatus.none => '',
      };

      children.addAll(<Widget>[
        const Spacer(),
        const Center(child: AppLoadingIndicator()),
        EvoDimension.space32,
        _buildLoadingDesc(desc),
      ]);
    }

    children.addAll(<Widget>[
      const Spacer(),
      _buildSafeDesc(),
    ]);

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: EvoDimension.screenHorizontalPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: children,
      ),
    );
  }

  Widget _buildLoadingDesc(String desc) {
    return Text(
      desc,
      textAlign: TextAlign.center,
      style: evoTextStyles.regular(TextSize.base, color: evoColors.grayText),
    );
  }

  Widget _buildSafeDesc() {
    return Padding(
      padding: EdgeInsets.only(bottom: EvoDimension.screenBottomPadding),
      child: SafeInfoWidget(),
    );
  }
}
