part of 'selfie_verification_cubit.dart';

sealed class SelfieVerificationState implements BlocState {}

class SelfieVerificationInitial extends SelfieVerificationState {}

class InitializeBridgeSuccess extends SelfieVerificationState {}

class SelfieVerificationLoading extends SelfieVerificationState {}

class SelfieVerificationProcessing extends SelfieVerificationState {}

class SelfieVerificationSuccess extends SelfieVerificationState {
  final BaseEntity entity;

  SelfieVerificationSuccess(this.entity);
}

class SelfieVerificationFailure extends SelfieVerificationState {
  final SelfieErrorUiModel error;

  SelfieVerificationFailure({required this.error});
}

class SelfieCapturingSuccess extends SelfieVerificationState {
  final List<String>? imageIds;
  final List<String>? videoIds;

  SelfieCapturingSuccess({
    this.imageIds,
    this.videoIds,
  });
}
