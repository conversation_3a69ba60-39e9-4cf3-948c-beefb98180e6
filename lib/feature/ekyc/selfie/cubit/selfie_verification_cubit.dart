import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_error_reason.dart';
import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_liveness_mode.dart';
import 'package:flutter_common_package/feature/ekyc/facial_verification/facial_verification_handler.dart';
import 'package:flutter_common_package/feature/ekyc/facial_verification/models/facial_verification_initialize_args.dart';
import 'package:flutter_common_package/feature/ekyc/facial_verification/models/facial_verification_initialize_result.dart';
import 'package:flutter_common_package/feature/ekyc/facial_verification/models/facial_verification_start_capturing_args.dart';
import 'package:flutter_common_package/feature/ekyc/facial_verification/models/facial_verification_start_capturing_result.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../../data/repository/authentication_repo.dart';
import '../../../../data/request/activate_account_request.dart';
import '../../../../data/request/reset_pin_request.dart';
import '../../../../data/response/account_activation_entity.dart';
import '../../../../data/response/reset_pin_entity.dart';
import '../../../account_activation/mock/mock_account_activation_use_case.dart';
import '../../../pin/mock/mock_pin_use_case.dart';
import '../model/selfie_error_ui_model.dart';
import '../selfie_verification_screen.dart';
import 'selfie_error_factory.dart';

part 'selfie_verification_state.dart';

class SelfieVerificationCubit extends CommonCubit<SelfieVerificationState> {
  final FacialVerificationHandler selfieHandler;
  final SelfieVerificationErrorFactory errorFactory;
  final AuthenticationRepo authRepo;

  SelfieVerificationCubit({
    required this.selfieHandler,
    required this.errorFactory,
    required this.authRepo,
  }) : super(SelfieVerificationInitial());

  Future<void> initialize(String? sessionToken) async {
    if (sessionToken == null) {
      commonLog(
        'session_token should not be null',
        methodName: 'initialize',
      );
      emit(SelfieVerificationFailure(
          error: SelfieErrorUiModel(
        actionType: ErrorActionType.blocked,
      )));
      return;
    }

    emit(SelfieVerificationLoading());
    final FacialVerificationInitializeResult initResult = await selfieHandler.initialize(
        args: FacialVerificationInitializeArgs(
      sessionToken: sessionToken,
      languageCode: 'en',
    ));

    if (initResult is FacialVerificationInitializeErrorResult) {
      _handleInitializedError(
        bridgeError: initResult.bridgeErrorReason,
        errorEntity: initResult.apiErrorResponse,
      );
      return;
    }

    emit(InitializeBridgeSuccess());
    return;
  }

  Future<void> captureSelfie({
    required EkycBridgeLivenessMode liveMode,
  }) async {
    emit(SelfieVerificationProcessing());
    final FacialVerificationStartCapturingResult result = await selfieHandler.startCapturing(
        args: FacialVerificationStartCapturingArgs(
      livenessMode: liveMode,
    ));

    if (result is! FacialVerificationStartCapturingSuccessResult) {
      _handleCaptureError(result);
      return;
    }

    emit(SelfieCapturingSuccess(
      imageIds: result.imageIds,
      videoIds: result.videoIds,
    ));
  }

  void _handleInitializedError({
    EkycBridgeErrorReason? bridgeError,
    BaseEntity? errorEntity,
  }) {
    final SelfieErrorUiModel errorUiModel = errorFactory.create(
      entity: errorEntity,
      bridgeError: bridgeError,
    );
    emit(SelfieVerificationFailure(error: errorUiModel));
  }

  void _handleCaptureError(FacialVerificationStartCapturingResult result) {
    late SelfieErrorUiModel errorUiModel;

    if (result is FacialVerificationStartCapturingErrorResult) {
      errorUiModel = errorFactory.create(
        entity: result.apiErrorResponse,
        bridgeError: result.bridgeErrorReason,
      );
    } else {
      errorUiModel = errorFactory.create();
    }

    emit(SelfieVerificationFailure(error: errorUiModel));
  }

  Future<void> verifySelfie({
    required SelfieVerificationFlowType flowType,
    required EkycBridgeLivenessMode liveMode,
    required String? sessionToken,
    List<String>? imageIds,
    List<String>? videoIds,
  }) async {
    final BaseEntity entity = switch (flowType) {
      SelfieVerificationFlowType.resetPin => await resetPinVerifySelfie(
          selfieType: liveMode.value,
          imageIds: imageIds,
          videoIds: videoIds,
          sessionToken: sessionToken,
        ),
      SelfieVerificationFlowType.signIn => await signInVerifySelfie(),
      SelfieVerificationFlowType.activeAccount => await activeAccountVerifySelfie(),
    };

    if (entity.statusCode != CommonHttpClient.SUCCESS) {
      final SelfieErrorUiModel error = errorFactory.create(entity: entity);
      emit(SelfieVerificationFailure(error: error));
      return;
    }

    emit(SelfieVerificationSuccess(entity));
  }

  @visibleForTesting
  Future<BaseEntity> resetPinVerifySelfie({
    required String selfieType,
    required String? sessionToken,
    List<String>? imageIds,
    List<String>? videoIds,
  }) async {
    final ResetPinEntity resetPinEntity = await authRepo.resetPin(
        request: ResetPinFaceAuthRequest(
          imageIds: imageIds,
          videoIds: videoIds,
          selfieType: selfieType,
          sessionToken: sessionToken,
        ),
        mockConfig: MockConfig(
          enable: true,
          fileName: getMockPinFileNameByCase(
            MockPinUseCase.getResetPinFaceAuthSuccess,
          ),
        ));

    return resetPinEntity;
  }

  Future<BaseEntity> signInVerifySelfie() {
    /// TODO: hoang-nguyen2 implement sign-in verify selfie
    throw UnimplementedError();
  }

  Future<BaseEntity> activeAccountVerifySelfie() async {
    final AccountActivationEntity entity = await authRepo.activateAccount(
      request: ActivateAccountVerifySelfieRequest(),
      mockConfig: MockConfig(
        enable: true,
        fileName: getMockAccountActivationFileNameByCase(
          MockAccountActivationUseCase.getCreateUsernameChallengeType,
        ),
      ),
    );

    return entity;
  }
}
