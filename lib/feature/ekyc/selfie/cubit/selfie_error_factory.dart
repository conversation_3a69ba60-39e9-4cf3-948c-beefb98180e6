import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_error_reason.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../resources/ui_strings.dart';
import '../model/selfie_error_ui_model.dart';

class SelfieVerificationErrorFactory {
  SelfieErrorUiModel create({
    BaseEntity? entity,
    EkycBridgeErrorReason? bridgeError,
  }) {
    if (bridgeError != null) {
      return createBridgeError(bridgeError);
    }
    if (entity != null) {
      return createApiError(entity);
    }
    return createUnknownError();
  }

  @visibleForTesting
  SelfieErrorUiModel createApiError(BaseEntity entity) {
    final bool isLockedFlow = entity.statusCode == CommonHttpClient.LOCKED_RESOURCE ||
        entity.statusCode == CommonHttpClient.LIMIT_EXCEEDED ||
        entity.statusCode == CommonHttpClient.INVALID_TOKEN;

    return SelfieErrorUiModel.fromModel(
      errorUIModel: ErrorUIModel.fromEntity(entity),
      actionType: isLockedFlow ? ErrorActionType.blocked : ErrorActionType.retry,
    );
  }

  @visibleForTesting
  SelfieErrorUiModel createUnknownError() {
    return SelfieErrorUiModel(
      actionType: ErrorActionType.retry,
    );
  }

  @visibleForTesting
  SelfieErrorUiModel createBridgeError(EkycBridgeErrorReason error) {
    switch (error) {
      case EkycBridgeErrorReason.userCancelled:
        return SelfieErrorUiModel(
          actionType: ErrorActionType.ignore,
        );
      case EkycBridgeErrorReason.exceedLimit:
        return SelfieErrorUiModel(
            title: EvoStrings.maxTriesReached,
            description: EvoStrings.tryAgainLater,
            actionType: ErrorActionType.blocked);
      case EkycBridgeErrorReason.sessionExpired:
      case EkycBridgeErrorReason.initWithInvalidSession:
        return SelfieErrorUiModel(
          title: EvoStrings.titleSessionTokenExpired,
          description: EvoStrings.contentSessionTokenExpiredSignIn,
          actionType: ErrorActionType.blocked,
        );
      default:
        return createUnknownError();
    }
  }
}
