// coverage:ignore-file
import 'package:evoapp/feature/ekyc/selfie/cubit/selfie_verification_cubit.dart';
import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_liveness_mode.dart';
import 'package:flutter_common_package/feature/ekyc/facial_verification/models/facial_verification_start_capturing_args.dart';

class MockSelfieVerificationCubit extends SelfieVerificationCubit {
  MockSelfieVerificationCubit({
    required super.selfieHandler,
    required super.errorFactory,
    required super.authRepo,
  });

  @override
  Future<void> captureSelfie({
    required EkycBridgeLivenessMode liveMode,
  }) async {
    emit(SelfieVerificationProcessing());

    await selfieHandler.startCapturing(
        args: FacialVerificationStartCapturingArgs(
      livenessMode: liveMode,
    ));

    emit(SelfieCapturingSuccess(imageIds: [], videoIds: []));
  }
}
