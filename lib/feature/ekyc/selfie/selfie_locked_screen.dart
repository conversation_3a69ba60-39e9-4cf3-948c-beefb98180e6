import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../resources/resources.dart';
import '../../../util/screen_util.dart';
import '../../../widget/buttons.dart';
import '../../../widget/no_app_bar_wrapper.dart';
import '../../welcome/welcome_screen.dart';

class SelfieLockedScreenArg extends PageBaseArg {
  final String title;

  final String subtitle;

  SelfieLockedScreenArg({required this.title, required this.subtitle});
}

class SelfieLockedScreen extends PageBase {
  final String title;

  final String subtitle;

  const SelfieLockedScreen({required this.title, required this.subtitle, super.key});

  static void pushReplacementNamed({required String title, required String subtitle}) {
    navigatorContext?.pushReplacementNamed(
      Screen.selfieLockedScreen.name,
      extra: SelfieLockedScreenArg(title: title, subtitle: subtitle),
    );
  }

  @override
  State<SelfieLockedScreen> createState() => _SelfieLockedScreenState<SelfieLockedScreen>();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.selfieLockedScreen.routeName);
}

class _SelfieLockedScreenState<T extends PageBase> extends EvoPageStateBase<SelfieLockedScreen> {
  @override
  Widget getContentWidget(BuildContext context) {
    return NoAppBarWrapper(
      child: PopScope(
        canPop: false,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: EvoDimension.screenHorizontalPadding),
          child: Column(
            children: <Widget>[
              const Spacer(),
              evoImageProvider.asset(EvoImages.icAlertError, width: 136.w, height: 136.w),
              EvoDimension.space8,
              Text(
                widget.title,
                textAlign: TextAlign.center,
                style: evoTextStyles.bold(TextSize.h3, color: evoColors.grayText),
              ),
              EvoDimension.space8,
              Text(
                widget.subtitle,
                textAlign: TextAlign.center,
                style: evoTextStyles.regular(TextSize.base, color: evoColors.grayBase),
              ),
              const Spacer(),
              PrimaryButton(
                text: EvoStrings.backToHomePage,
                onTap: WelcomeScreen.goNamed,
              ),
              SizedBox(height: EvoDimension.screenBottomPadding),
            ],
          ),
        ),
      ),
    );
  }
}
