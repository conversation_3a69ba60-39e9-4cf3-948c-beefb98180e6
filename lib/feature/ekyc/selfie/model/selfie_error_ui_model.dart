import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../resources/ui_strings.dart';

enum ErrorActionType { retry, ignore, blocked }

class SelfieErrorUiModel {
  final String title;
  final String description;
  ErrorActionType actionType;

  SelfieErrorUiModel.fromModel({
    required ErrorActionType actionType,
    ErrorUIModel? errorUIModel,
  }) : this(
          title: errorUIModel?.userMessageTitle,
          description: errorUIModel?.userMessage,
          actionType: actionType,
        );

  SelfieErrorUiModel({
    required this.actionType,
    String? title,
    String? description,
  })  : title = title ?? EvoStrings.unknownError,
        description = description ?? EvoStrings.tryAgainLater;
}
