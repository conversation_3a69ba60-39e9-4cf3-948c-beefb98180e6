// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../resources/resources.dart';
import '../../../util/screen_util.dart';
import '../../../widget/buttons.dart';
import '../../../widget/no_app_bar_wrapper.dart';

class SelfieSuccessScreenArg extends PageBaseArg {
  final VoidCallback onProceed;

  SelfieSuccessScreenArg({required this.onProceed});
}

class SelfieSuccessScreen extends PageBase {
  final VoidCallback onProceed;

  const SelfieSuccessScreen({required this.onProceed, super.key});

  static void pushReplacementNamed({required VoidCallback onProceed}) {
    navigatorContext?.pushReplacementNamed(
      Screen.selfieSuccessScreen.name,
      extra: SelfieSuccessScreenArg(onProceed: onProceed),
    );
  }

  @override
  State<SelfieSuccessScreen> createState() => _SelfieSuccessScreenState<SelfieSuccessScreen>();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.selfieSuccessScreen.routeName);
}

class _SelfieSuccessScreenState<T extends PageBase> extends EvoPageStateBase<SelfieSuccessScreen> {
  @override
  Widget getContentWidget(BuildContext context) {
    return NoAppBarWrapper(
      child: PopScope(
        canPop: false,
        child: Padding(
          padding: EdgeInsets.only(
            left: EvoDimension.screenHorizontalPadding,
            right: EvoDimension.screenHorizontalPadding,
            bottom: EvoDimension.screenBottomPadding,
          ),
          child: Column(
            children: <Widget>[
              const Spacer(),
              evoImageProvider.asset(
                EvoImages.imgFaceCaptureCheck,
                fit: BoxFit.contain,
                height: 328.w,
              ),
              EvoDimension.space4,
              Text(
                EvoStrings.selfieSuccessTitle,
                textAlign: TextAlign.center,
                style: evoTextStyles.semibold(TextSize.h1, color: evoColors.grayText),
              ),
              const Spacer(),
              PrimaryButton(
                text: EvoStrings.ctaProceed,
                onTap: widget.onProceed,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
