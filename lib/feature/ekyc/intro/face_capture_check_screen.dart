import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../resources/resources.dart';
import '../../../util/screen_util.dart';
import '../../../widget/appbar/evo_appbar.dart';
import '../../../widget/buttons.dart';
import '../selfie/selfie_verification_screen.dart';

class FaceCaptureCheckScreenArgs extends PageBaseArg {
  final String? sessionToken;
  final void Function(BaseEntity entity) onPopSuccess;

  FaceCaptureCheckScreenArgs({
    required this.onPopSuccess,
    this.sessionToken,
  });
}

class FaceCaptureCheckScreen extends PageBase {
  final String? sessionToken;
  final void Function(BaseEntity entity) onPopSuccess;

  const FaceCaptureCheckScreen({
    required this.sessionToken,
    required this.onPopSuccess,
    super.key,
  });

  @override
  State<FaceCaptureCheckScreen> createState() => _FaceCaptureCheckScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.faceCaptureCheckScreen.routeName);

  static void pushNamed({
    required void Function(BaseEntity entity) onPopSuccess,
    required String? sessionToken,
  }) {
    navigatorContext?.pushNamed(
      Screen.faceCaptureCheckScreen.name,
      extra: FaceCaptureCheckScreenArgs(
        sessionToken: sessionToken,
        onPopSuccess: onPopSuccess,
      ),
    );
  }
}

class _FaceCaptureCheckScreenState extends PageStateBase<FaceCaptureCheckScreen> {
  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      appBar: EvoAppBar(),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: EvoDimension.screenHorizontalPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Text(
                // TODO: Replace "<first_name>" with first name
                EvoStrings.faceCaptureCheckTitle.replaceVariableByValue(['<first_name>']),
                style: evoTextStyles.semibold(TextSize.h3, color: evoColors.grayText),
              ),
              EvoDimension.space24,
              Text(
                EvoStrings.faceCaptureCheckSubtitle,
                style: evoTextStyles.semibold(TextSize.h5, color: evoColors.grayText),
              ),
              EvoDimension.space4,
              Text(
                EvoStrings.faceCaptureCheckDesc,
                style: evoTextStyles.regular(TextSize.base, color: evoColors.grayBase),
              ),
              Expanded(
                child: Center(
                  child: evoImageProvider.asset(
                    EvoImages.imgFaceCaptureCheck,
                    fit: BoxFit.contain,
                    height: 328.w,
                  ),
                ),
              ),
              _buildProceedButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProceedButton() {
    return Padding(
      padding: EdgeInsets.only(bottom: EvoDimension.screenBottomPadding),
      child: PrimaryButton(
        onTap: () {
          SelfieVerificationScreen.pushReplacementNamed(
            sessionToken: widget.sessionToken,
            flowType: SelfieVerificationFlowType.activeAccount,
            onPopSuccess: widget.onPopSuccess,
          );
        },
        text: EvoStrings.ctaProceed,
      ),
    );
  }
}
