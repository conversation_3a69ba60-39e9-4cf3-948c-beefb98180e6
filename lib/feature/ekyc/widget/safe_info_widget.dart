import 'package:flutter/material.dart';

import '../../../resources/resources.dart';
import '../../../util/screen_util.dart';

class SafeInfoWidget extends StatelessWidget {
  const SafeInfoWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return RichText(
      textAlign: TextAlign.center,
      text: TextSpan(
        style: evoTextStyles.regular(TextSize.xs, color: evoColors.grayBase),
        children: <InlineSpan>[
          WidgetSpan(
            alignment: PlaceholderAlignment.middle,
            child: Padding(
              padding: EdgeInsets.only(right: 4.w),
              child: evoImageProvider.asset(
                EvoImages.icPolicy,
                color: evoColors.informationBase,
                height: 24.w,
                width: 24.w,
              ),
            ),
          ),
          const TextSpan(text: EvoStrings.selfieSafeInfoDesc),
        ],
      ),
    );
  }
}
