import 'package:flutter_common_package/ui_model/error_ui_model.dart';

abstract class PrivilegeActionRequest {}

class PrivilegeActionResponse<T> {
  final T? data;
  final ErrorUIModel? error;

  bool get isSuccess => data != null;

  PrivilegeActionResponse._({this.data, this.error});

  factory PrivilegeActionResponse.success(T data) {
    return PrivilegeActionResponse<T>._(data: data);
  }

  factory PrivilegeActionResponse.error(ErrorUIModel error) {
    return PrivilegeActionResponse<T>._(error: error);
  }
}

abstract class PrivilegeActionHandler<T> {
  /// **biometricToken** is token for biometric authentication
  /// **pin** is pin for pin authentication
  /// **request** is parameters for privilege action
  Future<PrivilegeActionResponse<T>> execute({String? biometricToken, String? pin, PrivilegeActionRequest? request});
}
