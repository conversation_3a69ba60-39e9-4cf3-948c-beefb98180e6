import 'core/privilege_action_handler.dart';

class UnfreezeCardActionData {}

class UnfreezeCardHandler implements PrivilegeActionHandler<UnfreezeCardActionData> {
  @override
  Future<PrivilegeActionResponse<UnfreezeCardActionData>> execute({
    String? biometricToken,
    String? pin,
    PrivilegeActionRequest? request,
  }) async {
    // TODO: update with API integration
    await Future<void>.delayed(const Duration(seconds: 1));
    return PrivilegeActionResponse<UnfreezeCardActionData>.success(UnfreezeCardActionData());
  }
}
