import 'core/privilege_action_handler.dart';
import 'get_card_information_handler.dart';
import 'unfreeze_card_handler.dart';

enum PrivilegeActionType {
  showCardInformation,
  unfreezeCard,
}

class PrivilegeActionHandlerFactory {
  PrivilegeActionHandler<dynamic> createHandler(PrivilegeActionType type) {
    switch (type) {
      case PrivilegeActionType.showCardInformation:
        return GetCardInformationHandler();
      case PrivilegeActionType.unfreezeCard:
        return UnfreezeCardHandler();
    }
  }
}
