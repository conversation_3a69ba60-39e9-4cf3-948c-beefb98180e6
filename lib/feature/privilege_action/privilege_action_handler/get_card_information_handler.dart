import 'core/privilege_action_handler.dart';

/// TODO this response is replaced when API is ready
class GetCardInformationResponse {
  final String cardNumber;
  final String cardHolderName;
  final String cardExpiryDate;
  final String cvv;

  GetCardInformationResponse({
    required this.cardNumber,
    required this.cardHolderName,
    required this.cardExpiryDate,
    required this.cvv,
  });
}

class GetCardInformationHandler extends PrivilegeActionHandler<GetCardInformationResponse> {
  @override
  Future<PrivilegeActionResponse<GetCardInformationResponse>> execute(
      {String? biometricToken, String? pin, PrivilegeActionRequest? request}) async {
    
    /// TODO this implementation is replaced when API is ready
    await Future<void>.delayed(const Duration(seconds: 1));
    return PrivilegeActionResponse<GetCardInformationResponse>.success(GetCardInformationResponse(
      cardNumber: '111111111',
      cardHolderName: 'Username',
      cardExpiryDate: '12/12',
      cvv: '123',
    ));
  }
}
