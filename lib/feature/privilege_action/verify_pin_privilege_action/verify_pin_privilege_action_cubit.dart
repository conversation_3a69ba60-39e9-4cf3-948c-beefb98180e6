import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../privilege_action_handler/core/privilege_action_handler.dart';

part 'verify_pin_privilege_action_state.dart';

class VerifyPinPrivilegeActionCubit extends CommonCubit<VerifyPinPrivilegeActionState> {
  final PrivilegeActionHandler<dynamic> privilegeActionHandler;

  VerifyPinPrivilegeActionCubit({
    required this.privilegeActionHandler,
  }) : super(VerifyPinPrivilegeActionInitState());

  Future<void> performs({String? pin, PrivilegeActionRequest? request}) async {
    emit(PrivilegeActionLoadingState());

    final PrivilegeActionResponse<dynamic> response = await privilegeActionHandler.execute(
      pin: pin,
      request: request,
    );

    if (!response.isSuccess && response.error?.statusCode == CommonHttpClient.BAD_REQUEST) {
      /// at this condition, response.error is always not null. So it's safe to use `!`
      final ErrorUIModel errorUIModel = response.error!;
      emit(PrivilegeActionBadRequestState(userMessage: errorUIModel.userMessage));
      return;
    }

    emit(PrivilegeActionCompleteState(result: response));
  }
}
