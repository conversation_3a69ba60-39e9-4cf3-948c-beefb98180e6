part of 'verify_pin_privilege_action_cubit.dart';

abstract class VerifyPinPrivilegeActionState implements BlocState {}

class VerifyPinPrivilegeActionInitState extends VerifyPinPrivilegeActionState {}

class PrivilegeActionLoadingState extends VerifyPinPrivilegeActionState {}

class PrivilegeActionCompleteState extends VerifyPinPrivilegeActionState {
  final PrivilegeActionResponse<dynamic> result;

  PrivilegeActionCompleteState({required this.result});
}

class PrivilegeActionBadRequestState extends VerifyPinPrivilegeActionState {
  final String? userMessage;

  PrivilegeActionBadRequestState({required this.userMessage});
}
