import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../base/evo_page_state_base.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/resources.dart';
import '../../../../util/functions.dart';
import '../../../../widget/appbar/evo_support_appbar.dart';
import '../../../../widget/evo_mpin_code/evo_mpin_code_widget.dart';
import '../../pin/reset_pin/reset_pin_handler.dart';
import '../privilege_action_handler/core/privilege_action_handler.dart';
import 'verify_pin_privilege_action_cubit.dart';


typedef OnCompleteVerifyPinPrivilegeAction = void Function(PrivilegeActionResponse<dynamic> result);

class VerifyPinPrivilegeActionScreenArgs extends PageBaseArg {
  final PrivilegeActionHandler<dynamic> handler;
  final OnCompleteVerifyPinPrivilegeAction onComplete;
  final PrivilegeActionRequest? request;

  VerifyPinPrivilegeActionScreenArgs({required this.handler, required this.onComplete, this.request});
}

class VerifyPinPrivilegeActionScreen extends PageBase {
  final VerifyPinPrivilegeActionScreenArgs args;

  const VerifyPinPrivilegeActionScreen({required this.args, super.key});

  @override
  EvoPageStateBase<VerifyPinPrivilegeActionScreen> createState() =>
      _VerifyPinPrivilegeActionScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.verifyPinPrivilegeActionScreen.name);

  static Future<void> pushNamed(
      {required PrivilegeActionHandler<dynamic> handler,
      required OnCompleteVerifyPinPrivilegeAction onComplete}) async {
    return navigatorContext?.pushNamed(
      Screen.verifyPinPrivilegeActionScreen.name,
      extra: VerifyPinPrivilegeActionScreenArgs(
        handler: handler,
        onComplete: onComplete,
      ),
    );
  }
}

class _VerifyPinPrivilegeActionScreenState
    extends EvoPageStateBase<VerifyPinPrivilegeActionScreen> {
  late VerifyPinPrivilegeActionCubit _cubit;

  TextEditingController pinTextController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _cubit = VerifyPinPrivilegeActionCubit(privilegeActionHandler: widget.args.handler);
  }


  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      appBar: EvoSupportAppbar(),
      body: BlocProvider<VerifyPinPrivilegeActionCubit>(
        create: (_) => _cubit,
        child: BlocListener<VerifyPinPrivilegeActionCubit, VerifyPinPrivilegeActionState>(
          listener: (BuildContext context, VerifyPinPrivilegeActionState currState) {
            _onStateChanged(currState);
          },
          child: _getContent(),
        ),
      ),
    );
  }

  Widget _getContent() {
    return BlocBuilder<VerifyPinPrivilegeActionCubit, VerifyPinPrivilegeActionState>(
        buildWhen:
            (VerifyPinPrivilegeActionState prevState, VerifyPinPrivilegeActionState currState) =>
                currState is! PrivilegeActionLoadingState,
        builder: (BuildContext context, VerifyPinPrivilegeActionState state) {
          String? errMessage;

          if (state is PrivilegeActionBadRequestState) {
            errMessage = state.userMessage ?? CommonStrings.otherGenericErrorMessage;
          }

          return Container(
            padding: const EdgeInsets.all(20),
            child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Text(
                    EvoStrings.inputPinTitle,
                    style: evoTextStyles.bold(TextSize.xl2),
                  ),
                  EvoMPINCodeWidget(
                    textEditingController: pinTextController,
                    title: EvoStrings.inputPinDesc,
                    onSubmit: _verifyPin,
                    errorMessage: errMessage,
                    onResetPin: _onRequestResetPin,
                  )
                ]),
          );
        });
  }

  void _verifyPin(String pin) {
    _cubit.performs(pin: pin, request: widget.args.request);
  }

  void _onStateChanged(VerifyPinPrivilegeActionState currState) {
    if (currState is PrivilegeActionLoadingState) {
      evoUtilFunction.showHudLoading();
      return;
    }

    evoUtilFunction.hideHudLoading();

    if (currState is PrivilegeActionCompleteState) {
      navigatorContext?.pop();
      widget.args.onComplete(currState.result);
      return;
    }
  }

  void _onRequestResetPin() {
    final String? phoneNumber = appState.userInfo.value?.phoneNumber;

    getIt.get<ResetPinHandler>().requestResetPin(
          phoneNumber: phoneNumber,
          onError: handleEvoApiError,
          entryScreenName: widget.routeSettings.name,
        );
  }
}
