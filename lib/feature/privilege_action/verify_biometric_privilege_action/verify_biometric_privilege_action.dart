import '../../../resources/ui_strings.dart';
import '../../../util/functions.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../util/token_utils/jwt_helper.dart';
import '../../biometric/utils/bio_auth_result.dart';
import '../../biometric/utils/biometrics_authenticate.dart';
import '../privilege_action_handler/core/privilege_action_handler.dart';

class VerifyBiometricForPrivilegeAction {
  final BiometricsAuthenticate biometricsAuthenticate;
  final EvoLocalStorageHelper secureStorageHelper;
  final EvoUtilFunction evoUtilFunction;
  final JwtHelper jwtHelper;

  VerifyBiometricForPrivilegeAction({
    required this.biometricsAuthenticate,
    required this.secureStorageHelper,
    required this.jwtHelper,
    required this.evoUtilFunction,
  });

  Future<bool> isAuthenticateBiometricsEnabled() async {
    final bool isEnableBioAuth = await _isEnableBiometricAuthenticator();
    final bool isTokenUsable = await _isBiometricTokenUsable();
    return isEnableBioAuth && isTokenUsable;
  }

  /// Authenticate with biometric if success, it performs privilege action
  /// Otherwise, if it return NULL, it means user **cancel** biometric authentication, or **biometric is not available**
  Future<PrivilegeActionResponse<dynamic>?> authenticate({
    required PrivilegeActionHandler<dynamic> handler,
    PrivilegeActionRequest? request,
  }) async {
    final BioAuthResult result = await biometricsAuthenticate.authenticate(
        localizedReason: EvoStrings.localizedReasonForUsingBiometrics);

    if (result.isAuthSuccess && await _isBiometricTokenUsable()) {
      evoUtilFunction.showHudLoading();

      /// biometric is success and process privilege action
      final String? biometricToken = await _getBiometricToken();

      final PrivilegeActionResponse<dynamic> response =
          await handler.execute(biometricToken: biometricToken, request: request);

      evoUtilFunction.hideHudLoading();

      return response;
    } else {
      return null;
    }
  }

  Future<String?> _getBiometricToken() {
    return secureStorageHelper.getBiometricToken();
  }

  Future<bool> _isEnableBiometricAuthenticator() {
    return secureStorageHelper.isEnableBiometricAuthenticator();
  }

  Future<bool> _isBiometricTokenUsable() async {
    final String? biometricToken = await secureStorageHelper.getBiometricToken();
    return jwtHelper.isCanUse(biometricToken);
  }
}
