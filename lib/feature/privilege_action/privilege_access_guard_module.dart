import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';

import 'privilege_action_handler/core/privilege_action_handler.dart';
import 'privilege_action_handler/privilege_action_factory.dart';
import 'verify_biometric_privilege_action/verify_biometric_privilege_action.dart';
import 'verify_pin_privilege_action/verify_pin_privilege_action_screen.dart';

abstract class PrivilegeAccessGuardCallback {
  void onPrivilegeActionSuccess(dynamic response);

  void onPrivilegeActionError(ErrorUIModel error);
}

/// Purpose of this module is force user have perform biometric or pin authentication before  access
/// to sensitive data or actions. If user enable biometric authentication, this module will check biometric. Otherwise,
/// user have to enter pin to perform privilege action.
/// This secure flow is applied to sensitive data such a:
/// * Show card information. such as, card number, card holder name, card expiry date, cvv
/// * Unfreezing card when card is frozen state
class PrivilegeAccessGuardModule {
  final VerifyBiometricForPrivilegeAction biometricForPrivilegeAction;
  final PrivilegeActionHandlerFactory privilegeActionFactory;

  late PrivilegeAccessGuardCallback? privilegeAccessGuardCallback;

  PrivilegeAccessGuardModule({
    required this.biometricForPrivilegeAction,
    required this.privilegeActionFactory,
  });

  Future<void> confirm({
    required PrivilegeAccessGuardCallback callback,
    required PrivilegeActionType type,
    PrivilegeActionRequest? request,
    bool isForcePin = false,
  }) async {
    privilegeAccessGuardCallback = callback;
    final PrivilegeActionHandler<dynamic> handler = privilegeActionFactory.createHandler(type);

    if (isForcePin) {
      await _showVerifyPinScreen(handler: handler);
      return;
    }

    final bool isAuthBiometricEnabled =
        await biometricForPrivilegeAction.isAuthenticateBiometricsEnabled();
    if (!isAuthBiometricEnabled) {
      await _showVerifyPinScreen(handler: handler);
      return;
    }

    final PrivilegeActionResponse<dynamic>? result =
        await biometricForPrivilegeAction.authenticate(handler: handler, request: request);

    if (result != null) {
      _handlePrivilegeActionResponse(result);
    } else {
      await _showVerifyPinScreen(handler: handler);
    }
  }

  Future<void> _showVerifyPinScreen({
    required PrivilegeActionHandler<dynamic> handler,
  }) async {
    /// Consider this solution to handle this case.
    /// Should update the `VerifyPinPrivilegeActionScreen` to return `PrivilegeActionResponse<dynamic>` instead of `void`.
    /// For instance:
    /// ***
    /// final PrivilegeActionResponse<dynamic>? result = await VerifyPinPrivilegeActionScreen.pushNamed(handler: handler, onComplete: onCompleteVerifyPinPrivilegeAction);
    /// ***
    /// To do this change, we need to update CommonNavigator in CommonPackage
    /// ***
    /// Future<T?> pushNamed(BuildContext context, String pageName, {PageBaseArg? extra});
    /// ***
    await VerifyPinPrivilegeActionScreen.pushNamed(
        handler: handler,
        onComplete: (PrivilegeActionResponse<dynamic> result) {
          _handlePrivilegeActionResponse(result);
        });
    commonLog('onHidePinPopup');
  }

  void _handlePrivilegeActionResponse(PrivilegeActionResponse<dynamic> result) {
    if (result.isSuccess) {
      privilegeAccessGuardCallback?.onPrivilegeActionSuccess(result.data);
    } else {
      /// at this condition, response.error is always not null. So it's safe to use `!`
      privilegeAccessGuardCallback?.onPrivilegeActionError(result.error!);
    }
  }
}
