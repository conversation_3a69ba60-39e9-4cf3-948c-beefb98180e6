enum MockVerifyOtpUseCase {
  getSignInOtpSuccess('sign_in_otp_success.json'),
  getVerifySignInOtpSuccess('verify_sign_in_otp_success.json'),
  getVerifySignInOtpIncorrectOtp('verify_sign_in_otp_incorrect_otp.json'),
  getVerifySignInResendOtpLimitExceeded('verify_sign_in_resend_otp_limit_exceeded.json'),
  getVerifyActiveAccountSuccess('get_verify_active_account_success.json');

  final String value;

  const MockVerifyOtpUseCase(this.value);
}

String getMockVerifyOtpFileNameByCase(MockVerifyOtpUseCase mockCase) {
  return mockCase.value;
}
