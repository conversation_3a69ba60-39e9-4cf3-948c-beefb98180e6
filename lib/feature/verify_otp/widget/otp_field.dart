import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/feature/keyboard_visible/keyboard_controller.dart';
import 'package:flutter_common_package/widget/otp_listenable/otp_listenable_widget.dart';

import '../../../resources/resources.dart';
import '../../../util/screen_util.dart';

/// Code is based on [CommonPinCodeFields].
/// Figma https://www.figma.com/design/o0mjhHZYvsfTQfK4X14tj7/Kyko-Design-System?node-id=65-6100
/// Flutter sample https://flutter-shadcn-ui.mariuti.com/components/input-otp
class OtpField extends StatefulWidget {
  final BuildContext context;

  final FocusNode? focusNode;

  final TextEditingController? controller;

  final ValueChanged<String>? onChanged;

  final ValueChanged<String>? onCompleted;

  final String? error;

  final bool obscureText;

  const OtpField({
    required this.context,
    this.controller,
    this.focusNode,
    this.onChanged,
    this.onCompleted,
    this.error,
    this.obscureText = false,
    super.key,
  });

  @override
  State<OtpField> createState() => _OtpFieldState();
}

class _OtpFieldState extends State<OtpField> with TickerProviderStateMixin {
  late final TextEditingController? _textController;

  late final FocusNode? _focusNode;

  late List<String> _inputList;

  int _focusedIndex = 0;

  late AnimationController _blinkController;
  late Animation<double> _blinkAnimation;

  final TextStyle _textStyle = evoTextStyles.regular(TextSize.h5);

  final Radius radius = Radius.circular(6.w);

  final double _fieldSize = 40.w;

  final int _pinLength = 6;

  bool get _hasError => widget.error != null && widget.error?.isNotEmpty == true;

  late final CommonKeyboardController keyboardController = CommonKeyboardController();

  @override
  void initState() {
    super.initState();
    _textController = widget.controller ?? TextEditingController();
    _textController!.addListener(_handleTextChange);

    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode!.addListener(() {
      _setState(() {});
    });

    _inputList = List<String>.filled(_pinLength, '');

    _blinkController =
        AnimationController(duration: const Duration(milliseconds: 1000), vsync: this);
    _blinkAnimation = Tween<double>(begin: 1, end: 0)
        .animate(CurvedAnimation(parent: _blinkController, curve: Curves.easeIn));
    _blinkController.repeat();

    if (_textController.text.isNotEmpty) {
      _setTextToInput(_textController.text);
    }

    keyboardController.register();
  }

  Future<void> _handleTextChange() async {
    String currentText = _textController!.text;

    if (_inputList.join() != currentText) {
      if (currentText.length >= _pinLength) {
        if (widget.onCompleted != null) {
          if (currentText.length > _pinLength) {
            currentText = currentText.substring(0, _pinLength);
          }
          await Future<void>.delayed(const Duration(milliseconds: 300));
          widget.onCompleted!(currentText);
        }
        _focusNode!.unfocus();
      }
      _setTextToInput(currentText);

      widget.onChanged?.call(currentText);
    }
  }

  @override
  void dispose() {
    _textController!.removeListener(_handleTextChange);
    _textController.dispose();
    _focusNode!.dispose();
    _blinkController.dispose();
    keyboardController.dispose();
    super.dispose();
  }

  Future<void> _onTap() async {
    _textController?.clear();

    if (_focusNode?.hasFocus == false) {
      _focusNode?.requestFocus();
    }
  }

  void _setTextToInput(String data) {
    final List<String> replaceInputList = List<String>.filled(_pinLength, '');

    for (int i = 0; i < _pinLength; i++) {
      replaceInputList[i] = data.length > i ? data[i] : '';
    }

    _setState(() {
      _focusedIndex = data.length;
      _inputList = replaceInputList;
    });
  }

  void _setState(void Function() function) {
    if (mounted) {
      setState(function);
    }
  }

  void onOtpCodeReceived(String code) {
    _textController?.text = code;
  }

  @override
  Widget build(BuildContext context) {
    return keyboardController.initKeyboardProvider(
        child: Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        _buildOptField(),
        _buildError(),
      ],
    ));
  }

  Widget _buildOptField() {
    final TextFormField textField = TextFormField(
      // The desired behavior is no input action at all (TextInputAction.none) but iOS does not support yet.
      // With TextInputAction.done, the OTP field would have a same behavior with the PIN field.
      // TextInputAction.done is supported by both Android and iOS.
      textInputAction: TextInputAction.done,
      controller: _textController,
      focusNode: _focusNode,
      enabled: true,
      autocorrect: false,
      keyboardType: TextInputType.number,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      inputFormatters: <TextInputFormatter>[
        FilteringTextInputFormatter.digitsOnly,
        LengthLimitingTextInputFormatter(_pinLength),
      ],
      enableInteractiveSelection: false,
      showCursor: false,
      decoration: InputDecoration(
        contentPadding: const EdgeInsets.all(0),
        border: InputBorder.none,
        enabledBorder: InputBorder.none,
        focusedBorder: InputBorder.none,
        disabledBorder: InputBorder.none,
        errorBorder: InputBorder.none,
        focusedErrorBorder: InputBorder.none,
      ),
      style: TextStyle(color: evoColors.transparent, height: 0, fontSize: 0),
      obscureText: widget.obscureText,
    );

    return OtpListenableWidget(
      onOtpCodeReceived: onOtpCodeReceived,
      child: SizedBox(
        height: _fieldSize,
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: <Widget>[
            AbsorbPointer(
              child: AutofillGroup(child: textField),
            ),
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: GestureDetector(
                onTap: _onTap,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: _generateFields(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildError() {
    if (!_hasError) {
      return SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsets.only(top: 8.w),
      child: SizedBox(
        width: 280.w,
        child: Text(
          widget.error!,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
          textAlign: TextAlign.center,
          style: evoTextStyles.regular(TextSize.s, color: evoColors.errorBase),
        ),
      ),
    );
  }

  List<Widget> _generateFields() {
    return <Widget>[
      _buildField(0, _Position.start),
      _buildField(1, _Position.mid),
      _buildField(2, _Position.end),
      _buildDot(),
      _buildField(3, _Position.start),
      _buildField(4, _Position.mid),
      _buildField(5, _Position.end),
    ];
  }

  Widget _buildDot() {
    return Container(
      width: 4.w,
      height: 4.w,
      margin: EdgeInsets.symmetric(horizontal: 18.w),
      decoration: BoxDecoration(color: evoColors.black, shape: BoxShape.circle),
    );
  }

  /// There are 4 states for a field: normal (no error), normalFocused, error, errorFocused.
  T _onState<T>(
    int index, {
    required T normal,
    required T normalFocused,
    T? error,
    T? errorFocused,
  }) {
    if (_isFocused(index)) {
      if (_hasError) {
        return errorFocused ?? normalFocused;
      } else {
        return normalFocused;
      }
    } else {
      if (_hasError) {
        return error ?? normal;
      } else {
        return normal;
      }
    }
  }

  bool _isFocused(int index) {
    return (_focusedIndex == index || (_focusedIndex == index + 1 && index + 1 == _pinLength)) &&
        _focusNode!.hasFocus;
  }

  Color _getBorderColor(int index) {
    return _onState(
      index,
      normal: evoColors.grayBorders,
      normalFocused: evoColors.secondaryBase,
      error: evoColors.errorBase,
      errorFocused: evoColors.grayBase,
    );
  }

  double _getBorderWidth(int index) {
    return _onState(
      index,
      normal: 1.w,
      normalFocused: 2.w,
      error: 1.w,
    );
  }

  Border _getBorder(int index, _Position pos) {
    final BorderSide side = BorderSide(
      color: _getBorderColor(index),
      width: _getBorderWidth(index),
    );
    final Border borderAll = Border.fromBorderSide(side);
    final Border borderNoLeft = Border(top: side, right: side, bottom: side);

    final bool isFocused = _isFocused(index);
    return switch (pos) {
      _Position.start => borderAll,
      _Position.mid => isFocused ? borderAll : borderNoLeft,
      _Position.end => isFocused ? borderAll : borderNoLeft,
    };
  }

  BorderRadius _getBorderRadius(_Position pos) {
    return switch (pos) {
      _Position.start => BorderRadius.only(topLeft: radius, bottomLeft: radius),
      _Position.mid => BorderRadius.zero,
      _Position.end => BorderRadius.only(topRight: radius, bottomRight: radius),
    };
  }

  Widget _buildField(int index, _Position pos) {
    return Container(
      width: _fieldSize,
      height: _fieldSize,
      decoration: BoxDecoration(
        color: evoColors.transparent,
        borderRadius: _getBorderRadius(pos),
        border: _getBorder(index, pos),
      ),
      child: Center(
        child: _buildChild(index),
      ),
    );
  }

  Widget _buildChild(int index) {
    final bool isLastItem = index + 1 == _pinLength;
    final bool isInputFull = _focusedIndex == index + 1;
    if (((_focusedIndex == index) || (isInputFull && isLastItem)) && _focusNode!.hasFocus) {
      return _buildCursor();
    }
    return _buildText(index);
  }

  Widget _buildText(int index) {
    final String inputString = _inputList[index];
    final String text = widget.obscureText && inputString.isNotEmpty ? '•' : inputString;
    return Text(
      text,
      key: Key(inputString),
      style: _textStyle,
    );
  }

  Widget _buildCursor() {
    return FadeTransition(
      opacity: _blinkAnimation,
      child: SizedBox(
        width: 2.w,
        height: _textStyle.fontSize! + 2.w,
        child: ColoredBox(color: _textStyle.color!),
      ),
    );
  }
}

enum _Position { start, mid, end }
