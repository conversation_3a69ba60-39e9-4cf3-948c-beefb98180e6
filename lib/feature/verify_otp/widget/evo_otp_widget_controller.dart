import 'package:flutter/foundation.dart';

import '../../../widget/countdown/countdown_widget_builder.dart';
import 'evo_otp_widget.dart';

class OtpWidgetController {
  OtpWidgetController() {
    countdown = CountdownController(onDone: onCountdownDone);
  }

  late final CountdownController countdown;
  final ValueNotifier<ResendOtpStatus> _resendStatusNotifier =
      ValueNotifier<ResendOtpStatus>(ResendOtpStatus.disabled);

  ValueListenable<ResendOtpStatus> get listenableResendOtpStatus => _resendStatusNotifier;

  void startCountdown() {
    countdown.start();
    _resendStatusNotifier.value = ResendOtpStatus.disabled;
  }

  @visibleForTesting
  void onCountdownDone() {
    _resendStatusNotifier.value = _resendStatusNotifier.value = ResendOtpStatus.enabled;
  }
}
