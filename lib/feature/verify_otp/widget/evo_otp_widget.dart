import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../resources/resources.dart';
import '../../../util/screen_util.dart';
import '../../../widget/countdown/countdown_widget_builder.dart';
import 'evo_otp_widget_controller.dart';
import 'otp_field.dart';

enum ResendOtpStatus {
  enabled,
  disabled,
}

class EvoOtpWidget extends StatefulWidget {
  const EvoOtpWidget({
    required this.onSubmit,
    required this.onResendOtp,
    required this.controller,
    this.otpValiditySecs,
    this.errorText,
    super.key,
  });

  final String? errorText;
  final void Function(String)? onSubmit;
  final void Function() onResendOtp;
  final int? otpValiditySecs;
  final OtpWidgetController controller;

  @override
  State<EvoOtpWidget> createState() => EvoOtpWidgetState();
}

@visibleForTesting
class EvoOtpWidgetState extends State<EvoOtpWidget> {
  final FocusNode _focusNode = FocusNode();
  final TextEditingController _phoneInputController = TextEditingController();
  late final Duration otpValidDuration;

  OtpWidgetController get controller => widget.controller;

  @override
  void initState() {
    super.initState();

    commonUtilFunction.delayAndRequestFocus(_focusNode);
    otpValidDuration = switch (widget.otpValiditySecs) {
      final int secs => Duration(seconds: secs),
      null => Duration.zero,
    };
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        OtpField(
          context: context,
          controller: _phoneInputController,
          focusNode: _focusNode,
          onCompleted: widget.onSubmit,
          error: widget.errorText,
        ),
        _buildCountdownDesc(),
        _buildResendWidget(),
      ],
    );
  }

  Widget _buildResendWidget() {
    return ValueListenableBuilder<ResendOtpStatus>(
        valueListenable: controller.listenableResendOtpStatus,
        builder: (BuildContext context, ResendOtpStatus status, _) {
          if (status != ResendOtpStatus.enabled) {
            return SizedBox.shrink();
          }
          return Padding(
            padding: EdgeInsets.only(top: 16.w),
            child: RichText(
                text: TextSpan(style: evoTextStyles.regular(TextSize.base), children: [
              TextSpan(
                text: EvoStrings.otpResendCodeDesc,
              ),
              TextSpan(
                text: ' ${EvoStrings.otpResendCode}',
                style: TextStyle(color: evoColors.secondaryBase),
                recognizer: TapGestureRecognizer()..onTap = _onResendOtp,
              )
            ])),
          );
        });
  }

  void _onResendOtp() {
    widget.onResendOtp();
    _phoneInputController.clear();
  }

  Widget _buildCountdownDesc() {
    return ValueListenableBuilder<ResendOtpStatus>(
        valueListenable: controller.listenableResendOtpStatus,
        builder: (BuildContext context, ResendOtpStatus status, _) {
          return Offstage(
            offstage: status == ResendOtpStatus.enabled,
            child: Padding(
              padding: EdgeInsets.only(top: 8.w),
              child: CountdownWidgetBuilder(
                builder: (BuildContext context, Duration duration) {
                  return Text(
                    EvoStrings.otpExpiredDesc.replaceVariableByValue(
                      <String>[duration.remainder()],
                    ),
                    style: evoTextStyles.regular(
                      TextSize.base,
                    ),
                  );
                },
                controller: controller.countdown,
                duration: otpValidDuration,
              ),
            ),
          );
        });
  }
}
