// coverage:ignore-file
// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import '../cubit/otp_success_model.dart';
import '../cubit/verify_otp_cubit.dart';
import 'verify_otp_handler.dart';

class VerifyEmailOtpHandler implements VerifyOtpHandler {
  // TODO: integrate API
  @override
  Future<VerifyOtpModel> verifyOtp(String otp, Map<String, String>? data) async {
    await Future<void>.delayed(const Duration(seconds: 1));
    return VerifyOtpModel.success(
      successModel: OtpSuccessModel(
        challengeType: 'none',
        sessionToken: 'none',
      ),
    );
  }

  // TODO: integrate API
  @override
  Future<ResendOtpModel> resendOtp(String contactInfo, Map<String, String>? data) async {
    return ResendOtpModel.success(
      resendOtpSuccess: ResendOtpSuccess(
        sessionToken: 'token',
        otpResendSecs: 10,
        otpValiditySecs: 10,
      ),
    );
  }
}
