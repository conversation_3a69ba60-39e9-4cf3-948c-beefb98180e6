import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../data/repository/authentication_repo.dart';
import '../../../data/response/sign_in_entity.dart';
import '../cubit/otp_success_model.dart';
import '../cubit/verify_otp_cubit.dart';
import '../mock/mock_verify_otp_use_case.dart';
import 'verify_otp_handler.dart';

class SignInVerifyOtpHandler extends VerifyOtpHandler {
  final AuthenticationRepo authRepo;

  SignInVerifyOtpHandler({
    required this.authRepo,
  });

  @override
  Future<ResendOtpModel> resendOtp(String phoneNumber, Map<String, String>? data) async {
    final SignInEntity entity = await authRepo.signIn(TypeLogin.otp,
        phoneNumber: phoneNumber,
        mockConfig: MockConfig(
          enable: false,
          fileName: getMockVerifyOtpFileNameByCase(
            MockVerifyOtpUseCase.getVerifySignInOtpSuccess,
          ),
          statusCode: CommonHttpClient.INVALID_TOKEN,
        ));

    return _generateResendOtpModelFromEntity(entity);
  }

  @override
  Future<VerifyOtpModel> verifyOtp(String otp, Map<String, String>? data) async {
    final String sessionToken = data?['sessionToken'] ?? '';
    final SignInEntity entity = await authRepo.signIn(
      TypeLogin.verifyOTP,
      otp: otp,
      sessionToken: sessionToken,
      mockConfig: MockConfig(
        enable: true,
        fileName: getMockVerifyOtpFileNameByCase(
          MockVerifyOtpUseCase.getVerifySignInOtpSuccess,
        ),
      ),
    );

    return _generateVerifyOtpModelFromEntity(entity);
  }

  ResendOtpModel _generateResendOtpModelFromEntity(SignInEntity entity) {
    switch (entity.statusCode) {
      case CommonHttpClient.SUCCESS:
        return ResendOtpModel.success(
          resendOtpSuccess: ResendOtpSuccess(
            sessionToken: entity.sessionToken,
            otpResendSecs: entity.otpResendSecs,
            otpValiditySecs: entity.otpValiditySecs,
          ),
        );
      case CommonHttpClient.INVALID_TOKEN:
        return ResendOtpModel.error(
          errorType: OtpErrorType.sessionExpired,
          errorUIModel: ErrorUIModel.fromEntity(entity),
        );
      case CommonHttpClient.BAD_REQUEST:
        return ResendOtpModel.error(
          errorType: OtpErrorType.invalidParams,
          errorUIModel: ErrorUIModel.fromEntity(entity),
        );
        break;
      case CommonHttpClient.LIMIT_EXCEEDED:
        return ResendOtpModel.error(
          errorType: OtpErrorType.limitExceeded,
          errorUIModel: ErrorUIModel.fromEntity(entity),
        );
      default:
        return ResendOtpModel.error(
          errorType: OtpErrorType.unknown,
          errorUIModel: ErrorUIModel.fromEntity(entity),
        );
    }
  }

  VerifyOtpModel _generateVerifyOtpModelFromEntity(SignInEntity entity) {
    switch (entity.statusCode) {
      case CommonHttpClient.SUCCESS:
        return VerifyOtpModel.success(
          successModel: OtpSuccessModel(
            challengeType: entity.challengeType,
            sessionToken: entity.sessionToken,
            ekycClientSettings: entity.ekycClientSettings,
          ),
        );
      case CommonHttpClient.INVALID_TOKEN:
        return VerifyOtpModel.error(
          errorType: OtpErrorType.sessionExpired,
          errorUIModel: ErrorUIModel.fromEntity(entity),
        );
      case CommonHttpClient.LIMIT_EXCEEDED:
        return VerifyOtpModel.error(
          errorType: OtpErrorType.limitExceeded,
          errorUIModel: ErrorUIModel.fromEntity(entity),
        );
      case CommonHttpClient.BAD_REQUEST:
        return VerifyOtpModel.error(
          errorUIModel: ErrorUIModel.fromEntity(entity),
          errorType: OtpErrorType.invalidParams,
        );
      default:
        return VerifyOtpModel.error(
          errorUIModel: ErrorUIModel.fromEntity(entity),
          errorType: OtpErrorType.unknown,
        );
    }
  }
}
