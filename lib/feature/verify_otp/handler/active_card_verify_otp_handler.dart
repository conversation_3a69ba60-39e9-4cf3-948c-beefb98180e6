import '../cubit/otp_success_model.dart';
import '../cubit/verify_otp_cubit.dart';
import 'verify_otp_handler.dart';

class ActiveCardVerifyOtpHandler implements VerifyOtpHandler {
  @override
  Future<ResendOtpModel> resendOtp(String phoneNumber, Map<String, String>? data) async {
    /// TODO: nam-pham-ts mock data. Update when API is ready
    await Future<void>.delayed(const Duration(seconds: 2));
    return ResendOtpModel.success(resendOtpSuccess: ResendOtpSuccess(
      otpResendSecs: 120,
      sessionToken: 'sessionToken',
      otpValiditySecs: 240,
    ));
  }

  @override
  Future<VerifyOtpModel> verifyOtp(String otp, Map<String, String>? data) async {
    /// TODO: nam-pham-ts mock data. Update when API is ready
    await Future<void>.delayed(const Duration(seconds: 2));
    return VerifyOtpModel.success(
        successModel: OtpSuccessModel(challengeType: 'none', sessionToken: 'none'));
  }
}
