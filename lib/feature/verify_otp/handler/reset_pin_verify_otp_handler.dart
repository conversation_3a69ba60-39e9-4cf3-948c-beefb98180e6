import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../data/repository/authentication_repo.dart';
import '../../../data/request/reset_pin_request.dart';
import '../../../data/response/reset_pin_entity.dart';
import '../../../util/mock_file_name_utils/mock_authentication_file_name.dart';
import '../../pin/mock/mock_pin_use_case.dart';
import '../cubit/otp_success_model.dart';
import '../cubit/verify_otp_cubit.dart';
import 'verify_otp_handler.dart';

class ResetPinVerifyOtpHandler extends VerifyOtpHandler {
  final AuthenticationRepo authRepo;

  ResetPinVerifyOtpHandler({required this.authRepo});

  @override
  Future<ResendOtpModel> resendOtp(String phoneNumber, Map<String, String>? data) async {
    final ResetPinEntity entity = await authRepo.resetPin(
        request: InitializeResetPinRequest(phoneNumber: phoneNumber),
        mockConfig: MockConfig(
          enable: false,
          fileName: getRequestResetPinMockFileName(),
        ));
    return _generateResentOtpModel(entity);
  }

  @override
  Future<VerifyOtpModel> verifyOtp(String otp, Map<String, String>? data) async {
    final String sessionToken = data?['sessionToken'] ?? '';
    final ResetPinEntity entity = await authRepo.resetPin(
      request: ResetPinVerifyOTPRequest(
        otp: otp,
        sessionToken: sessionToken,
      ),
      mockConfig: MockConfig(
        enable: true,
        fileName: getMockPinFileNameByCase(
          MockPinUseCase.getResetPinVerifyOTPSuccess,
        ),
      ),
    );

    return _generateVerifyOtpModel(entity);
  }

  ResendOtpModel _generateResentOtpModel(ResetPinEntity entity) {
    switch (entity.statusCode) {
      case CommonHttpClient.SUCCESS:
        return ResendOtpModel.success(
          resendOtpSuccess: ResendOtpSuccess(
            sessionToken: entity.sessionToken,
            otpResendSecs: entity.otpResendSecs,
            otpValiditySecs: entity.otpValiditySecs,
          ),
        );
      case CommonHttpClient.INVALID_TOKEN:
        return ResendOtpModel.error(
          errorType: OtpErrorType.sessionExpired,
          errorUIModel: ErrorUIModel.fromEntity(entity),
        );
      case CommonHttpClient.LOCKED_RESOURCE:
      case CommonHttpClient.LIMIT_EXCEEDED:
        return ResendOtpModel.error(
          errorType: OtpErrorType.limitExceeded,
          errorUIModel: ErrorUIModel.fromEntity(entity),
        );
      default:
        return ResendOtpModel.error(
          errorType: OtpErrorType.unknown,
          errorUIModel: ErrorUIModel.fromEntity(entity),
        );
    }
  }

  VerifyOtpModel _generateVerifyOtpModel(ResetPinEntity entity) {
    switch (entity.statusCode) {
      case CommonHttpClient.BAD_REQUEST:
        return VerifyOtpModel.error(
          errorType: OtpErrorType.invalidParams,
          errorUIModel: ErrorUIModel.fromEntity(entity),
        );
      case CommonHttpClient.INVALID_TOKEN:
        return VerifyOtpModel.error(
          errorType: OtpErrorType.sessionExpired,
          errorUIModel: ErrorUIModel.fromEntity(entity),
        );
      case CommonHttpClient.LIMIT_EXCEEDED:
        return VerifyOtpModel.error(
          errorType: OtpErrorType.limitExceeded,
          errorUIModel: ErrorUIModel.fromEntity(entity),
        );
      case CommonHttpClient.SUCCESS:
        return VerifyOtpModel.success(
          successModel: OtpSuccessModel(
            challengeType: entity.challengeType,
            sessionToken: entity.sessionToken,
            ekycClientSettings: entity.ekycClientSettings,
          ),
        );
      default:
        return VerifyOtpModel.error(
          errorType: OtpErrorType.unknown,
          errorUIModel: ErrorUIModel.fromEntity(entity),
        );
    }
  }
}
