import '../../../data/repository/authentication_repo.dart';
import '../verify_otp_page.dart';
import 'activate_account_verify_otp_handler.dart';
import 'active_card_verify_otp_handler.dart';
import 'reset_pin_verify_otp_handler.dart';
import 'sign_in_verify_otp_handler.dart';
import 'verify_email_otp_handler.dart';
import 'verify_otp_handler.dart';

class VerifyOtpHandlerFactory {
  static VerifyOtpHandler createHandler(VerifyOtpType type, AuthenticationRepo authRepo) {
    switch (type) {
      case VerifyOtpType.signIn:
        return SignInVerifyOtpHandler(authRepo: authRepo);
      case VerifyOtpType.resetPin:
        return ResetPinVerifyOtpHandler(authRepo: authRepo);
      case VerifyOtpType.activateAccount:
        return ActivateAccountVerifyOtpHandler(authRepo: authRepo);
      case VerifyOtpType.activateCard:

        /// TODO: nam-pham-ts update when API is ready
        return ActiveCardVerifyOtpHandler();
      case VerifyOtpType.email:
        return VerifyEmailOtpHandler();
      default:
        throw ArgumentError('Invalid VerifyOtpType: $type');
    }
  }
}
