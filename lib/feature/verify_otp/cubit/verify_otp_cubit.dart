import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../handler/verify_otp_handler.dart';
import 'otp_success_model.dart';

part 'verify_otp_state.dart';

class VerifyOtpCubit extends CommonCubit<VerifyOtpState> {
  final VerifyOtpHandler verifyHandler;

  VerifyOtpCubit({
    required this.verifyHandler,
  }) : super(VerifyOtpInitial());

  Future<void> verifyOtp(String otp, String? sessionToken) async {
    emit(VerifyOtpLoading());

    final VerifyOtpModel model =
        await verifyHandler.verifyOtp(otp, <String, String>{'sessionToken': sessionToken ?? ''});

    if (model.isSuccess) {
      emit(VerifyOtpSuccess(model.successModel!));
      return;
    }

    /// errorType & errorUIModel is not null. So it's safe to use ! operator
    _handleError(errorType: model.errorType!, errorUIModel: model.errorUIModel!);
  }

  Future<void> resendOtp(String contactInfo, String? sessionToken) async {
    emit(VerifyOtpLoading());

    final ResendOtpModel model = await verifyHandler
        .resendOtp(contactInfo, <String, String>{'sessionToken': sessionToken ?? ''});

    if (model.isSuccess == true) {
      emit(ResendOtpSuccess(
        otpResendSecs: model.resendOtpSuccess?.otpResendSecs,
        sessionToken: model.resendOtpSuccess?.sessionToken,
        otpValiditySecs: model.resendOtpSuccess?.otpValiditySecs,
      ));
      return;
    }

    /// errorType & errorUIModel is not null. So it's safe to use ! operator
    _handleError(
      errorType: model.errorType!,
      errorUIModel: model.errorUIModel!,
      isResendFeature: true,
    );
  }

  void _handleError({
    required OtpErrorType errorType,
    required ErrorUIModel errorUIModel,
    bool isResendFeature = false,
  }) {
    switch (errorType) {
      case OtpErrorType.sessionExpired:
        emit(VerifyOtpSessionExpired());
      case OtpErrorType.limitExceeded:
        emit(LimitExceedOtp(errorText: errorUIModel.userMessage, isResent: isResendFeature));
      case OtpErrorType.invalidParams:
        emit(VerifyOtpFailed(
          error: errorUIModel,
          unknownError: false,
        ));
      default:
        emit(VerifyOtpFailed(error: errorUIModel));
    }
  }
}
