part of 'verify_otp_cubit.dart';

@immutable
sealed class VerifyOtpState implements BlocState {}

class VerifyOtpInitial extends VerifyOtpState {}

class VerifyOtpLoading extends VerifyOtpState {}

class LimitExceedOtp extends VerifyOtpState {
  final String? errorText;
  final bool isResent;

  LimitExceedOtp({required this.errorText, this.isResent = false});
}

class ResendOtpSuccess extends VerifyOtpState {
  final int? otpResendSecs;
  final String? sessionToken;
  final int? otpValiditySecs;

  ResendOtpSuccess({
    required this.otpResendSecs,
    required this.sessionToken,
    required this.otpValiditySecs,
  });
}

class VerifyOtpFailed extends VerifyOtpState {
  final ErrorUIModel error;
  final bool unknownError;

  VerifyOtpFailed({
    required this.error,
    this.unknownError = true,
  });
}

class VerifyOtpSessionExpired extends VerifyOtpState {}

class VerifyOtpSuccess extends VerifyOtpState {
  final OtpSuccessModel uiModel;

  VerifyOtpSuccess(this.uiModel);
}
