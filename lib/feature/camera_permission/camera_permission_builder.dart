import 'package:app_settings/app_settings.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../base/evo_page_state_base.dart';
import '../../model/evo_dialog_id.dart';
import '../../resources/ui_strings.dart';
import '../../util/dialog_functions.dart';
import 'cubit/camera_permission_cubit.dart';
import 'cubit/camera_permission_state.dart';

class CameraPermissionBuilder extends PageBase {
  final Widget Function(BuildContext context, CameraPermissionState state) builder;

  const CameraPermissionBuilder({
    required this.builder,
    super.key,
  });

  @override
  EvoPageStateBase<PageBase> createState() => CameraPermissionBuilderState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: 'camera_permission_builder');
}

@visibleForTesting
class CameraPermissionBuilderState extends EvoPageStateBase<CameraPermissionBuilder> {
  @visibleForTesting
  late final CameraPermissionCubit cameraCubit;

  // To reference the dialog and pop it when needed
  @visibleForTesting
  GlobalKey cameraDialogKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    cameraCubit = context.read<CameraPermissionCubit?>() ?? CameraPermissionCubit();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      cameraCubit.requestPermission();
    });
  }

  @override
  void onPageResumed() {
    super.onPageResumed();
    cameraCubit.requestPermissionOnResume();
  }

  @override
  void onPagePaused() {
    super.onPagePaused();
    cameraCubit.resetIfPermissionDenied();
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocConsumer<CameraPermissionCubit, CameraPermissionState>(
      bloc: cameraCubit,
      listener: (_, CameraPermissionState state) => _handleCameraPermissionState(state),
      builder: widget.builder,
    );
  }

  void _handleCameraPermissionState(CameraPermissionState state) {
    switch (state) {
      case CameraPermissionGranted():
        return;
      case CameraPermissionDenied():
        _showOpenAppSettingsCameraDialog();
        return;
      case CameraPermissionInitial():
        _hideCameraPermissionDialog();
        return;
    }
  }

  void _showOpenAppSettingsCameraDialog() {
    evoDialogFunction.showDialogConfirm(
      key: cameraDialogKey,
      dialogId: EvoDialogId.openDeviceSecuritySettingDialog,
      title: EvoStrings.cameraPermissionTitle,
      content: EvoStrings.cameraPermissionDesc,
      textPositive: EvoStrings.settingTitle,
      textNegative: EvoStrings.ignoreTitle,
      isDismissible: false,
      onClickPositive: () {
        AppSettings.openAppSettings();
      },
      onClickNegative: () {
        _hideCameraPermissionDialog();
        navigatorContext?.pop();
      },
    );
  }

  void _hideCameraPermissionDialog() {
    cameraDialogKey.currentContext?.pop();
  }
}
