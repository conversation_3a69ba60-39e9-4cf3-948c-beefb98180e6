import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/util/permission/device_permission.dart';
import 'package:flutter_common_package/util/permission/permission_handler_mixin.dart';

import 'camera_permission_state.dart';

@visibleForTesting
class CameraPermissionHandler with PermissionHandlerMixin {
  const CameraPermissionHandler();
}

class CameraPermissionCubit extends CommonCubit<CameraPermissionState>
    implements PermissionHandlerCallback {
  final PermissionHandlerMixin _handler;

  CameraPermissionCubit({PermissionHandlerMixin handler = const CameraPermissionHandler()})
      : _handler = handler,
        super(CameraPermissionInitial());

  void requestPermission() {
    _handler.requestPermissionWhenPageIsInit(
      devicePermission: TsDevicePermission.camera,
      callback: this,
    );
  }

  void requestPermissionOnResume() {
    // To prevent requesting permission again when camera cubit is not reset
    if (state is CameraPermissionInitial) {
      requestPermission();
    }
  }

  void resetIfPermissionDenied() {
    if (state is CameraPermissionDenied) {
      emit(CameraPermissionInitial());
    }
  }

  @override
  void onDeniedPermission(TsDevicePermission permission) {
    emit(CameraPermissionDenied());
  }

  @override
  void onGrantedPermission(TsDevicePermission permission) {
    emit(CameraPermissionGranted());
  }
}
