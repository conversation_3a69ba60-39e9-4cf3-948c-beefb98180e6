import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/common_package/common_package.dart';

sealed class CameraPermissionState extends BlocState with EquatableMixin {
  @override
  List<Object?> get props => <Object?>[];
}

class CameraPermissionInitial extends CameraPermissionState {}

class CameraPermissionGranted extends CameraPermissionState {}

class CameraPermissionDenied extends CameraPermissionState {}
