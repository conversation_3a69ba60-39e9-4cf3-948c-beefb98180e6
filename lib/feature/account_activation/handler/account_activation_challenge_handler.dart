import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../data/response/account_activation_entity.dart';
import 'account_activation_ui_handler.dart';

class AccountActivationChallengeHandler {
  @visibleForTesting
  late AccountActivationUiHandler uiHandler;

  AccountActivationChallengeHandler({
    required void Function(ErrorUIModel? uiModel) onError,
  }) {
    uiHandler = AccountActivationUiHandler(
      onError: onError,
      onSuccess: nextChallenge,
    );
  }

  void nextChallenge({
    required AccountActivationEntity entity,
    String? phoneNumber,
  }) {
    final AccountActivationType type = AccountActivationType.fromString(entity.challengeType);
    switch (type) {
      case AccountActivationType.verifyOTP:
        uiHandler.verifyOtp(
          entity: entity,
          phoneNumber: phoneNumber,
        );
      case AccountActivationType.verifySelfie:
        uiHandler.verifySelfie(
          entity: entity,
        );
      case AccountActivationType.createUsername:
        uiHandler.createUsername(
          entity: entity,
        );
        return;
      case AccountActivationType.createPin:
        uiHandler.createPin();
        return;
      case AccountActivationType.verifyEmail:
        uiHandler.verifyEmail(entity: entity);
      case AccountActivationType.verifyEmailOtp:
        uiHandler.verifyEmailOtp(entity: entity);
      case AccountActivationType.none:
        uiHandler.activateAccountSuccess();
        return;
      case AccountActivationType.unknown:
        commonLog('unknown challenge:$type', methodName: 'nextChallenge');
        uiHandler.onError(ErrorUIModel());
        return;
    }
  }
}
