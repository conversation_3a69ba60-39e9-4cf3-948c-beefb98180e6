import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../data/response/account_activation_entity.dart';
import '../../ekyc/intro/face_capture_check_screen.dart';
import '../../pin/change_pin/create_new_pin_flow.dart';
import '../../pin/change_pin/create_new_pin_screen.dart';
import '../../verify_otp/cubit/verify_otp_cubit.dart';
import '../../verify_otp/verify_otp_page.dart';
import '../activate_account_success_screen.dart';
import '../create_username/create_username_screen.dart';
import '../verify_email/verify_email_screen.dart';

enum AccountActivationType {
  verifyOTP(verifyOTPValue),
  verifySelfie(verifySelfieValue),
  createUsername(createUsernameValue),
  createPin(createPinValue),
  verifyEmail(verifyEmailValue),
  verifyEmailOtp(verifyEmailOtpValue),
  none(noneValue),
  unknown(unknownValue);

  final String value;

  const AccountActivationType(this.value);

  static const String verifyOTPValue = 'verify_otp';
  static const String verifySelfieValue = 'face_auth';
  static const String createUsernameValue = 'create_user_name';
  static const String createPinValue = 'create_pin';
  static const String verifyEmailValue = 'verify_email';
  static const String verifyEmailOtpValue = 'verify_email_otp';
  static const String noneValue = 'none';
  static const String unknownValue = 'unknown';

  static AccountActivationType fromString(String? value) {
    switch (value) {
      case verifyOTPValue:
        return AccountActivationType.verifyOTP;
      case verifySelfieValue:
        return AccountActivationType.verifySelfie;
      case createUsernameValue:
        return AccountActivationType.createUsername;
      case createPinValue:
        return AccountActivationType.createPin;
      case verifyEmailValue:
        return AccountActivationType.verifyEmail;
      case verifyEmailOtpValue:
        return AccountActivationType.verifyEmailOtp;
      case noneValue:
        return AccountActivationType.none;
      default:
        return AccountActivationType.unknown;
    }
  }
}

class AccountActivationUiHandler {
  void Function(ErrorUIModel? uiModel) onError;
  void Function({
    required AccountActivationEntity entity,
  }) onSuccess;

  AccountActivationUiHandler({
    required this.onError,
    required this.onSuccess,
  });

  void verifyOtp({
    required AccountActivationEntity entity,
    required String? phoneNumber,
  }) {
    VerifyOtpPage.pushNamed(
      contactInfo: phoneNumber,
      otpResendSecs: entity.otpResendSecs,
      otpValiditySecs: entity.otpValiditySecs,
      verifyOtpType: VerifyOtpType.activateAccount,
      sessionToken: entity.sessionToken,
      onPopSuccess: _verifyOtpPopSuccess,
    );
  }

  void verifySelfie({required AccountActivationEntity entity}) {
    FaceCaptureCheckScreen.pushNamed(
      sessionToken: entity.sessionToken,
      onPopSuccess: _onPopSuccess,
    );
  }

  void createUsername({required AccountActivationEntity entity}) {
    CreateUsernameScreen.pushNamed(
      sessionToken: entity.sessionToken,
      onPopSuccess: _onPopSuccess,
    );
  }

  void createPin() {
    CreateNewPinScreen.pushNamed(
      flow: CreateNewPinFlow.createPin,
      onSuccess: _onPopSuccess,
    );
  }

  // TODO: integrate API
  void verifyEmail({required AccountActivationEntity entity}) {
    VerifyEmailScreen.pushNamed(
      email: '<EMAIL>',
      sessionToken: entity.sessionToken,
      onPopSuccess: _onPopSuccess,
    );
  }

  // TODO: integrate API
  void verifyEmailOtp({required AccountActivationEntity entity}) {
    VerifyOtpPage.pushNamed(
      contactInfo: '<EMAIL>',
      verifyOtpType: VerifyOtpType.email,
      otpValiditySecs: entity.otpValiditySecs,
      otpResendSecs: entity.otpResendSecs,
      sessionToken: entity.sessionToken,
      onPopSuccess: _verifyOtpPopSuccess,
    );
  }

  void _verifyOtpPopSuccess(VerifyOtpState state) {
    if (state is VerifyOtpSuccess) {
      /// TODO: hoang-nguyen-2 check [state.uiModel.verdict] for app status
      /// refer https://trustingsocial1.atlassian.net/browse/ENBCC-276
      final AccountActivationEntity nextEntity = AccountActivationEntity(
        challengeType: state.uiModel.challengeType,
        sessionToken: state.uiModel.sessionToken,
      );
      onSuccess(entity: nextEntity);
      return;
    }
    if (state is VerifyOtpFailed) {
      onError(state.error);
      return;
    }
  }

  void _onPopSuccess(BaseEntity entity) {
    if (entity is AccountActivationEntity) {
      onSuccess(entity: entity);
      return;
    }
    commonLog('entity is ${entity.runtimeType} should be instance of AccountActivationEntity',
        methodName: '_onPopSuccess');
    onError(ErrorUIModel());
  }

  void activateAccountSuccess() {
    ActivateAccountSuccessScreen.goNamed();
  }
}
