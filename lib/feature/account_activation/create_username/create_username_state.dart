part of 'create_username_cubit.dart';

@immutable
sealed class CreateU<PERSON>nameState implements BlocState {}

class CreateUsernameInitial extends CreateUsernameState {}

class CreateUsernameStateLoading extends CreateUsernameState {}

class CreateUsernameStateSuccess extends CreateUsernameState {
  final BaseEntity entity;

  CreateUsernameStateSuccess(this.entity);
}

class CreateUsernameInvalidUsername extends CreateUsernameState {
  final ErrorUIModel error;

  CreateUsernameInvalidUsername(this.error);
}

class ChangeUsernameState extends CreateUsernameState {
  final String username;

  ChangeUsernameState(this.username);
}

class CreateUsernameBadRequest extends CreateUsernameState {
  final ErrorUIModel error;

  CreateUsernameBadRequest(this.error);
}

class CreateUsernameInvalidToken extends CreateUsernameState {
  CreateUsernameInvalidToken();
}

class CreateUsernameFailed extends CreateUsernameState {
  final ErrorUIModel error;

  CreateUsernameFailed(this.error);
}
