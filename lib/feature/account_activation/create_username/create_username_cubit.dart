import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../data/repository/authentication_repo.dart';
import '../../../data/request/activate_account_request.dart';
import '../../../data/response/account_activation_entity.dart';
import '../../../util/validator/username_validator.dart';
import '../mock/mock_account_activation_use_case.dart';

part 'create_username_state.dart';

class CreateUsernameCubit extends CommonCubit<CreateUsernameState> {
  final AuthenticationRepo authRepo;
  final UsernameValidator usernameValidator;

  CreateUsernameCubit({
    required this.authRepo,
    required this.usernameValidator,
  }) : super(CreateUsernameInitial());

  Future<void> submit({
    required String username,
    required String? sessionToken,
  }) async {
    final String? errMsg = usernameValidator.validate(username);
    if (errMsg != null) {
      emit(CreateUsernameInvalidUsername(
        ErrorUIModel(userMessage: errMsg),
      ));
      return;
    }

    emit(CreateUsernameStateLoading());

    final AccountActivationEntity entity = await authRepo.activateAccount(
        request: ActivateAccountCreateUsernameRequest(
          username: username,
          sessionToken: sessionToken,
        ),
        mockConfig: MockConfig(
          enable: false,
          statusCode: CommonHttpClient.BAD_REQUEST,
          fileName: getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.getCreateUsernameBadRequest,
          ),
        ));

    _handleEntity(entity);
  }

  void updateUsername(String username) {
    emit(ChangeUsernameState(username));
  }

  void _handleEntity(BaseEntity entity) {
    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      emit(CreateUsernameStateSuccess(entity));
      return;
    }

    switch (entity.statusCode) {
      case CommonHttpClient.INVALID_TOKEN:
        emit(CreateUsernameInvalidToken());
      case CommonHttpClient.BAD_REQUEST:
        emit(CreateUsernameBadRequest(ErrorUIModel.fromEntity(entity)));
      default:
        emit(CreateUsernameFailed(ErrorUIModel.fromEntity(entity)));
    }
  }
}
