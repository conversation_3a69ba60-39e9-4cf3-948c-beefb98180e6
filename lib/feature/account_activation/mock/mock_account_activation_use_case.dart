enum MockAccountActivationUseCase {
  getVerifyOtpChallengeType('get_account_activation_verify_otp_challenge_type.json'),
  getVerifySelfieChallengeType('get_account_activation_verify_selfie_challenge_type.json'),
  getCreateUsernameChallengeType('get_account_activation_create_username_challenge_type.json'),
  getVerifyEmailChallengeType('get_account_activation_verify_email_challenge_type.json'),
  getCreateUsernameSuccess('get_account_activation_create_user_name_success.json'),
  getCreateUsernameBadRequest('get_account_activation_create_user_name_bad_request.json'),
  getNoneChallengeType('get_none_challenge_type.json');

  final String value;

  const MockAccountActivationUseCase(this.value);
}

String getMockAccountActivationFileNameByCase(MockAccountActivationUseCase mockCase) {
  return mockCase.value;
}
