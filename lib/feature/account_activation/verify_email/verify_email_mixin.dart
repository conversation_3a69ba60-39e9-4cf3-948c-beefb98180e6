// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../util/functions.dart';
import 'cubit/verify_email_cubit.dart';
import 'cubit/verify_email_state.dart';

mixin VerifyEmailMixin<T extends PageBase> on EvoPageStateBase<T> {
  late final VerifyEmailCubit _verifyEmailCubit =
      context.read<VerifyEmailCubit?>() ?? VerifyEmailCubit();

  void _listenVerifyEmailState(VerifyEmailState state) {
    if (state is VerifyEmailLoading) {
      evoUtilFunction.showHudLoading();
      return;
    }
    evoUtilFunction.hideHudLoading();

    if (state is VerifyEmailSuccess) {
      onVerifyEmailSuccess(state.entity);
      return;
    }
  }

  @protected
  void onVerifyEmailSuccess(BaseEntity entity);

  @protected
  void verifyEmail({String? email, String? token}) {
    _verifyEmailCubit.verify(email: email, token: token);
  }

  Widget provideVerifyEmailCubit({required Widget child}) {
    return BlocProvider<VerifyEmailCubit>(
      create: (_) => _verifyEmailCubit,
      child: BlocListener<VerifyEmailCubit, VerifyEmailState>(
        listener: (_, VerifyEmailState state) => _listenVerifyEmailState(state),
        child: child,
      ),
    );
  }
}
