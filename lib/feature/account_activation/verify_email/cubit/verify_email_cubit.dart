// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter_common_package/base/common_cubit.dart';

import '../../../../data/response/account_activation_entity.dart';
import 'verify_email_state.dart';

class VerifyEmailCubit extends CommonCubit<VerifyEmailState> {
  VerifyEmailCubit() : super(VerifyEmailInitial());

  // TODO: integrate API, append the verified email address to AccountActivationEntity
  Future<void> verify({String? email, String? token}) async {
    emit(VerifyEmailLoading());
    await Future<void>.delayed(const Duration(seconds: 1));
    emit(VerifyEmailSuccess(AccountActivationEntity(
      challengeType: 'verify_email_otp',
      otpResendSecs: 10,
      otpValiditySecs: 10,
      sessionToken: 'token',
    )));
  }
}
