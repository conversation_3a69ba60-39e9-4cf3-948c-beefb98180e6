// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter_common_package/base/bloc_state.dart';

sealed class ValidateEmailState extends BlocState {}

class ValidateEmailInitial extends ValidateEmailState {}

class ValidateEmailSuccess extends ValidateEmailState {
  final String email;

  ValidateEmailSuccess(this.email);
}

class ValidateEmailFailure extends ValidateEmailState {
  final String error;

  ValidateEmailFailure({required this.error});
}
