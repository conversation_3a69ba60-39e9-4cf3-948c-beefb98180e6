// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../resources/resources.dart';
import '../../../widget/appbar/evo_appbar.dart';
import '../../../widget/buttons.dart';
import '../../../widget/evo_text_field.dart';
import 'cubit/validate_email_cubit.dart';
import 'cubit/validate_email_state.dart';
import 'verify_email_mixin.dart';

class InputEmailArg extends PageBaseArg {
  final String? email;

  final String? sessionToken;

  final void Function(BaseEntity entity) onPopSuccess;

  InputEmailArg({
    required this.onPopSuccess,
    this.email,
    this.sessionToken,
  });
}

class InputEmailScreen extends PageBase {
  final String? email;

  final String? sessionToken;

  final void Function(BaseEntity entity) onPopSuccess;

  const InputEmailScreen({
    required this.onPopSuccess,
    this.email,
    this.sessionToken,
    super.key,
  });

  @override
  State<InputEmailScreen> createState() => _InputEmailScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.inputEmailScreen.routeName);

  static void pushNamed({
    required void Function(BaseEntity entity) onPopSuccess,
    String? email,
    String? sessionToken,
  }) {
    navigatorContext?.pushNamed(
      Screen.inputEmailScreen.name,
      extra: InputEmailArg(
        email: email,
        sessionToken: sessionToken,
        onPopSuccess: onPopSuccess,
      ),
    );
  }
}

class _InputEmailScreenState extends EvoPageStateBase<InputEmailScreen>
    with VerifyEmailMixin<InputEmailScreen> {
  late final ValidateEmailCubit _validateEmailCubit =
      context.read<ValidateEmailCubit?>() ?? ValidateEmailCubit();

  late final TextEditingController _controller = TextEditingController(text: widget.email);

  late final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  void _listenValidateEmailState(ValidateEmailState state) {
    if (state is ValidateEmailSuccess) {
      verifyEmail(email: state.email, token: widget.sessionToken);
    }
  }

  @override
  void onVerifyEmailSuccess(BaseEntity entity) {
    widget.onPopSuccess(entity);
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<ValidateEmailCubit>(
      create: (_) => _validateEmailCubit,
      child: BlocListener<ValidateEmailCubit, ValidateEmailState>(
        listener: (_, ValidateEmailState state) => _listenValidateEmailState(state),
        child: Scaffold(
          appBar: EvoAppBar(),
          resizeToAvoidBottomInset: false,
          body: SafeArea(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                EvoDimension.space8,
                _buildTitle(),
                _buildTextField(),
                const Spacer(),
                _buildSendCodeBtn(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: EvoDimension.screenHorizontalPadding),
      child: Text(
        EvoStrings.inputEmailTitle,
        style: evoTextStyles.semibold(TextSize.h3, color: evoColors.grayText),
      ),
    );
  }

  Widget _buildTextField() {
    return Padding(
      padding: EdgeInsets.all(EvoDimension.screenHorizontalPaddingWithTextField),
      child: BlocBuilder<ValidateEmailCubit, ValidateEmailState>(
        builder: (_, ValidateEmailState state) {
          String error = '';
          if (state is ValidateEmailFailure) {
            error = state.error;
          }
          return EvoTextField(
            textEditingController: _controller,
            focusNode: _focusNode,
            keyboardType: TextInputType.emailAddress,
            textInputAction: TextInputAction.done,
            errMessage: error,
            onSubmitted: (_) => _validateEmail(),
            onChanged: (_) => _validateEmailCubit.onUpdateEmail(),
          );
        },
      ),
    );
  }

  Widget _buildSendCodeBtn() {
    return Padding(
      padding: EdgeInsets.only(
        left: EvoDimension.screenHorizontalPadding,
        right: EvoDimension.screenHorizontalPadding,
        bottom: EvoDimension.screenBottomPadding,
      ),
      child: provideVerifyEmailCubit(
        child: PrimaryButton(
          text: EvoStrings.sendEmailCodeBtn2,
          onTap: _validateEmail,
        ),
      ),
    );
  }

  void _validateEmail() {
    _validateEmailCubit.validate(_controller.text);
  }
}
