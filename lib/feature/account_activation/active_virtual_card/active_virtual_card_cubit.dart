import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';


part 'active_virtual_card_state.dart';

class CreateUsernameCubit extends CommonCubit<ActiveVirtualCardIntroState> {
  CreateUsernameCubit() : super(ActiveVirtualCardInitial());

  /// TODO nam-pham-ts update this method when API is available
  Future<void> active(String text) async {

    emit(ActiveVirtualCardStateLoading());

    await Future<void>.delayed(const Duration(seconds: 2));

    emit(ActiveVirtualCardFailed(ErrorUIModel(userMessage: 'Failed to create username')));
  }

}
