import 'package:flutter/material.dart';

import '../../../../resources/resources.dart';
import '../../../../util/screen_util.dart';

class FrameCardWithNameWidget extends StatelessWidget {
  final String name;

  const FrameCardWithNameWidget({
    required this.name,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    /// Card section with background image
    /// aspect ratio 342:216 is referred from figma design
    return AspectRatio(
      aspectRatio: EvoDimension.defaultCardRatio,
      child: _buildDefaultCardWidget(name: name),
    );
  }

  Widget _buildDefaultCardWidget({required String name}) {
    return Stack(
      children: <Widget>[
        // Background image
        Positioned.fill(
          child: evoImageProvider.asset(
            EvoImages.frameVirtualCardNameHolder,
            fit: BoxFit.fill,
          ),
        ),
        Positioned(
          left: 1,
          bottom: 1,
          right: 1,
          child: Padding(
            padding: EdgeInsets.only(left: 24.w, bottom: 20.6.h, right: 24.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  EvoStrings.nameOnCard,
                  style: evoTextStyles.regular(
                    TextSize.sm,
                    color: evoColors.greyScale75,
                  ),
                ),
                Text(
                  name,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: evoTextStyles.bold(
                    TextSize.base,
                    color: evoColors.defaultWhite,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
