import 'package:flutter/widgets.dart';

import '../../../../resources/resources.dart';
import '../../../../util/screen_util.dart';
import '../../../../widget/evo_list_tile.dart';

class ActiveVirtualGuideItem extends StatelessWidget {
  final String title;
  final String iconAsset;

  const ActiveVirtualGuideItem(
      {required this.title, required this.iconAsset, super.key});

  @override
  Widget build(BuildContext context) {
    return EvoListTileWidget(
        leading: evoImageProvider.asset(
          iconAsset,
          width: 48.w,
          height: 48.w,
          fit: BoxFit.scaleDown,
        ),
        title: title);
  }
}
