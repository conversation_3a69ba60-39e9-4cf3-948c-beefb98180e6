part of 'active_virtual_card_cubit.dart';


@immutable
abstract class ActiveVirtualCardIntroState implements BlocState {}

class ActiveVirtualCardInitial extends ActiveVirtualCardIntroState {}

class ActiveVirtualCardStateLoading extends ActiveVirtualCardIntroState {}


class ActiveVirtualC<PERSON>Success extends ActiveVirtualCardIntroState {

  ActiveVirtualCardSuccess();
}

class ActiveVirtualCardFailed extends ActiveVirtualCardIntroState {
  final ErrorUIModel errorUIModel;

  ActiveVirtualCardFailed(this.errorUIModel);
}

