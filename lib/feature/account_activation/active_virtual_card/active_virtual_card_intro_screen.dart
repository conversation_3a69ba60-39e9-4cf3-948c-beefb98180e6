import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../resources/resources.dart';
import '../../../util/screen_util.dart';
import '../../../widget/appbar/evo_support_appbar.dart';
import '../../verify_otp/verify_otp_page.dart';
import '../virtual_card_activated/virtual_card_activated_screen.dart';
import 'widgets/active_virtual_guide_item.dart';
import 'widgets/frame_card_with_name_widget.dart';

class ActiveVirtualCardArg extends PageBaseArg {
  final String cardHolderName;

  ActiveVirtualCardArg({required this.cardHolderName});
}

class ActiveVirtualCardIntroScreen extends PageBase {
  static Future<void> pushNamed({required String cardHolderName}) async {
    return navigatorContext?.pushNamed(
      Screen.activeVirtualCardIntroScreen.name,
      extra: ActiveVirtualCardArg(
        cardHolderName: cardHolderName,
      ),
    );
  }

  final String cardHolderName;

  const ActiveVirtualCardIntroScreen({required this.cardHolderName, super.key});

  @override
  State<ActiveVirtualCardIntroScreen> createState() => _ActiveVirtualCardIntroScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.activeVirtualCardIntroScreen.routeName);
}

class _ActiveVirtualCardIntroScreenState extends EvoPageStateBase<ActiveVirtualCardIntroScreen> {
  @override
  Widget getContentWidget(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        appBar: EvoSupportAppbar(
          leading: null,
        ),
        body: SafeArea(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 24.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: <Widget>[
                        ..._buildTitle(),
                        EvoDimension.space24,
                        FrameCardWithNameWidget(name: widget.cardHolderName),
                        EvoDimension.space24,
                        ..._buildActivateGuideLine(),
                        EvoDimension.space24,
                      ],
                    ),
                  ),
                ),
                _buildActiveAccountButton(),
                EvoDimension.space16,
                _buildActiveLaterButton(),
                EvoDimension.space16,
              ],
            ),
          ),
        ),
      ),
    );
  }

  List<Widget> _buildTitle() {
    return <Widget>[
      Text(
        EvoStrings.activeVirtualCardTitle,
        style: evoTextStyles.bold(TextSize.xl2, color: evoColors.screenTitle),
      ),
      EvoDimension.space4,
      Text(
        EvoStrings.activeVirtualCardDesc,
        style: evoTextStyles.regular(TextSize.base),
      ),
    ];
  }

  Widget _buildActiveAccountButton() {
    return CommonButton(
      onPressed: () {
        /// TODO: hoang-nguyen2 removed mock data
        VerifyOtpPage.pushNamed(
            contactInfo: '**********',
            otpResendSecs: 60,
            otpValiditySecs: 120,
            verifyOtpType: VerifyOtpType.activateCard,
            onPopSuccess: (_) {
              VirtualCardActivatedScreen.pushNamed(username: 'Crisostomo');
            });
      },
      isWrapContent: false,
      style: evoButtonStyles.primary(ButtonSize.medium),
      child: const Text(EvoStrings.ctaActiveVirtualCard),
    );
  }

  Widget _buildActiveLaterButton() {
    return CommonButton(
      onPressed: () {
        /// TODO nam-pham-ts active later
      },
      style: evoButtonStyles.tertiary(ButtonSize.medium),
      child: Text(
        EvoStrings.ctaActiveLater,
        style: evoTextStyles.bold(TextSize.base, color: evoColors.primary100),
      ),
    );
  }

  List<Widget> _buildActivateGuideLine() {
    return <Widget>[
      const ActiveVirtualGuideItem(
          iconAsset: EvoImages.icActiveVirtualCardGuideline1,
          title: EvoStrings.activeVirtualCardGuideLine1),
      EvoDimension.space16,
      const ActiveVirtualGuideItem(
          iconAsset: EvoImages.icActiveVirtualCardGuideline2,
          title: EvoStrings.activeVirtualCardGuideLine2),
      EvoDimension.space16,
      const ActiveVirtualGuideItem(
          iconAsset: EvoImages.icActiveVirtualCardGuideline3,
          title: EvoStrings.activeVirtualCardGuideLine3),
    ];
  }
}
