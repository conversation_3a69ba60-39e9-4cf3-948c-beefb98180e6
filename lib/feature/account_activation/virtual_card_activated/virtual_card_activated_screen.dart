import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../resources/resources.dart';
import '../../../util/screen_util.dart';
import '../../../widget/appbar/evo_support_appbar.dart';
import '../../main_screen/main_screen.dart';
import 'widgets/card_activated_intro_widget.dart';

class VirtualCardActivatedArg extends PageBaseArg {
  final String userName;

  VirtualCardActivatedArg({required this.userName});
}

class VirtualCardActivatedScreen extends PageBase {
  static Future<void> pushNamed({required String username}) async {
    return navigatorContext?.pushNamed(
      Screen.virtualCardActivatedScreen.name,
      extra: VirtualCardActivatedArg(
        userName: username,
      ),
    );
  }

  final String username;

  const VirtualCardActivatedScreen({required this.username, super.key});

  @override
  State<VirtualCardActivatedScreen> createState() => _VirtualCardActivatedScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.virtualCardActivatedScreen.routeName);
}

class _VirtualCardActivatedScreenState extends EvoPageStateBase<VirtualCardActivatedScreen> {
  @override
  Widget getContentWidget(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        appBar: EvoSupportAppbar(
          leading: null,
        ),
        body: SafeArea(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 24.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        _buildTitle(),
                        EvoDimension.space24,
                        Center(
                          child: evoImageProvider.asset(
                            EvoImages.virtualCardActivated,
                            fit: BoxFit.fitWidth,
                            width: 171.w,
                          ),
                        ),
                        EvoDimension.space24,
                        Padding(
                          padding: EdgeInsets.symmetric(vertical: 12.h),
                          child: const CardActivatedIntroWidget(),
                        ),
                      ],
                    ),
                  ),
                ),
                _buildGoToHomeButton(),
                EvoDimension.space16,
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Text(
      EvoStrings.virtualCardActivatedTitle.replaceVariableByValue(<String>[widget.username]),
      style: evoTextStyles.bold(TextSize.xl2, color: evoColors.screenTitle),
    );
  }

  Widget _buildGoToHomeButton() {
    return CommonButton(
      onPressed: () {
        /// TODO nam-pham-ts virtual card activated
        MainScreen.goNamed(isLoggedIn: true);
      },
      isWrapContent: false,
      style: evoButtonStyles.primary(ButtonSize.medium),
      child: const Text(EvoStrings.ctaGoToHome),
    );
  }
}
