import 'package:collection/collection.dart';
import 'package:flutter/widgets.dart';

import '../../../../resources/resources.dart';
import '../../../../util/screen_util.dart';
import '../../../../widget/evo_list_tile.dart';

class CardActivatedIntroWidget extends StatelessWidget {
  static const List<String> descriptions = <String>[
    EvoStrings.virtualCardActivatedIntroItem1,
    EvoStrings.virtualCardActivatedIntroItem2,
    EvoStrings.virtualCardActivatedIntroItem3
  ];

  const CardActivatedIntroWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        Text(
          EvoStrings.virtualCardActivatedIntroTitle,
          style: evoTextStyles.bold(TextSize.base, color: evoColors.textNormal),
        ),
        ..._buildListItemInfo()
      ],
    );
  }

  List<Widget> _buildListItemInfo() {
    final List<Widget> result = <Widget>[];

    descriptions.forEachIndexed((int index, String description) {
      result.add(EvoDimension.space16);
      result.add(
        _buildIntroItem(index: (index + 1).toString(), description: description),
      );
    });

    return result;
  }

  Widget _buildIntroItem({required String index, required String description}) {
    return EvoListTileWidget(
      leading: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: evoColors.accent70,
        ),
        width: 48.w,
        height: 48.w,
        alignment: Alignment.center,
        child: Text(
          index,
          style: evoTextStyles.bold(TextSize.base, color: evoColors.accent90),
        ),
      ),
      title: description,
    );
  }
}
