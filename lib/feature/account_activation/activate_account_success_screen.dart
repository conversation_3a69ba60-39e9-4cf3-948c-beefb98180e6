import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../resources/resources.dart';
import '../../util/screen_util.dart';
import '../biometric/activate_biometric/active_biometric_page.dart';
import 'active_virtual_card/active_virtual_card_intro_screen.dart';

class ActivateAccountSuccessScreen extends PageBase {
  const ActivateAccountSuccessScreen({super.key});

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.mobileNumberCheckScreen.routeName);

  @override
  State<ActivateAccountSuccessScreen> createState() => _ActivateAccountSuccessScreenState();

  static void pushNamed() {
    navigatorContext?.pushNamed(Screen.activateAccountSuccessScreen.name);
  }

  static void goNamed() {
    navigatorContext?.goNamed(Screen.activateAccountSuccessScreen.name);
  }
}

class _ActivateAccountSuccessScreenState extends PageStateBase<ActivateAccountSuccessScreen> {
  @override
  Widget getContentWidget(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (_, __) => _proceed(),
      child: ColoredBox(
        color: evoColors.primary,
        child: SafeArea(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 24.w),
            child: Stack(
              alignment: Alignment.center,
              children: <Widget>[
                _buildImageText(),
                Positioned(
                  left: 0,
                  right: 0,
                  bottom: 64.w,
                  child: _buildButton(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildImageText() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        evoImageProvider.asset(
          EvoImages.imgLoginOnNewDevice,
          width: 170.w,
          height: 170.w,
          fit: BoxFit.contain,
        ),
        EvoDimension.space32,
        Text(
          EvoStrings.activateAccountSuccessDesc,
          textAlign: TextAlign.center,
          style: evoTextStyles.bold(TextSize.xl2, color: evoColors.activateCardSuccessText),
        ),
      ],
    );
  }

  Widget _buildButton() {
    return CommonButton(
      isWrapContent: false,
      onPressed: _proceed,
      style: evoButtonStyles.primary(ButtonSize.medium, brightness: Brightness.light),
      child: const Text(EvoStrings.ctaProceed),
    );
  }

  void _proceed() {
    ActiveBiometricScreen.pushNamed(onSuccess: () {
      ActiveVirtualCardIntroScreen.pushNamed(cardHolderName: '');
    });
  }
}
