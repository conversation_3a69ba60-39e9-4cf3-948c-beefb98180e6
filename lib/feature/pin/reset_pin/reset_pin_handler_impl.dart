import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../data/repository/authentication_repo.dart';
import '../../../data/request/reset_pin_request.dart';
import '../../../data/response/reset_pin_entity.dart';
import '../../../util/functions.dart';
import '../mock/mock_pin_use_case.dart';
import 'reset_pin_handler.dart';
import 'reset_pin_ui_handler.dart';

class ResetPinHandlerImpl implements ResetPinHandler {
  final ResetPinUiHandler resetPinUiHandler;
  final AuthenticationRepo authRepo;
  String? entryScreenName;
  void Function(ErrorUIModel? error)? onError;

  ResetPinHandlerImpl({
    required this.resetPinUiHandler,
    required this.authRepo,
  });

  @override
  Future<void> requestResetPin({
    required String? phoneNumber,
    required String? entryScreenName,
    required void Function(ErrorUIModel? error) onError,
  }) async {
    this.onError = onError;
    this.entryScreenName = entryScreenName;

    if (phoneNumber == null || phoneNumber.isEmpty) {
      commonLog(
        'phoneNumber should not be empty',
        methodName: '_onRequestResetPin',
      );
      onError(ErrorUIModel());
      return;
    }

    evoUtilFunction.showHudLoading();

    final ResetPinEntity entity = await authRepo.resetPin(
        request: InitializeResetPinRequest(
          phoneNumber: phoneNumber,
        ),
        mockConfig: MockConfig(
          enable: true,
          fileName: getMockPinFileNameByCase(
            MockPinUseCase.getResetPinInitializeSuccess,
          ),
        ));

    evoUtilFunction.hideHudLoading();

    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      onChallengeSuccess(entity);
      return;
    }

    if (entity.statusCode == CommonHttpClient.LOCKED_RESOURCE ||
        entity.statusCode == CommonHttpClient.LIMIT_EXCEEDED) {
      resetPinUiHandler.handleLimitExceeded(
        error: ErrorUIModel.fromEntity(entity),
        onClickPositive: onPopUntilEntryScreen,
      );
      return;
    }

    onError(ErrorUIModel.fromEntity(entity));
  }

  @visibleForTesting
  @override
  void onChallengeSuccess(ResetPinEntity entity) {
    final ResetPinType challengeType = ResetPinType.fromString(entity.challengeType);

    switch (challengeType) {
      case ResetPinType.verifyOTP:
        resetPinUiHandler.handleVerifyOTP(
          onSuccess: onChallengeSuccess,
          entity: entity,
        );
        return;
      case ResetPinType.changePin:
        resetPinUiHandler.handleChangePIN(
          sessionToken: entity.sessionToken,
          onSuccess: onChallengeSuccess,
        );
        return;
      case ResetPinType.faceAuth:
        resetPinUiHandler.handleFaceAuth(
          entity: entity,
          onSuccess: onChallengeSuccess,
        );
        return;
      case ResetPinType.none:
        resetPinUiHandler.handleResetPinSuccess(
          onSuccess: onPopUntilEntryScreen,
        );
        return;
      case ResetPinType.unknown:
        commonLog(
          'unsupported ResetPinType: $challengeType',
          methodName: 'onChallengeSuccess',
        );
        onError?.call(ErrorUIModel());
        return;
    }
  }

  @visibleForTesting
  void onPopUntilEntryScreen() {
    final String? screenName = entryScreenName;

    if (screenName == null) {
      return;
    }
    navigatorContext?.popUntilNamed(screenName);
  }
}
