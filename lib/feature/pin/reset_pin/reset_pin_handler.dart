import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../data/response/reset_pin_entity.dart';

abstract class ResetPinHandler {
  Future<void> requestResetPin({
    required String? phoneNumber,
    required String? entryScreenName,
    required void Function(ErrorUIModel? error) onError,
  });

  @protected
  @visibleForTesting
  void onChallengeSuccess(ResetPinEntity entity);
}
