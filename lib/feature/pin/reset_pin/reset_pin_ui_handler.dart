import 'dart:ui';

import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../data/response/reset_pin_entity.dart';
import '../../../model/evo_dialog_id.dart';
import '../../../resources/ui_strings.dart';
import '../../../util/dialog_functions.dart';
import '../../ekyc/selfie/selfie_verification_screen.dart';
import '../../verify_otp/cubit/verify_otp_cubit.dart';
import '../../verify_otp/verify_otp_page.dart';
import '../change_pin/create_new_pin_flow.dart';
import '../change_pin/create_new_pin_screen.dart';
import '../new_pin_success_screen.dart';

class ResetPinUiHandler {
  Future<void> handleVerifyOTP({
    required void Function(ResetPinEntity entity) onSuccess,
    required ResetPinEntity entity,
    String? phoneNumber,
  }) async {
    VerifyOtpPage.pushNamed(
      contactInfo: phoneNumber,
      otpResendSecs: entity.otpResendSecs,
      otpValiditySecs: entity.otpValiditySecs,
      sessionToken: entity.sessionToken,
      verifyOtpType: VerifyOtpType.resetPin,
      onPopSuccess: (VerifyOtpState state) {
        if (state is VerifyOtpSuccess) {
          onSuccess(ResetPinEntity(
            challengeType: state.uiModel.challengeType,
            sessionToken: state.uiModel.sessionToken,
            ekycClientSettings: state.uiModel.ekycClientSettings,
          ));
        }
      },
    );
  }

  Future<void> handleLimitExceeded({
    required ErrorUIModel error,
    required VoidCallback onClickPositive,
  }) async {
    await evoDialogFunction.showDialogConfirm(
      isDismissible: false,
      alertType: DialogAlertType.error,
      title: EvoStrings.resetMPINLimitExceededTitle,
      content: error.userMessage,
      textPositive: EvoStrings.ok,
      dialogId: EvoDialogId.defaultErrorDialog,
      onClickPositive: onClickPositive,
    );
  }

  void handleFaceAuth({
    required ResetPinEntity entity,
    required void Function(ResetPinEntity entity) onSuccess,
  }) {
    SelfieVerificationScreen.pushNamed(
      sessionToken: entity.sessionToken,
      flowType: SelfieVerificationFlowType.resetPin,
      onPopSuccess: (BaseEntity entity) {
        if (entity is ResetPinEntity) {
          onSuccess(entity);
        }
      },
    );
  }

  void handleChangePIN({
    required String? sessionToken,
    required void Function(ResetPinEntity entity) onSuccess,
  }) {
    CreateNewPinScreen.pushNamed(
      sessionToken: sessionToken,
      flow: CreateNewPinFlow.resetPin,
      onSuccess: (BaseEntity entity) {
        if (entity is ResetPinEntity) {
          onSuccess(entity);
        }
      },
    );
  }

  void handleResetPinSuccess({
    required VoidCallback onSuccess,
  }) {
    NewPinSuccessScreen.pushReplacementNamed(
      buttonText: EvoStrings.gotIt,
      onNext: onSuccess,
    );
  }
}
