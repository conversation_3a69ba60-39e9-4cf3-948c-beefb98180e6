import 'package:flutter/material.dart';

import '../../../../resources/resources.dart';
import '../../../../util/screen_util.dart';
import '../../../../widget/evo_mpin_code/evo_mpin_code_config.dart';
import '../../../../widget/evo_pin_text_field.dart';

class ConfirmNewPinWidget extends StatelessWidget {
  final TextEditingController inputController;
  final String? errorMessage;
  final void Function(String)? onSubmit;
  final void Function(String)? onChange;

  const ConfirmNewPinWidget({
    required this.inputController,
    required this.onSubmit,
    this.onChange,
    this.errorMessage,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          EvoStrings.confirmResetPinTitle,
          style: evoTextStyles.semibold(TextSize.h3, color: evoColors.grayText),
        ),
        Text(
          EvoStrings.confirmResetPinDesc,
          style: evoTextStyles.regular(TextSize.base, color: evoColors.grayBase),
        ),
        _getPinWidget(),
      ],
    );
  }

  Widget _getPinWidget() {
    return EvoPinTextField(
      isObscureText: true,
      pinLength: EvoMPINCodeConfig.defaultMPINCodeLength,
      textEditingController: inputController,
      onSubmit: onSubmit,
      onChange: onChange,
      errorMessage: errorMessage,
    );
  }
}
