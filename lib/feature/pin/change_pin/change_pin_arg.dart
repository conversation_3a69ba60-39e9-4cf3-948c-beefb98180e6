import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

import 'create_new_pin_flow.dart';

typedef ConfirmNewPinSuccessCallback = void Function(BaseEntity entity);

class CreateNewPinArgs extends PageBaseArg {
  final String? sessionToken;
  final CreateNewPinFlow flow;
  final ConfirmNewPinSuccessCallback onSuccess;

  CreateNewPinArgs({
    required this.onSuccess,
    required this.sessionToken,
    required this.flow,
  });
}

class ConfirmNewPinArgs extends CreateNewPinArgs {
  final String pin;

  ConfirmNewPinArgs({
    required this.pin,
    required super.onSuccess,
    required super.sessionToken,
    required super.flow,
  });
}
