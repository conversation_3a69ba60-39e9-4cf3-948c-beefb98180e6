import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';

import '../../../../../../util/validator/mpin_validator.dart';

part 'create_new_pin_state.dart';

class CreateNewPinCubit extends CommonCubit<CreateNewPinCodeState> {
  final MpinValidator validator;
  
  CreateNewPinCubit({required this.validator}) : super(CreateNewPinInitState());

  void validate(String mpin) {
    final String? errorMessage = validator.validate(mpin);

    if (errorMessage != null) {
      emit(NewPinInvalid(errorMessage));
      return;
    }

    emit(NewPinValid(mpin));
  }

  void initState() {
    emit(CreateNewPinInitState());
  }
}
