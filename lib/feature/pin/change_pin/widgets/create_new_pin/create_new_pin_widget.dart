import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';

import '../../../../../prepare_for_app_initiation.dart';
import '../../../../../util/validator/mpin_validator.dart';
import '../../../../../widget/evo_mpin_code/evo_mpin_code_config.dart';
import '../../../../../widget/evo_pin_text_field.dart';
import 'cubit/create_new_pin_cubit.dart';

/// [CreateNewPinWidget] using in change MPIN flow
/// for validate MPIN before moving to confirm-reset pin
class CreateNewPinWidget extends StatefulWidget {
  final void Function(String text) onSubmit;

  const CreateNewPinWidget({required this.onSubmit, super.key});
  
  @override
  State<CreateNewPinWidget> createState() => _CreateNewPinWidgetState();
}

class _CreateNewPinWidgetState extends State<CreateNewPinWidget> {
  final CreateNewPinCubit _cubit = CreateNewPinCubit(
    validator: getIt.get<MpinValidator>(),
  );
  final TextEditingController _editingController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return BlocProvider<CreateNewPinCubit>(
      create: (BuildContext context) {
        return _cubit;
      },
      child: BlocConsumer<CreateNewPinCubit, CreateNewPinCodeState>(
        listener: (_, CreateNewPinCodeState state) {
          _handleStateChanged(state);
        },
        builder: _buildPinTextField,
      ),
    );
  }

  Widget _buildPinTextField(BuildContext context, CreateNewPinCodeState state) {
    String? errorMessage;

    if (state is NewPinInvalid) {
      errorMessage = state.errorMessage;
    }

    return EvoPinTextField(
      textEditingController: _editingController,
      pinLength: EvoMPINCodeConfig.defaultMPINCodeLength,
      onSubmit: _onSubmit,
      onChange: (String text) => _onChange(text, state),
      isObscureText: true,
      errorMessage: errorMessage,
    );
  }

  void _onChange(String text, CreateNewPinCodeState state) {
    if (text.isEmpty && state is! CreateNewPinInitState) {
      _cubit.initState();
    }
  }

  void _onSubmit(String text) {
    _cubit.validate(text);
  }

  void _handleStateChanged(CreateNewPinCodeState state) {
    if (state is NewPinValid) {
      widget.onSubmit.call(state.mpin);
      return;
    }
  }
}
