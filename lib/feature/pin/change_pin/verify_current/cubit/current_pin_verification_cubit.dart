import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../../data/repository/user_repo.dart';
import '../../../../../data/request/change_pin_request.dart';
import '../../../../../data/response/auth_challenge_type.dart';
import '../../../../../data/response/change_pin_entity.dart';
import '../../../../../prepare_for_app_initiation.dart';
import '../../../mock/mock_pin_use_case.dart';
import '../../../models/change_pin_status.dart';

part 'current_pin_verification_state.dart';

class CurrentPINVerificationCubit extends CommonCubit<CurrentPINVerificationState> {
  CurrentPINVerificationCubit({
    required this.userRepo,
    required this.appState,
  }) : super(VerifyCurrentPinInitState());

  final UserRepo userRepo;
  final AppState appState;

  Future<void> verifyCurrentPin({
    required String pin,
    required String? sessionToken,
  }) async {
    emit(VerifyCurrentPinLoading());

    final ChangePinEntity entity = await userRepo.changePin(
      request: VerifyCurrentPinRequest(
        pin: pin,
        sessionToken: sessionToken,
      ),
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockPinFileNameByCase(MockPinUseCase.getChangePinVerifyCurrentLockedResource),
        statusCode: CommonHttpClient.LIMIT_EXCEEDED,
      ),
    );

    final String? entitySessionToken = entity.sessionToken;

    if (entity.statusCode == CommonHttpClient.SUCCESS && entitySessionToken != null) {
      emit(VerifyCurrentPinSuccess(
        sessionToken: entitySessionToken,
        type: AuthChallengeType.fromString(entity.challengeType),
      ));
      return;
    }

    if (entity.statusCode == CommonHttpClient.BAD_REQUEST) {
      emit(VerifyCurrentPinBadRequest(
        errorMessage: entity.userMessage ?? entity.message,
      ));
      return;
    }

    if (entity.statusCode == CommonHttpClient.LIMIT_EXCEEDED) {
      appState.changePinStatusNotifier.value = ChangePinStatus.locked;
      emit(VerifyCurrentPinLocked(
        errorMessage: entity.userMessage,
      ));
      return;
    }

    emit(VerifyCurrentPinFailure(ErrorUIModel.fromEntity(entity)));
  }
}
