part of 'current_pin_verification_cubit.dart';

sealed class CurrentPINVerificationState extends BlocState {}

class VerifyCurrentPinInitState extends CurrentPINVerificationState {}

class VerifyCurrentPinLoading extends CurrentPINVerificationState {}

class VerifyC<PERSON><PERSON>PinSuc<PERSON> extends CurrentPINVerificationState {
  final String sessionToken;
  final AuthChallengeType? type;

  VerifyCurrentPinSuccess({
    required this.sessionToken,
    this.type = AuthChallengeType.none,
  });
}

class VerifyCurrentPinBadRequest extends CurrentPINVerificationState {
  final String? errorMessage;

  VerifyCurrentPinBadRequest({this.errorMessage});
}

class VerifyCurrentPinLocked extends CurrentPINVerificationState {
  final String? errorMessage;

  VerifyCurrentPinLocked({this.errorMessage});
}

class VerifyCurrentPinFailure extends CurrentPINVerificationState {
  final ErrorUIModel error;

  VerifyCurrentPinFailure(this.error);
}
