import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../resources/resources.dart';
import '../../../util/screen_util.dart';
import '../../../widget/appbar/evo_appbar.dart';
import '../../../widget/banners/info_banner_widget.dart';
import 'change_pin_arg.dart';
import 'confirm_new_pin_screen.dart';
import 'create_new_pin_flow.dart';
import 'widgets/create_new_pin/create_new_pin_widget.dart';

class CreateNewPinScreen extends PageBase {
  final String? sessionToken;
  final CreateNewPinFlow flow;
  final ConfirmNewPinSuccessCallback onSuccess;

  const CreateNewPinScreen({
    required this.sessionToken,
    required this.flow,
    required this.onSuccess,
    super.key,
  });

  @override
  EvoPageStateBase<CreateNewPinScreen> createState() => _CreateNewPinScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(
        name: Screen.createNewPinScreen.name,
      );

  static Future<void> pushReplacement({
    required CreateNewPinFlow flow,
    required ConfirmNewPinSuccessCallback onSuccess,
    String? sessionToken,
  }) async {
    return navigatorContext?.pushReplacementNamed(Screen.createNewPinScreen.name,
        extra: CreateNewPinArgs(
          sessionToken: sessionToken,
          flow: flow,
          onSuccess: onSuccess,
        ));
  }

  static Future<void> pushNamed({
    required CreateNewPinFlow flow,
    required ConfirmNewPinSuccessCallback onSuccess,
    String? sessionToken,
  }) async {
    return navigatorContext?.pushNamed(Screen.createNewPinScreen.name,
        extra: CreateNewPinArgs(
          sessionToken: sessionToken,
          flow: flow,
          onSuccess: onSuccess,
        ));
  }
}

class _CreateNewPinScreenState extends EvoPageStateBase<CreateNewPinScreen> {
  @override
  Widget getContentWidget(BuildContext context) {
    return PopScope(
      canPop: widget.flow.isCreatePin,
      child: Scaffold(
        backgroundColor: evoColors.grayBackground,
        appBar: EvoAppBar(),
        body: SafeArea(
          child: _buildItemBody(),
        ),
      ),
    );
  }

  Widget _buildItemBody() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            _title,
            style: evoTextStyles.semibold(TextSize.h3, color: evoColors.grayText),
          ),
          EvoDimension.space4,
          Text(
            _description,
            style: evoTextStyles.regular(TextSize.base, color: evoColors.grayBase),
          ),
          CreateNewPinWidget(
            onSubmit: (String pin) {
              ConfirmNewPinScreen.pushNamed(
                pin: pin,
                sessionToken: widget.sessionToken,
                flow: widget.flow,
                onSuccess: widget.onSuccess,
              );
            },
          ),
          _mPINCreationGuidelineBanner()
        ],
      ),
    );
  }

  Widget _mPINCreationGuidelineBanner() {
    return InfoBannerWidget(
      info: <String>[
        if (!widget.flow.isCreatePin) EvoStrings.infoBannerNotSamePrevious,
        EvoStrings.infoBannerNotIncreasing,
        EvoStrings.infoBannerNotDecreasing,
        EvoStrings.infoBannerNotSameDigits,
        EvoStrings.infoBannerMustBe4Digits,
      ],
    );
  }

  String get _title {
    return widget.flow.isCreatePin
        ? EvoStrings.activateAccountCreatePinTitle
        : EvoStrings.createNewMPINTitle;
  }

  String get _description {
    return widget.flow.isCreatePin
        ? EvoStrings.activateAccountCreatePinDesc
        : EvoStrings.createNewMPINDesc;
  }
}
