enum CreateNewPinFlow {
  /// Used when a user wants to reset PIN,
  /// for confirm reset called [AuthenticationRepo.resetPin] with [ResetPinConfirmNewPinRequest]
  resetPin,

  /// Used when a user wants to change PIN,
  /// for confirm change called [UserRepo.changePin] with [ConfirmNewPinRequest]
  changePin,

  /// Used when a user create PIN for first time
  /// for confirm create called [AuthenticationRepo.activateAccount]
  createPin;

  bool get isCreatePin => this == CreateNewPinFlow.createPin;
}
