enum MockPinUseCase {
  getVerifyPinSuccess('verify_pin_success.json'),
  getVerifyPinLimitExceeded('verify_pin_limit_exceeded.json'),
  getVerifyPinInvalidCredential('verify_pin_invalid_credential.json'),

  /// change mpin
  getChangePinVerifyCurrentSuccess('change_pin_verify_current_success.json'),
  getChangePinVerifyCurrentSuccessNullSessionToken(
      'change_pin_verify_current_success_null_token.json'),
  getChangePinVerifyCurrentLockedResource('change_pin_verify_current_locked_resource.json'),
  getConfirmResetPinSuccess('confirm_reset_pin_success.json'),
  getConfirmResetPinFailure('confirm_reset_pin_failure.json'),

  /// reset mpin
  getResetPinInitializeSuccess('reset_pin_initialize_success.json'),
  getResetPinVerifyOTPSuccess('reset_pin_verify_otp_success.json'),
  getResetPinFaceAuthSuccess('reset_pin_face_auth_success.json'),
  getResetPinSuccess('reset_pin_success.json'),
  getResetPinFaceAuthChallenge('reset_pin_face_auth_challenge.json');

  final String value;

  const MockPinUseCase(this.value);
}

String getMockPinFileNameByCase(MockPinUseCase mockCase) {
  return mockCase.value;
}
