import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../data/repository/authentication_repo.dart';
import '../../../../data/repository/user_repo.dart';
import '../../../../data/request/activate_account_request.dart';
import '../../../../data/request/change_pin_request.dart';
import '../../../../data/request/reset_pin_request.dart';
import '../../../../data/response/account_activation_entity.dart';
import '../../../../data/response/change_pin_entity.dart';
import '../../../../data/response/reset_pin_entity.dart';
import '../../../account_activation/mock/mock_account_activation_use_case.dart';
import '../../change_pin/create_new_pin_flow.dart';
import '../../mock/mock_pin_use_case.dart';

part 'confirm_new_pin_state.dart';

class ConfirmNewPinCubit extends CommonCubit<ConfirmNewPinState> {
  final UserRepo userRepo;
  final AuthenticationRepo authRepo;

  ConfirmNewPinCubit({
    required this.userRepo,
    required this.authRepo,
  }) : super(ConfirmInitialState());

  Future<void> onConfirm({
    required String pin,
    required String confirmPin,
    required CreateNewPinFlow flow,
    String? sessionToken,
  }) async {
    if (pin != confirmPin) {
      emit(PinUnmatchedState());
      return;
    }

    emit(ConfirmLoadingState());
    late final BaseEntity entity;
    switch (flow) {
      case CreateNewPinFlow.resetPin:
        entity = await _resetPin(
          pin: pin,
          sessionToken: sessionToken,
        );
        break;
      case CreateNewPinFlow.changePin:
        entity = await _changePin(
          pin: pin,
          sessionToken: sessionToken,
        );
        break;
      case CreateNewPinFlow.createPin:
        entity = await _createPin(
          pin: pin,
        );
    }

    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      emit(ConfirmNewPinSuccess(entity));
      return;
    }

    if (entity.statusCode == CommonHttpClient.INVALID_TOKEN) {
      emit(ConfirmNewPinSessionExpired());
      return;
    }

    if (entity.statusCode == CommonHttpClient.BAD_REQUEST) {
      emit(ConfirmNewPinBadRequest(error: ErrorUIModel.fromEntity(entity)));
      return;
    }

    emit(ConfirmNewPinFailure(error: ErrorUIModel.fromEntity(entity)));
  }

  void resetState() {
    emit(ConfirmInitialState());
  }

  Future<ChangePinEntity> _changePin({
    required String pin,
    String? sessionToken,
  }) {
    return userRepo.changePin(
        request: ConfirmNewPinRequest(
          pin: pin,
          sessionToken: sessionToken,
        ),
        mockConfig: MockConfig(
          enable: false,
          statusCode: CommonHttpClient.BAD_REQUEST,
          fileName: getMockPinFileNameByCase(
            MockPinUseCase.getChangePinVerifyCurrentSuccess,
          ),
        ));
  }

  Future<ResetPinEntity> _resetPin({
    required String pin,
    String? sessionToken,
  }) {
    return authRepo.resetPin(
        request: ResetPinConfirmNewPinRequest(
          newPin: pin,
          sessionToken: sessionToken,
        ),
        mockConfig: MockConfig(
          enable: true,
          fileName: getMockPinFileNameByCase(
            MockPinUseCase.getResetPinSuccess,
          ),
        ));
  }

  Future<AccountActivationEntity> _createPin({
    required String pin,
  }) {
    return authRepo.activateAccount(
        request: ActivateAccountVerifyPinRequest(),
        mockConfig: MockConfig(
          enable: true,
          fileName: getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.getVerifyEmailChallengeType,
          ),
        ));
  }
}
