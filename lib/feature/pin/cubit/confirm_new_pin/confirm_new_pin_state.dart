part of 'confirm_new_pin_cubit.dart';

sealed class ConfirmNewPinState extends BlocState {}

class ConfirmInitialState extends ConfirmNewPinState {}

class ConfirmLoadingState extends ConfirmNewPinState {}

class PinUnmatchedState extends ConfirmNewPinState {}

class ConfirmNewPinSessionExpired extends ConfirmNewPinState {}

class ConfirmNewPinBadRequest extends ConfirmNewPinState {
  final ErrorUIModel error;

  ConfirmNewPinBadRequest({required this.error});
}

class ConfirmNewPinFailure extends ConfirmNewPinState {
  final ErrorUIModel error;

  ConfirmNewPinFailure({required this.error});
}

class ConfirmNewPinSuccess extends ConfirmNewPinState {
  final BaseEntity entity;

  ConfirmNewPinSuccess(this.entity);
}
