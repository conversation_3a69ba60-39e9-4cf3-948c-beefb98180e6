import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../base/evo_page_state_base.dart';
import '../../resources/resources.dart';

class NewPinSuccessScreenArg extends PageBaseArg {
  NewPinSuccessScreenArg({
    required this.buttonText,
    required this.onNext,
  });

  final VoidCallback onNext;
  final String buttonText;
}

class NewPinSuccessScreen extends PageBase {
  const NewPinSuccessScreen({
    required this.buttonText,
    required this.onNext,
    super.key,
  });

  final VoidCallback onNext;
  final String buttonText;

  @override
  EvoPageStateBase<NewPinSuccessScreen> createState() => _NewPinSuccessScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.newPinSuccessScreen.name);

  static Future<void> pushReplacementNamed({
    required VoidCallback onNext,
    required String buttonText,
  }) async {
    return navigatorContext?.pushReplacementNamed(Screen.newPinSuccessScreen.name,
        extra: NewPinSuccessScreenArg(
          buttonText: buttonText,
          onNext: onNext,
        ));
  }
}

class _NewPinSuccessScreenState extends EvoPageStateBase<NewPinSuccessScreen> {
  @override
  Widget getContentWidget(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
          body: SafeArea(
        top: false,
        child: SizedBox.expand(
          child: Container(
            decoration: BoxDecoration(
              color: evoColors.primary,
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
              child: Column(
                children: <Widget>[
                  Expanded(
                      child: evoImageProvider.asset(
                    EvoImages.icNewPinSuccess,
                    fit: BoxFit.contain,
                  )),
                  CommonButton(
                    isWrapContent: false,
                    onPressed: () {
                      widget.onNext();
                    },
                    style: evoButtonStyles.primary(
                      ButtonSize.large,
                      brightness: Brightness.light,
                    ),
                    child: Text(widget.buttonText),
                  )
                ],
              ),
            ),
          ),
        ),
      )),
    );
  }
}
