import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../../resources/resources.dart';

class LoginByBiometricWidget extends StatelessWidget {
  final VoidCallback? onClick;

  const LoginByBiometricWidget({super.key, this.onClick});

  @override
  Widget build(BuildContext context) {
    return CommonButton(
      onPressed: onClick,
      style: evoButtonStyles.tertiary(ButtonSize.medium),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          evoImageProvider.asset(
            EvoImages.icBiometrics,
            width: 24,
            height: 24,
          ),
          const SizedBox(width: 6),
          Text(
            EvoStrings.loginScreenLoginWithBiometric,
            style: evoTextStyles.h200(color: evoColors.primary100),
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
