import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../data/repository/authentication_repo.dart';
import '../../../data/repository/user_repo.dart';
import '../../../data/response/sign_in_entity.dart';
import '../../../model/evo_dialog_id.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/dialog_functions.dart';
import '../../../util/functions.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../widget/appbar/evo_appbar.dart';
import '../../../widget/evo_mpin_code/evo_mpin_code_widget.dart';
import '../../biometric/utils/biometrics_authenticate.dart';
import '../../main_screen/main_screen.dart';
import '../../pin/reset_pin/reset_pin_handler.dart';
import '../../profile/cubit/user_profile_cubit.dart';
import '../../welcome/welcome_screen.dart';
import '../utils/login_old_device_utils.dart';
import '../verify_username/verify_username_screen.dart';
import 'biometric/biometric_cubit.dart';
import 'login_on_old_device_cubit.dart';
import 'pincode/pin_code_cubit.dart';
import 'widgets/login_by_biometric_widget.dart';

class LoginOnOldDeviceScreen extends PageBase {
  const LoginOnOldDeviceScreen({super.key});

  static Future<void> goNamed() async {
    return navigatorContext?.goNamed(
      Screen.loginOnOldDeviceScreen.name,
    );
  }

  static Future<void> pushNamed() async {
    return navigatorContext?.pushNamed(
      Screen.loginOnOldDeviceScreen.name,
    );
  }

  @override
  State<LoginOnOldDeviceScreen> createState() => _LoginScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.loginOnOldDeviceScreen.routeName);
}

class _LoginScreenState extends EvoPageStateBase<LoginOnOldDeviceScreen> {
  late final LoginOnOldDeviceCubit _loginCubit = context.read<LoginOnOldDeviceCubit?>() ??
      LoginOnOldDeviceCubit(
        authenticationRepo: getIt.get<AuthenticationRepo>(),
        loginOldDeviceUtils: getIt.get<LoginOldDeviceUtils>(),
        localStorageHelper: getIt.get<EvoLocalStorageHelper>(),
      );
  late final BiometricCubit _biometricCubit = context.read<BiometricCubit?>() ??
      BiometricCubit(
        loginOldDeviceUtils: getIt.get<LoginOldDeviceUtils>(),
        bioAuth: getIt.get<BiometricsAuthenticate>(),
      );
  late final PinCodeCubit _pinCodeCubit = context.read<PinCodeCubit?>() ?? PinCodeCubit();
  late final UserProfileCubit _userProfileCubit = context.read<UserProfileCubit?>() ??
      UserProfileCubit(
        userRepo: getIt.get<UserRepo>(),
        appState: appState,
        localStorageHelper: getIt.get<EvoLocalStorageHelper>(),
      );
  final ResetPinHandler _resetPinHandler = getIt.get<ResetPinHandler>();
  String? phoneNumber;
  final TextEditingController textEditingController = TextEditingController();
  final FocusNode focusNode = FocusNode();

  /// This variable determine when this page is launched first time & biometric authentication is available,
  /// Biometric Authentication is triggered automatically.
  bool isAutoBiometricAuthAtFirstLaunch = true;

  String? _pinCode;

  @override
  void initState() {
    super.initState();
    _initSetup();
    appState.biometricStatusChangeNotifier.addListener(_initSetup);
  }

  @override
  void dispose() {
    appState.biometricStatusChangeNotifier.removeListener(_initSetup);
    super.dispose();
  }

  void _initSetup() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _biometricCubit.initialize();
      _userProfileCubit.getLocalUserInfo();
    });
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return PopScope(
      /// Block user from pop screen back to MainScreen
      canPop: false,
      child: Scaffold(
        appBar: _buildAppBar(),
        backgroundColor: evoColors.background,
        body: SafeArea(
          child: MultiBlocProvider(
            providers: <BlocProvider<dynamic>>[
              BlocProvider<LoginOnOldDeviceCubit>(
                create: (_) => _loginCubit,
              ),
              BlocProvider<BiometricCubit>(
                create: (_) => _biometricCubit,
              ),
              BlocProvider<PinCodeCubit>(
                create: (_) => _pinCodeCubit,
              ),
              BlocProvider<UserProfileCubit>(
                create: (_) => _userProfileCubit,
              )
            ],
            child: MultiBlocListener(
              listeners: <BlocListener<dynamic, dynamic>>[
                BlocListener<LoginOnOldDeviceCubit, LoginOnOldDeviceState>(
                  listener: (_, LoginOnOldDeviceState state) {
                    _listenLoginState(state);
                  },
                ),
                BlocListener<BiometricCubit, BiometricState>(
                  listener: (_, BiometricState state) {
                    _listenBiometricState(state);
                  },
                ),
                BlocListener<UserProfileCubit, UserProfileState>(
                    listener: (_, UserProfileState state) {
                  _listenUserProfileState(state);
                }),
              ],
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Column(
                  children: <Widget>[
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            _buildLoginTitleWidget(),
                            const SizedBox(height: 4),
                            _buildMPINCodeWidget(),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildLoginCTA(),
                    _buildBiometricCTA(),
                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  EvoAppBar _buildAppBar() {
    return EvoAppBar(
      leading: null,
      actions: <Widget>[
        _buildSwitchAccountButton(),
      ],
    );
  }

  Widget _buildSwitchAccountButton() {
    return Padding(
      padding: const EdgeInsets.only(right: 24),
      child: CommonButton(
        onPressed: _loginWithAnotherAccount,
        style: evoButtonStyles.utility(
          ButtonSize.small,
          padding: const EdgeInsets.symmetric(
            vertical: 4,
            horizontal: 15,
          ),
        ),
        child: Row(
          children: <Widget>[
            imageProvider.asset(
              EvoImages.icSwitchAccount,
              width: 24,
              height: 24,
              fit: BoxFit.cover,
            ),
            const SizedBox(width: 8),
            const Text(EvoStrings.loginOnOldDeviceSwitchAccountCTA),
          ],
        ),
      ),
    );
  }

  Widget _buildLoginCTA() {
    return CommonButton(
      onPressed: () {
        _loginCubit.loginByPinCode(_pinCode);
      },
      style: evoButtonStyles.primary(ButtonSize.medium),
      isWrapContent: false,
      child: const Text(EvoStrings.login),
    );
  }

  bool _shouldShowBiometricLoginButton(BiometricState state) {
    switch (state) {
      case BiometricAuthenticationAvailable():
      case BiometricAuthUserDismiss():
      case BiometricAuthSuccess():
        return true;
      default:
        return false;
    }
  }

  Widget _buildBiometricCTA() {
    return BlocBuilder<BiometricCubit, BiometricState>(
      builder: (_, BiometricState state) {
        if (_shouldShowBiometricLoginButton(state)) {
          return Column(
            children: <Widget>[
              const SizedBox(height: 16),
              LoginByBiometricWidget(onClick: () {
                _biometricCubit.authenticate();
              }),
            ],
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildLoginTitleWidget() {
    return BlocBuilder<UserProfileCubit, UserProfileState>(
        builder: (BuildContext context, UserProfileState state) {
      final String title = generateWelcomeTitle(state);

      return Text(
        title,
        style: evoTextStyles.bold(
          TextSize.xl2,
          color: evoColors.screenTitle,
        ),
      );
    });
  }

  String generateWelcomeTitle(UserProfileState state) {
    String title = EvoStrings.loginOnOldDeviceTitle;

    if (state is UserProfileLoadedSuccess && state.user?.fullName != null) {
      title = '$title, ${state.user?.fullName}!';
    } else {
      title = '$title!';
    }

    return title;
  }

  Widget _buildMPINCodeWidget() {
    return BlocBuilder<PinCodeCubit, PinCodeState>(
      builder: (_, PinCodeState state) {
        String? errorMessage;
        if (state is PinCodeErrorState) {
          errorMessage = state.error ?? CommonStrings.otherGenericErrorMessage;
        }

        if (state is PinCodeClearErrorState) {
          errorMessage = null;
        }

        return EvoMPINCodeWidget(
          title: EvoStrings.enterMPIN,
          textEditingController: textEditingController,
          errorMessage: errorMessage,
          onSubmit: (String? pinCode) {
            _pinCode = pinCode;
            _loginCubit.loginByPinCode(_pinCode);
          },
          onChange: (_) {
            _pinCodeCubit.clearPinCodeError();
          },
          onResetPin: _onResetPin,
          focusNode: focusNode,
        );
      },
    );
  }

  Future<void> _listenLoginState(LoginOnOldDeviceState state) async {
    if (state is LoginOnOldDeviceLoading) {
      evoUtilFunction.showHudLoading();
      return;
    }

    evoUtilFunction.hideHudLoading();

    if (state is LoginOnOldDeviceSuccess) {
      _handleLoginSuccess();
    } else if (state is LoginOnOldDeviceError) {
      _handleLoginError(state);
    }
  }

  void _handleLoginError(LoginOnOldDeviceError state) {
    if (state.error.verdict == SignInEntity.verdictLimitExceeded) {
      _showLimitedExceededPopup(state.error);
      return;
    }

    switch (state.loginType) {
      case TypeLogin.verifyPin:
        _handleLoginByPinCodeError(state);
        break;
      case TypeLogin.biometricToken:
      default:
        handleEvoApiError(state.error);
        break;
    }
  }

  void _handleLoginByPinCodeError(LoginOnOldDeviceError state) {
    final String? errorUserMsg = state.error.userMessage;
    final bool shouldHandleDefaultError = state.error.verdict == null || errorUserMsg == null;

    if (shouldHandleDefaultError) {
      handleEvoApiError(state.error);
      return;
    }

    _pinCodeCubit.setPinCodeError(errorUserMsg);
  }

  Future<void> _listenBiometricState(BiometricState state) async {
    if (state is BiometricAuthenticationAvailable && isAutoBiometricAuthAtFirstLaunch) {
      _biometricCubit.authenticate(shouldEmitUnavailableState: true);
      isAutoBiometricAuthAtFirstLaunch = false;
      return;
    }

    if (state is BiometricAuthSuccess) {
      _loginCubit.loginByBiometric();
      return;
    }

    if (state is BiometricAuthUserDismiss) {
      _openPinCodeKeyboard();
      return;
    }
  }

  Future<void> _loginWithAnotherAccount({String? lastPhoneNumberLogged}) async {
    evoDialogFunction.showDialogConfirm(
      title: EvoStrings.switchAccountTitle,
      textPositive: EvoStrings.ctaProceed,
      textNegative: EvoStrings.ctaCancel,
      dialogId: EvoDialogId.confirmSwitchAccountDialog,
      onClickPositive: () async {
        ///dismiss popup
        navigatorContext?.pop();

        await _loginCubit.loginWithNewAccount();
        VerifyUsernameScreen.pushReplacementNamed();
      },
    );
  }

  void _openPinCodeKeyboard() {
    commonUtilFunction.delayAndRequestFocus(focusNode);
  }

  @override
  Future<void> handleEvoApiError(ErrorUIModel? errorUIModel) async {
    /// If error code is  [CommonHttpClient.INVALID_TOKEN] we will re-initial this page.
    if (errorUIModel?.statusCode == CommonHttpClient.INVALID_TOKEN) {
      _initSetup();
    }
    super.handleEvoApiError(errorUIModel);
  }

  @override
  bool hasListenAuthorizationSessionExpired() => false;

  void _showLimitedExceededPopup(ErrorUIModel error) {
    evoDialogFunction.showDialogConfirm(
      alertType: DialogAlertType.error,
      title: EvoStrings.loginLimitedExceededTitle,
      content: error.userMessage ?? CommonStrings.otherGenericErrorMessage,
      textPositive: EvoStrings.backToHomePage,
      onClickPositive: () {
        WelcomeScreen.goNamed();
      },
      dialogId: EvoDialogId.loginLimitedExceededDialog,
      isDismissible: false,
    );
  }

  void _handleLoginSuccess() {
    updateUserLoginStatus(true);
    MainScreen.goNamed(isLoggedIn: true);
  }

  void _listenUserProfileState(UserProfileState state) {
    if (state is UserProfileLoadedSuccess) {
      phoneNumber = state.user?.phoneNumber;
    }
  }

  void _onResetPin() {
    _resetPinHandler.requestResetPin(
      phoneNumber: phoneNumber,
      onError: handleEvoApiError,
      entryScreenName: widget.routeSettings.name,
    );
  }
}
