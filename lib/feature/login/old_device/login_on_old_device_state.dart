part of 'login_on_old_device_cubit.dart';

sealed class LoginOnOldDeviceState extends BlocState {}

class LoginOnOldDeviceInitial extends LoginOnOldDeviceState {}

class LoginOnOldDeviceLoading extends LoginOnOldDeviceState {}

class LoginOnOldDeviceError extends LoginOnOldDeviceState {
  final ErrorUIModel error;
  final TypeLogin loginType;
  final String? phoneNumber;

  LoginOnOldDeviceError(
    this.error,
    this.loginType,
    this.phoneNumber,
  );
}

class LoginOnOldDeviceSuccess extends LoginOnOldDeviceState {
  final TypeLogin type;
  final AuthChallengeType? challengeType;

  LoginOnOldDeviceSuccess({
    required this.type,
    required this.challengeType,
  });
}
