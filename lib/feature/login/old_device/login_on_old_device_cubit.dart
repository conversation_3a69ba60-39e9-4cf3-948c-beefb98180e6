import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/feature/onesignal/onesignal.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../data/repository/authentication_repo.dart';
import '../../../data/response/auth_challenge_type.dart';
import '../../../data/response/sign_in_entity.dart';
import '../../../util/functions.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../utils/login_old_device_utils.dart';
import 'mock_file/mock_login_on_old_device_file_name.dart';

part 'login_on_old_device_state.dart';

class LoginOnOldDeviceCubit extends CommonCubit<LoginOnOldDeviceState> {
  final AuthenticationRepo authenticationRepo;
  final LoginOldDeviceUtils loginOldDeviceUtils;
  final EvoLocalStorageHelper localStorageHelper;

  LoginOnOldDeviceCubit({
    required this.authenticationRepo,
    required this.loginOldDeviceUtils,
    required this.localStorageHelper,
  }) : super(LoginOnOldDeviceInitial());

  Future<void> loginByPinCode(String? pinCode) async {
    emit(LoginOnOldDeviceLoading());

    final String? phoneNumber = await getUserPhoneNumberFromLocal();
    const TypeLogin type = TypeLogin.verifyPin;
    final SignInEntity response = await authenticationRepo.signIn(
      type,
      pin: pinCode,
      phoneNumber: phoneNumber,
      mockConfig: MockConfig(
        enable: true,
        fileName: getLoginOnOldDeviceMockFileName(LoginOnOldDeviceUseCase.success),
      ),
    );

    if (response.statusCode == CommonHttpClient.SUCCESS) {
      emit(LoginOnOldDeviceSuccess(
        type: type,
        challengeType: AuthChallengeType.fromString(response.challengeType),
      ));
    } else {
      emit(LoginOnOldDeviceError(
        ErrorUIModel.fromEntity(response),
        type,
        phoneNumber,
      ));
    }
  }

  Future<void> loginByBiometric() async {
    emit(LoginOnOldDeviceLoading());

    final String? biometricToken = await localStorageHelper.getBiometricToken();
    const TypeLogin type = TypeLogin.biometricToken;
    final SignInEntity response = await authenticationRepo.signIn(
      type,
      biometricToken: biometricToken,
      mockConfig: MockConfig(
        enable: false,
        fileName: getLoginOnOldDeviceMockFileName(LoginOnOldDeviceUseCase.invalidParameters),
        statusCode: CommonHttpClient.BAD_REQUEST,
      ),
    );

    if (response.statusCode == CommonHttpClient.SUCCESS) {
      emit(LoginOnOldDeviceSuccess(
        type: type,
        challengeType: AuthChallengeType.fromString(response.challengeType),
      ));
    } else {
      final String? phoneNumber = await getUserPhoneNumberFromLocal();
      emit(LoginOnOldDeviceError(
        ErrorUIModel.fromEntity(response),
        type,
        phoneNumber,
      ));
    }
  }

  /// `oneSignal` for unit test
  Future<void> loginWithNewAccount({OneSignal? oneSignal}) {
    return evoUtilFunction.clearAllUserData(oneSignal: oneSignal);
  }

  @visibleForTesting
  Future<String?> getUserPhoneNumberFromLocal() async {
    return await localStorageHelper.getUserPhoneNumber();
  }
}
