import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';

import '../../../../../resources/ui_strings.dart';
import '../../../biometric/utils/bio_auth_result.dart';
import '../../../biometric/utils/biometrics_authenticate.dart';
import '../../utils/login_old_device_utils.dart';

part 'biometric_state.dart';

class BiometricCubit extends CommonCubit<BiometricState> {
  BiometricCubit({
    required this.loginOldDeviceUtils,
    required this.bioAuth,
  }) : super(BiometricInitial());

  final LoginOldDeviceUtils loginOldDeviceUtils;
  final BiometricsAuthenticate bioAuth;

  /// check if user enabled Biometric Authentication feature.
  Future<void> initialize() async {
    final bool canLoginBiometric = await loginOldDeviceUtils.checkCanLoginByBiometric();

    if (!canLoginBiometric) {
      emit(BiometricAuthenticationUnavailable());
      return;
    }
    emit(BiometricAuthenticationAvailable());
  }

  /// Attempts to authenticate using biometrics and emits the appropriate state.
  ///
  /// [shouldEmitUnavailableState] determines whether to emit the
  /// [BiometricAuthenticationUnavailable] state when the biometric system is
  /// locked or not available. This can be used to improve UI/UX by informing
  /// the user about the unavailability of biometric authentication.
  Future<void> authenticate({bool shouldEmitUnavailableState = false}) async {
    final BioAuthResult result = await bioAuth.authenticate(
      localizedReason: EvoStrings.localizedReasonForUsingBiometrics,
    );

    if (result.isAuthSuccess == true) {
      emit(BiometricAuthSuccess());
      return;
    }

    if (shouldEmitUnavailableState && isBiometricsIsLocked(result.error)) {
      emit(BiometricAuthenticationUnavailable());
      return;
    }

    if (result.error == BioAuthError.userDismiss) {
      emit(BiometricAuthUserDismiss());
      return;
    }

    emit(BiometricAuthFail(bioAuthError: result.error));
  }

  /// Based on BioAuthError, it determine if Biometric Authentication of OS is locked.
  /// The `Locked` state represents user actions try to use unregistered face | fingerprint to do biometric authentication.
  /// After a few times try failed, OS will locked this feature.
  /// * Exception case on IOS devices:  when this feature is locked, it return `notEnrolled`.
  @visibleForTesting
  bool isBiometricsIsLocked(BioAuthError? error) {
    return error == BioAuthError.androidLockedOut ||
        error == BioAuthError.notEnrolled ||
        error == BioAuthError.permanentlyLockedOut;
  }
}
