part of 'biometric_cubit.dart';

sealed class BiometricState extends BlocState {}

class BiometricInitial extends BiometricState {}

class BiometricAuthSuccess extends BiometricState {}

class BiometricAuthFail extends BiometricState {
  final BioAuthError? bioAuthError;

  BiometricAuthFail({
    this.bioAuthError,
  });
}

class BiometricAuthUserDismiss extends BiometricState {}

class BiometricAuthenticationUnavailable extends BiometricState {}

class BiometricAuthenticationAvailable extends BiometricState {}
