import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../data/repository/authentication_repo.dart';
import '../../../../data/response/sign_in_entity.dart';
import '../../../../util/validator/evo_validator.dart';
import '../../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../pin/mock/mock_pin_use_case.dart';
part 're_auth_input_pin_state.dart';

class ReAuthInputPinCubit extends CommonCubit<ReAuthPinState> {
  final AuthenticationRepo authenticationRepo;
  final EvoLocalStorageHelper storageHelper;

  ReAuthInputPinCubit({
    required this.authenticationRepo,
    required this.storageHelper,
  }) : super(PinNotFullState());

  void validateLengthPin(String value) {
    final bool result = evoValidator.validateMaxLengthPin(value);
    emit(result ? PinFullState() : PinNotFullState());
  }

  Future<void> verifyPin(String phoneNumber, String? pinCode, String? sessionToken) async {
    emit(PinLoadingState());

    final SignInEntity entity = await authenticationRepo.signIn(
      TypeLogin.verifyPin,
      phoneNumber: phoneNumber,
      pin: pinCode,
      sessionToken: sessionToken,
      mockConfig: MockConfig(
        enable: true,
        fileName: getMockPinFileNameByCase(MockPinUseCase.getVerifyPinSuccess),
      ),
    );
    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      storageHelper.setUserPhoneNumber(phoneNumber);
      emit(VerifyPinSuccessState(pinCode));
    } else {
      handleVerifyPinError(entity);
    }
  }

  @visibleForTesting
  void handleVerifyPinError(SignInEntity entity) {
    final ErrorUIModel errorUIModel = ErrorUIModel.fromEntity(entity);

    switch (entity.statusCode) {
      case CommonHttpClient.LIMIT_EXCEEDED:
        emit(PinLimitExceeded(entity.userMessage));
        return;
      case CommonHttpClient.INVALID_TOKEN:
        emit(PinSessionExpired());
        return;
      case CommonHttpClient.BAD_REQUEST:
        emit(VerifyPinBadRequest(entity.userMessage));
        return;
      default:
        emit(PinErrorState(
          errorUIModel: errorUIModel,
        ));
    }
  }
}
