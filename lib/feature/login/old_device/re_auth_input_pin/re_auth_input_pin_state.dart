part of 're_auth_input_pin_cubit.dart';

sealed class <PERSON>A<PERSON><PERSON>inState extends BlocState {}

class PinNotFullState extends ReAuthPinState {}

class PinFullState extends ReAuthPinState {}

class PinLoadingState extends ReAuthPinState {}

class PinValidatedState extends ReAuthPinState {}

class VerifyPinSuccessState extends ReAuthPinState {
  final String? pinCode;

  VerifyPinSuccessState(this.pinCode);
}

class PinErrorState extends ReAuthPinState {
  final ErrorUIModel errorUIModel;

  PinErrorState({
    required this.errorUIModel,
  });
}

class PinSessionExpired extends ReAuthPinState {}

class ResetPinSuccessState extends ReAuthPinState {}

class PinLimitExceeded extends ReAuthPinState {
  String? errorMessage;

  PinLimitExceeded(this.errorMessage);
}

class VerifyPinBadRequest extends ReAuthPinState {
  String? errorMessage;

  VerifyPinBadRequest(this.errorMessage);
}
