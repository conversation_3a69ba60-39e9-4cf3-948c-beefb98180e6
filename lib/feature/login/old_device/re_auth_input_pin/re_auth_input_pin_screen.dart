import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../base/evo_page_state_base.dart';
import '../../../../data/repository/authentication_repo.dart';
import '../../../../model/evo_dialog_id.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/resources.dart';
import '../../../../util/dialog_functions.dart';
import '../../../../util/functions.dart';
import '../../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../../widget/appbar/evo_support_appbar.dart';
import '../../../../widget/evo_mpin_code/evo_mpin_code_widget.dart';
import '../../../biometric/activate_biometric/active_biometric_page.dart';
import '../../../main_screen/main_screen.dart';
import '../../../pin/reset_pin/reset_pin_handler.dart';
import '../../../welcome/welcome_screen.dart';
import 're_auth_input_pin_cubit.dart';

class ReAuthInputPinArg extends PageBaseArg {
  final String phoneNumber;
  final String? sessionToken;

  ReAuthInputPinArg({required this.phoneNumber, this.sessionToken});
}

class ReAuthInputPinScreen extends PageBase {
  static Future<void> pushNamed({required String phoneNumber, String? sessionToken}) async {
    return navigatorContext?.pushNamed(Screen.reAuthInputPinScreen.name,
        extra: ReAuthInputPinArg(phoneNumber: phoneNumber, sessionToken: sessionToken));
  }

  final String phoneNumber;
  final String? sessionToken;

  const ReAuthInputPinScreen({required this.phoneNumber, super.key, this.sessionToken});

  @override
  EvoPageStateBase<ReAuthInputPinScreen> createState() => ReAuthInputPinScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.reAuthInputPinScreen.routeName);
}

class ReAuthInputPinScreenState extends EvoPageStateBase<ReAuthInputPinScreen> {
  late ReAuthInputPinCubit _cubit;
  TextEditingController textCreateController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final ResetPinHandler _resetPinHandler = getIt.get<ResetPinHandler>();

  @override
  void initState() {
    super.initState();
    _cubit = context.read<ReAuthInputPinCubit?>() ??
        ReAuthInputPinCubit(
          authenticationRepo: getIt.get<AuthenticationRepo>(),
          storageHelper: getIt.get<EvoLocalStorageHelper>(),
        );
    commonUtilFunction.delayAndRequestFocus(_focusNode);
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      backgroundColor: evoColors.background,
      appBar: EvoSupportAppbar(
        leading: null,
      ),
      body: SafeArea(
        child: PopScope(
          canPop: false,
          child: BlocProvider<ReAuthInputPinCubit>(
            create: (_) => _cubit,
            child: BlocConsumer<ReAuthInputPinCubit, ReAuthPinState>(
              listener: (BuildContext context, ReAuthPinState state) {
                _handleListener(state);
              },
              builder: (BuildContext context, ReAuthPinState state) {
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: _itemBody(state),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _itemBody(ReAuthPinState state) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            EvoStrings.inputPinTitle,
            style: evoTextStyles.bold(TextSize.xl2),
          ),
          EvoMPINCodeWidget(
            title: EvoStrings.inputPinDesc,
            focusNode: _focusNode,
            autoFocus: false,
            textEditingController: textCreateController,
            onChange: _cubit.validateLengthPin,
            onSubmit: (String pin) =>
                _cubit.verifyPin(widget.phoneNumber, pin, widget.sessionToken),
            onResetPin: _onResetPin,
            errorMessage: getErrorText(state),
          ),
        ],
      );

  String? getErrorText(ReAuthPinState state) {
    return state is VerifyPinBadRequest ? state.errorMessage : null;
  }

  void _handleListener(ReAuthPinState state) {
    if (state is PinLoadingState) {
      evoUtilFunction.showHudLoading();
      return;
    }

    evoUtilFunction.hideHudLoading();

    switch (state) {
      case VerifyPinSuccessState():
        updateUserLoginStatus(true);
        ActiveBiometricScreen.pushNamed(onSuccess: () {
          MainScreen.goNamed(
            isLoggedIn: true,
          );
        });
        return;

      case PinSessionExpired():
        _handleSessionTokenExpired();
        return;

      case PinLimitExceeded():
        _showLimitExceededDialog(content: state.errorMessage);
        return;

      case PinErrorState():
        handleEvoApiError(state.errorUIModel);
        return;
      default:
        return;
    }
  }

  void _handleSessionTokenExpired() {
    evoDialogFunction.showDialogSessionTokenExpired();
  }

  void _showLimitExceededDialog({String? content}) {
    evoDialogFunction.showDialogConfirm(
        isDismissible: false,
        title: EvoStrings.loginLimitedExceededTitle,
        content: content,
        textPositive: EvoStrings.backToHomePage,
        dialogId: EvoDialogId.defaultErrorDialog,
        alertType: DialogAlertType.error,
        onClickPositive: () {
          WelcomeScreen.goNamed();
        });
  }

  void _onResetPin() {
    _resetPinHandler.requestResetPin(
      phoneNumber: widget.phoneNumber,
      onError: handleEvoApiError,
      entryScreenName: widget.routeSettings.name,
    );
  }
}
