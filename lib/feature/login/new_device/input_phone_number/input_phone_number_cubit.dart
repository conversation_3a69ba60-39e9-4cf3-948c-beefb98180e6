import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../data/repository/authentication_repo.dart';
import '../../../../data/response/sign_in_entity.dart';
import '../../../verify_otp/mock/mock_verify_otp_use_case.dart';

part 'input_phone_number_state.dart';

class InputPhoneNumberCubit extends CommonCubit<InputPhoneNumberState> {
  final AuthenticationRepo authenticationRepo;

  InputPhoneNumberCubit(this.authenticationRepo) : super(InputPhoneNumberInitial());

  Future<void> signIn(String phoneNumber) async {
    emit(InputPhoneNumberLoading());
    final SignInEntity signInEntity = await authenticationRepo.signIn(
      TypeLogin.otp,
      phoneNumber: phoneNumber,
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockVerifyOtpFileNameByCase(MockVerifyOtpUseCase.getVerifySignInOtpSuccess),
      ),
    );

    if (signInEntity.statusCode == CommonHttpClient.SUCCESS) {
      emit(InputPhoneNumberPhoneSuccess(entity: signInEntity));
    } else {
      emit(InputPhoneNumberFailed(ErrorUIModel.fromEntity(signInEntity)));
    }
  }

  void changePhoneNumber() {
    emit(ChangePhoneNumber());
  }
}
