// coverage:ignore-file
// hoang-nguyen2: file or testing for deprecated login flow, will be removed in the future
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/widgets.dart';

import '../../../../base/evo_page_state_base.dart';
import '../../../../data/repository/authentication_repo.dart';
import '../../../../data/response/auth_challenge_type.dart';
import '../../../../data/response/sign_in_entity.dart';
import '../../../../model/evo_dialog_id.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/resources.dart';
import '../../../../util/dialog_functions.dart';
import '../../../../util/extension.dart';
import '../../../../util/formatter/phone_number_formatter.dart';
import '../../../../util/functions.dart';
import '../../../../util/screen_util.dart';
import '../../../../widget/appbar/evo_appbar_leading_button.dart';
import '../../../../widget/appbar/evo_support_appbar.dart';
import '../../../../widget/evo_text_field.dart';
import '../../../ekyc/selfie/selfie_verification_screen.dart';
import '../../../verify_otp/cubit/verify_otp_cubit.dart';
import '../../../verify_otp/verify_otp_page.dart';
import '../../../welcome/welcome_screen.dart';
import '../../old_device/re_auth_input_pin/re_auth_input_pin_screen.dart';
import 'input_phone_number_cubit.dart';

class InputPhoneNumberPage extends PageBase {
  static Future<void> pushNamed({String? lastPhoneNumberLogged}) async {
    return navigatorContext?.pushNamed(
      Screen.inputPhoneNumberScreen.name,
    );
  }

  static Future<void> pushReplacement({String? lastPhoneNumberLogged}) async {
    return navigatorContext?.pushReplacementNamed(
      Screen.inputPhoneNumberScreen.name,
    );
  }

  static Future<void> goNamed() async {
    return navigatorContext?.goNamed(
      Screen.inputPhoneNumberScreen.name,
    );
  }

  static Future<void> pushReplacementNamed() async {
    return navigatorContext?.pushReplacementNamed(
      Screen.inputPhoneNumberScreen.name,
    );
  }

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.inputPhoneNumberScreen.routeName);

  const InputPhoneNumberPage({
    super.key,
  });

  @override
  State<InputPhoneNumberPage> createState() => _InputPhoneNumberState();
}

class _InputPhoneNumberState extends EvoPageStateBase<InputPhoneNumberPage> {
  /// https://www.figma.com/design/XEx8EOk56p5slUTSWDy3Ii/EVO-App-MVP?node-id=4693-87665&t=Qj9J9t5dr8BO3dyM-0
  static const double brandNameLogoWidth = 137.34;

  static const double brandNameTopPadding = 42;

  static const double _imgLoginOnNewDeviceHeight = 169.81;

  /// _phoneController & _phoneFocusNode will be disposed inside of EvoTextField widget
  final TextEditingController _phoneController = TextEditingController();
  final FocusNode _phoneFocusNode = FocusNode();
  String? errorMessage;

  final InputPhoneNumberCubit _cubit = InputPhoneNumberCubit(getIt.get<AuthenticationRepo>());

  String get _phoneNumber {
    return _phoneController.text.replaceAll(' ', '').addPrefixCountryCode();
  }

  @override
  void initState() {
    super.initState();
    commonUtilFunction.delayAndRequestFocus(_phoneFocusNode);
  }

  Future<void> _handleBackButton() async {
    await evoUtilFunction.hideKeyboard();
    WelcomeScreen.goNamed();
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<InputPhoneNumberCubit>(
      create: (_) => _cubit,
      child: BlocConsumer<InputPhoneNumberCubit, InputPhoneNumberState>(
        listener: (BuildContext previousState, InputPhoneNumberState currentState) async {
          _listenStateChanged(currentState);
        },
        builder: (BuildContext context, InputPhoneNumberState state) {
          return Scaffold(
            appBar: EvoSupportAppbar(
              leading: EvoAppBarLeadingButton(
                onPressed: () => _handleBackButton(),
              ),
            ),
            backgroundColor: evoColors.background,
            body: SafeArea(
              child: PopScope(
                canPop: false,
                onPopInvokedWithResult: (_, __) {
                  SchedulerBinding.instance.addPostFrameCallback((_) {
                    WelcomeScreen.goNamed();
                  });
                },
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
                  child: CustomScrollView(
                    slivers: <Widget>[
                      SliverFillRemaining(
                        hasScrollBody: false,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            SizedBox(
                              height: evoUtilFunction.calculateVerticalSpace(
                                context: context,
                                heightPercentage:
                                    brandNameTopPadding / EvoDimension.figmaScreenHeight,
                              ),
                            ),
                            imageProvider.asset(EvoImages.imgBrandName,
                                width: evoUtilFunction.calculateHorizontalSpace(
                                  context: context,
                                  widthPercentage:
                                      brandNameLogoWidth / EvoDimension.figmaScreenWidth,
                                ),
                                fit: BoxFit.fitWidth),
                            const SizedBox(height: 24),
                            Text(EvoStrings.loginDesc, style: evoTextStyles.regular(TextSize.base)),
                            const SizedBox(height: 24),
                            _buildPhoneTextField(state),
                            const SizedBox(height: 24),
                            _buildGetStarted(),
                            const Spacer(),
                            const SizedBox(height: 50),
                            Align(
                              alignment: FractionalOffset.bottomCenter,
                              child: CommonButton(
                                  onPressed: true == _phoneController.text.isNotEmpty
                                      ? _onTapLoginBtn
                                      : null,
                                  isWrapContent: false,
                                  style: evoButtonStyles.primary(ButtonSize.medium),
                                  child: const Text(EvoStrings.login)),
                            )
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Future<void> _listenStateChanged(InputPhoneNumberState currentState) async {
    if (currentState is InputPhoneNumberLoading) {
      evoUtilFunction.showHudLoading();
      return;
    }

    evoUtilFunction.hideHudLoading();

    if (currentState is InputPhoneNumberPhoneSuccess) {
      final SignInEntity? entity = currentState.entity;
      switch (entity?.verdict) {
        case BaseEntity.verdictSuccess:

          /// TODO: hoa.nguyen2 update the time when the popup is shown
          /// when https://trustingsocial1.atlassian.net/browse/EMA-3392 is resolved
          showLoginOnNewDevicePopup(entity);
          break;
        case SignInEntity.verdictUserNotExisted:
          break;
      }
    } else if (currentState is InputPhoneNumberFailed) {
      await _handleApiError(currentState);
    }
  }

  Widget _buildGetStarted() {
    return GestureDetector(
      onTap: () {
        /// TODO nam-pham-ts handle get started
        /// when https://trustingsocial1.atlassian.net/browse/EMA-3607 is resolved
      },
      child: RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
          style: evoTextStyles.regular(TextSize.base),
          children: <InlineSpan>[
            const TextSpan(text: EvoStrings.doNotHaveAccount),
            TextSpan(
                text: EvoStrings.getStarted,
                style: evoTextStyles
                    .bold(TextSize.base, color: evoColors.primary)
                    .copyWith(decoration: TextDecoration.underline)),
          ],
        ),
      ),
    );
  }

  Widget _buildPhoneTextField(InputPhoneNumberState state) {
    return EvoTextField(
      focusNode: _phoneFocusNode,
      textEditingController: _phoneController,
      onChanged: (String value) {
        _cubit.changePhoneNumber();
      },
      maxLines: 1,
      inputFormatters: <TextInputFormatter>[
        PhoneNumberFormatter(),
      ],
      label: EvoStrings.mobileNumberLabel,
      prefixBuilder: (EdgeInsets padding) {
        return Padding(
          padding: padding.copyWith(right: 4.sp),
          child: Text(
            EvoStrings.countryPhoneCode,
          ),
        );
      },
      keyboardType: TextInputType.phone,
      errMessage: errorMessage,
      hintText: EvoStrings.unKnowPhone,
    );
  }

  Future<void> _handleApiError(InputPhoneNumberFailed currentState) async {
    if (currentState.errorUIModel.verdict == SignInEntity.verdictLimitExceeded) {
      await evoDialogFunction.showDialogConfirm(
        alertType: DialogAlertType.error,
        title: EvoStrings.limitResendOtp,
        content: currentState.errorUIModel.userMessage,
        dialogId: EvoDialogId.inputPhoneNumberLimitOTPErrorDialog,
        isDismissible: false,
        textPositive: EvoStrings.backToHomePage,
        onClickPositive: () {
          WelcomeScreen.goNamed();
        },
      );
      return;
    }

    switch (currentState.errorUIModel.statusCode) {
      case CommonHttpClient.BAD_REQUEST:
        errorMessage = currentState.errorUIModel.userMessage;
        break;
      default:
        await handleEvoApiError(currentState.errorUIModel);
    }
  }

  void _onTapLoginBtn() {
    FocusManager.instance.primaryFocus?.unfocus();

    ///clear error text after call api
    errorMessage = null;
    _cubit.signIn(_phoneNumber);
  }

  void _handleOtpSuccess(VerifyOtpState state) {
    if (state is VerifyOtpSuccess) {
      final AuthChallengeType? challengeType =
          AuthChallengeType.fromString(state.uiModel.challengeType);
      switch (challengeType) {
        case AuthChallengeType.verifyPin:
          ReAuthInputPinScreen.pushNamed(
            phoneNumber: _phoneNumber,
            sessionToken: state.uiModel.sessionToken,
          );
          break;
        case AuthChallengeType.createPin:
        case AuthChallengeType.changePin:
        case AuthChallengeType.faceAuth:
          SelfieVerificationScreen.pushNamed(
            sessionToken: state.uiModel.sessionToken,
            flowType: SelfieVerificationFlowType.signIn,
            onPopSuccess: (_) {
              /// TODO: hoang-nguyen2 handle onSuccess
            },
          );
          break;
        case AuthChallengeType.none:
        case null:
          break;
      }
    }
  }

  Future<void> showLoginOnNewDevicePopup(SignInEntity? entity) {
    return evoDialogFunction.showDialogConfirm(
      imageHeader: imageProvider.asset(
        EvoImages.imgLoginOnNewDevice,
        height: evoUtilFunction.calculateVerticalSpace(
          context: context,
          heightPercentage: _imgLoginOnNewDeviceHeight / EvoDimension.figmaScreenHeight,
        ),
      ),
      title: EvoStrings.loginOnNewDeviceTitle,
      content: EvoStrings.loginOnNewDeviceDesc,
      dialogId: EvoDialogId.loginOnNewDeviceDialog,
      textPositive: EvoStrings.loginOnNewDeviceConfirmCTA,
      onClickPositive: () {
        navigatorContext?.pop();
        VerifyOtpPage.pushNamed(
          verifyOtpType: VerifyOtpType.signIn,
          contactInfo: _phoneNumber,
          otpValiditySecs: entity?.otpValiditySecs,
          otpResendSecs: entity?.otpResendSecs,
          sessionToken: entity?.sessionToken,
          onPopSuccess: _handleOtpSuccess,
        );
      },
      onClickNegative: () {
        navigatorContext?.pop();
      },
      textNegative: EvoStrings.ctaCancel,
    );
  }
}
