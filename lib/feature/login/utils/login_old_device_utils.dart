import '../../biometric/biometric_token_module/biometric_token_usability_mixin.dart';

class LoginOldDeviceUtils with BiometricTokenUsabilityMixin {
  Future<bool> checkCanLoginByBiometric() async {
    /// Check biometric is enabled
    final bool isEnableAuthByBiometrics =
        await biometricsTokenModule.isEnableBiometricAuthenticator();
    if (isEnableAuthByBiometrics != true) {
      return false;
    }

    /// Check biometricToken is can use or not
    if (await checkAndHandleBiometricTokenUnUsable() == true) {
      return false;
    }

    return true;
  }
}
