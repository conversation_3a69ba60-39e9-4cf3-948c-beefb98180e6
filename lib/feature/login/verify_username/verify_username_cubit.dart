import 'package:flutter_common_package/common_package/common_package.dart';

import '../../../util/validator/username_validator.dart';
import 'verify_username_state.dart';

class VerifyUsernameCubit extends BlocBase<VerifyUsernameState> {
  VerifyUsernameCubit() : super(VerifyUsernameInitial());

  // TODO: integrate API
  Future<void> verify(String username) async {
    final String? error = UsernameValidator().validate(username);
    if (error != null) {
      emit(VerifyUsernameError(error: error));
      return;
    }

    emit(VerifyUsernameLoading());
    await Future<void>.delayed(const Duration(seconds: 1));
    if (username == 'error') {
      emit(VerifyUsernameError(error: 'error message'));
    } else {
      emit(VerifyUsernameSuccess());
    }
  }

  void resetError() {
    if (state is VerifyUsernameError) {
      emit(VerifyUsernameInitial());
    }
  }
}
