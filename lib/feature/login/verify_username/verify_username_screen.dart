import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../data/response/auth_challenge_type.dart';
import '../../../resources/resources.dart';
import '../../../util/functions.dart';
import '../../../util/screen_util.dart';
import '../../../widget/appbar/evo_appbar_leading_button.dart';
import '../../../widget/appbar/need_help_support_appbar.dart';
import '../../../widget/evo_text_field.dart';
import '../../../widget/string_cta_widget.dart';
import '../../verify_otp/cubit/verify_otp_cubit.dart';
import '../../verify_otp/verify_otp_page.dart';
import '../../welcome/welcome_screen.dart';
import '../old_device/re_auth_input_pin/re_auth_input_pin_screen.dart';
import 'verify_username_cubit.dart';
import 'verify_username_state.dart';

class VerifyUsernameScreen extends PageBase {
  @visibleForTesting
  final VerifyUsernameCubit? cubit;

  const VerifyUsernameScreen({@visibleForTesting this.cubit, super.key});

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.verifyUsernameScreen.routeName);

  @override
  State<VerifyUsernameScreen> createState() => _VerifyUsernameScreenState();

  static void pushNamed() {
    navigatorContext?.pushNamed(Screen.verifyUsernameScreen.name);
  }

  static void goNamed() {
    navigatorContext?.goNamed(Screen.verifyUsernameScreen.name);
  }

  static void pushReplacementNamed() {
    navigatorContext?.pushReplacementNamed(Screen.verifyUsernameScreen.name);
  }
}

class _VerifyUsernameScreenState extends PageStateBase<VerifyUsernameScreen> {
  late final VerifyUsernameCubit _cubit = widget.cubit ?? VerifyUsernameCubit();

  final TextEditingController _controller = TextEditingController();

  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    commonUtilFunction.delayAndRequestFocus(_focusNode);
  }

  void _listenVerifyUsernameState(VerifyUsernameState state) {
    if (state is VerifyUsernameLoading) {
      evoUtilFunction.showHudLoading();
      return;
    }
    evoUtilFunction.hideHudLoading();

    if (state is VerifyUsernameSuccess) {
      // TODO: update with new flow using username in place of phone number
      VerifyOtpPage.pushNamed(
        verifyOtpType: VerifyOtpType.signIn,
        contactInfo: '639178919821',
        otpResendSecs: 60,
        otpValiditySecs: 120,
        onPopSuccess: _handleOtpSuccess,
      );
    }
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<VerifyUsernameCubit>(
      create: (_) => _cubit,
      child: BlocListener<VerifyUsernameCubit, VerifyUsernameState>(
        listener: (_, VerifyUsernameState state) => _listenVerifyUsernameState(state),
        child: PopScope(
          canPop: false,
          onPopInvokedWithResult: (_, __) => _onPop(),
          child: Scaffold(
            backgroundColor: evoColors.defaultWhite,
            appBar: _buildAppBar(),
            body: _buildBodyWidget(),
          ),
        ),
      ),
    );
  }

  Widget _buildBodyWidget() {
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.only(left: 24.w, right: 24.w, bottom: 24.w, top: 0.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
            Text(
              EvoStrings.verifyUsernameTitle,
              style: evoTextStyles.bold(TextSize.xl2, color: evoColors.screenTitle),
            ),
            EvoDimension.space24,
            _buildUsernameField(),
            EvoDimension.space24,
            StringCtaWidget(
              question: EvoStrings.forgotUsernameTitle,
              cta: EvoStrings.reset,
              onTap: _resetUsername,
            ),
            const Spacer(),
            CommonButton(
              onPressed: _verifyUsername,
              isWrapContent: false,
              style: evoButtonStyles.primary(ButtonSize.medium),
              child: const Text(EvoStrings.login),
            ),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return NeedHelpSupportAppbar(
      leading: EvoAppBarLeadingButton(onPressed: _onPop),
    );
  }

  Widget _buildUsernameField() {
    return BlocSelector<VerifyUsernameCubit, VerifyUsernameState, String?>(
      selector: (VerifyUsernameState state) => state is VerifyUsernameError ? state.error : null,
      builder: (_, String? error) {
        return EvoTextField(
          focusNode: _focusNode,
          textEditingController: _controller,
          maxLines: 1,
          errMessage: error,
          label: EvoStrings.usernameLabel,
          keyboardType: TextInputType.text,
          onChanged: (_) => _cubit.resetError(),
          onSubmitted: (_) => _verifyUsername(),
        );
      },
    );
  }

  void _verifyUsername() {
    _cubit.verify(_controller.text);
  }

  Future<void> _onPop() async {
    await evoUtilFunction.hideKeyboard();
    WelcomeScreen.goNamed();
  }

  void _resetUsername() {
    // TODO: Implement reset username flow
  }

  void _handleOtpSuccess(VerifyOtpState state) {
    if (state is VerifyOtpSuccess) {
      final AuthChallengeType? challengeType =
          AuthChallengeType.fromString(state.uiModel.challengeType);
      switch (challengeType) {
        case AuthChallengeType.verifyPin:
          ReAuthInputPinScreen.pushNamed(
            // TODO: update to username
            phoneNumber: '639178919821',
            sessionToken: state.uiModel.sessionToken,
          );
        default:
      }
    }
  }
}
