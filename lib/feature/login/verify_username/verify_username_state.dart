import 'package:flutter_common_package/base/bloc_state.dart';

sealed class VerifyUsernameState extends BlocState {}

class VerifyUsernameInitial extends VerifyUsernameState {}

class VerifyUsernameLoading extends VerifyUsernameState {}

class VerifyUsernameSuc<PERSON> extends VerifyUsernameState {}

class VerifyUsernameError extends VerifyUsernameState {
  final String error;

  VerifyUsernameError({required this.error});
}
