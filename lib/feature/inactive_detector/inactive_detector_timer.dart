enum InactiveDetectorTimerState {
  /// if user interact on device screen
  active,

  /// user doesn't interact with app in 60 seconds
  idleAWhile,

  /// user doesn't interact with app in 120 seconds
  inActive,

  /// initial state
  initial,
}

/// this class is used to detect user inActive \ Idle
/// if user doesn't interact with app in 60 seconds, state is [InactiveDetectorTimerState.idle] we will show a dialog to notify user
/// if user doesn't interact with app in 120 seconds, state is [InactiveDetectorTimerState.inActive] we will show a dialog to notify user
/// if user interact with app,  state is [InactiveDetectorTimerState.active]
/// [InactiveDetectorTimerState.initial] is initial state, it represents InActiveDetectorTimer is not started yet
/// PRD: https://trustingsocial1.atlassian.net/wiki/spaces/BPIRBank/pages/**********/Log-Out
abstract class InActiveDetectorTimer {
  /// start to detect  inActive.
  /// state is set to [InactiveDetectorTimerState.active]
  /// if current state is [InactiveDetectorTimerState.inActive], do nothing
  void start();

  /// this function is called when user interact with app & we need to reset the timer
  /// do not change state
  /// if current state is [InactiveDetectorTimerState.inActive] or [InactiveDetectorTimerState.initial], do nothing
  void reset();

  /// stop when do not need to detect inActive, Idle
  /// state is set to [InactiveDetectorTimerState.initial]
  void stop();

  /// get current state of InActiveDetectorTimer
  InactiveDetectorTimerState get state;
}
