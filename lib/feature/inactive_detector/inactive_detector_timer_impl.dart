import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/util/extension.dart';

import 'inactive_detector_timer.dart';

class InActiveDetectorTimerImpl implements InActiveDetectorTimer {
  static const int defaultTimeOutInSec = 60;
  final int timeOutInSec;

  InactiveDetectorTimerState _state = InactiveDetectorTimerState.initial;
  Timer? _timer;
  VoidCallback? onInactive;
  VoidCallback? onIdleAWhile;

  InActiveDetectorTimerImpl({
    this.timeOutInSec = defaultTimeOutInSec,
    this.onInactive,
    this.onIdleAWhile,
  });

  @override
  InactiveDetectorTimerState get state => _state;

  @override
  void start() {
    _cancelTimer();

    if (_state == InactiveDetectorTimerState.inActive) {
      commonLog('user is inActive - do nothing');
      return;
    }

    _state = InactiveDetectorTimerState.active;
    _timer = Timer(Duration(seconds: timeOutInSec), _onTimerCompleted);
  }

  @override
  void reset() {
    _cancelTimer();

    if (_state == InactiveDetectorTimerState.inActive ||
        _state == InactiveDetectorTimerState.initial) {
      commonLog('user is inactive - do nothing');
      return;
    }

    _timer = Timer(Duration(seconds: timeOutInSec), _onTimerCompleted);
  }

  @override
  void stop() {
    _cancelTimer();
    _state = InactiveDetectorTimerState.initial;
  }

  void _cancelTimer() {
    if (_timer?.isActive ?? false) {
      _timer?.cancel();
    }
  }

  void _onTimerCompleted() {
    switch (_state) {
      case InactiveDetectorTimerState.active:
        _state = InactiveDetectorTimerState.idleAWhile;
        reset();
        onIdleAWhile?.call();
        break;
      case InactiveDetectorTimerState.idleAWhile:
        _state = InactiveDetectorTimerState.inActive;
        _cancelTimer();
        onInactive?.call();
        break;
      default:
        break;
    }
  }
}
