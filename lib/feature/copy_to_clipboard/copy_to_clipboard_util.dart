import 'package:flutter_common_package/util/device_info_plugin_wrapper/device_info_plugin_wrapper.dart';

import '../../util/evo_snackbar.dart';

class CopyToClipboardUtil {
  // https://source.android.com/docs/setup/reference/build-numbers#source-code-tags-and-builds
  static const int androidOs13SdkVersion = 33;
  
  
  final DeviceInfoPluginWrapper deviceInfoPluginWrapper;
  final EvoSnackBar evoSnackBar;

  CopyToClipboardUtil({required this.deviceInfoPluginWrapper, required this.evoSnackBar});

  /// show feedback for copy to clipboard action
  /// for Android 13 and above, no need to show feedback because it is handled by the system
  /// for Android 12 and below, show itself toast message
  /// for iOS, show itself toast message
  /// reference: https://source.android.com/docs/setup/reference/build-numbers#source-code-tags-and-builds
  void showFeedback({required String toastMsg}) {
    final bool isAndroid13OrHigher = isAndroid13OrAbove();

    if (isAndroid13OrHigher) {
      // no-op
      return;
    }

    /// show feedback if device is running on:
    ///  Android device: Android 12 or below
    ///  iOS device
    evoSnackBar.show(
      toastMsg,
      durationInMilliSec: SnackBarDuration.short.value,
    );
  }

  /// check if the device is running on Android 13 or above
  bool isAndroid13OrAbove() {
    bool isAndroid13OrAbove = false;
    if (deviceInfoPluginWrapper.platform.isAndroid()) {
      final int sdkInt = int.parse(deviceInfoPluginWrapper.getOSVersion() ?? '0');
      isAndroid13OrAbove = sdkInt >= androidOs13SdkVersion;
    }
    return isAndroid13OrAbove;
  }
}
