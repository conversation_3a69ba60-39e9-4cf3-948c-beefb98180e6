import 'package:flutter_common_package/common_package/common_package.dart';

import '../../data/response/transaction_details_entity.dart';
import 'transaction_details_state.dart';

class TransactionDetailsCubit extends BlocBase<TransactionDetailsState> {
  TransactionDetailsCubit() : super(TransactionDetailsInitial());

  // TODO: integrate API
  Future<void> getTransactionDetails(String transactionId) async {
    await Future<void>.delayed(const Duration(milliseconds: 100));
    emit(TransactionDetailsLoading());
    await Future<void>.delayed(const Duration(seconds: 1));
    emit(TransactionDetailsSuccess(details: _fakeEntity));
  }

  late final TransactionDetailsEntity _fakeEntity = TransactionDetailsEntity(
    id: '1q2w3e4r5t6y7u8i9o0p',
    amount: 149,
    paidTo: 'Spotify',
    paidBy: '5638',
    dateTime: DateTime(2024, 2, 16, 19, 09),
    postingDate: DateTime(2024, 2, 19, 19, 09),
    byVirtualCard: true,
  );
}
