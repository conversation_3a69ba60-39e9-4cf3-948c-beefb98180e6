import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../data/response/transaction_details_entity.dart';
import '../../resources/resources.dart';
import '../../util/extension.dart';
import '../../util/functions.dart';
import '../../util/screen_util.dart';
import '../../widget/action_button_widget.dart';
import '../../widget/appbar/evo_appbar_leading_button.dart';
import '../../widget/appbar/evo_support_appbar.dart';
import '../../widget/elevated_container.dart';
import 'transaction_details_cubit.dart';
import 'transaction_details_state.dart';

@visibleForTesting
const String dateTimeFormatWithTime = 'MMM dd, yyyy, hh:mm a';

class TransactionDetailsScreenArg extends PageBaseArg {
  final String transactionId;

  TransactionDetailsScreenArg({required this.transactionId});
}

class TransactionDetailsScreen extends PageBase {
  final String transactionId;

  const TransactionDetailsScreen({required this.transactionId, super.key});

  @override
  State<TransactionDetailsScreen> createState() => _TransactionDetailsScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.transactionDetailsScreen.routeName);

  static void pushNamed({required String transactionId}) {
    navigatorContext?.pushNamed(
      Screen.transactionDetailsScreen.name,
      extra: TransactionDetailsScreenArg(transactionId: transactionId),
    );
  }
}

class _TransactionDetailsScreenState extends PageStateBase<TransactionDetailsScreen> {
  final TransactionDetailsCubit _cubit = TransactionDetailsCubit();

  @override
  void initState() {
    super.initState();
    _cubit.getTransactionDetails(widget.transactionId);
  }

  void _listenTransactionDetailsState(TransactionDetailsState state) {
    if (state is TransactionDetailsLoading) {
      evoUtilFunction.showHudLoading();
    } else {
      evoUtilFunction.hideHudLoading();
    }
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<TransactionDetailsCubit>(
      create: (_) => _cubit,
      child: Scaffold(
        backgroundColor: evoColors.background,
        appBar: _buildAppBar(),
        body: SafeArea(
          child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(horizontal: 24.w),
            child: BlocConsumer<TransactionDetailsCubit, TransactionDetailsState>(
              listener: (_, TransactionDetailsState state) => _listenTransactionDetailsState(state),
              builder: (_, TransactionDetailsState state) {
                if (state is TransactionDetailsSuccess) {
                  return _buildDetailsView(state.details);
                }
                return const SizedBox.shrink();
              },
            ),
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return EvoSupportAppbar(
      actions: <Widget>[],
      leading: EvoAppBarLeadingButton(
        onPressed: () => navigatorContext?.pop(),
      ),
    );
  }

  Widget _buildDetailsView(TransactionDetailsEntity transaction) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: <Widget>[
        Text(
          EvoStrings.transactionDetailsTitle,
          style: evoTextStyles.bold(TextSize.xl2, color: evoColors.screenTitle),
        ),
        EvoDimension.space24,
        Text(
          EvoStrings.paymentTitle,
          style: evoTextStyles.bold(TextSize.base, color: evoColors.screenTitle),
        ),
        EvoDimension.space24,
        _buildRowAmount(transaction.amount),
        EvoDimension.space24,
        _buildColumnDetails(transaction),
        EvoDimension.space24,
        _buildRowNeedHelp(),
      ],
    );
  }

  Widget _buildRowAmount(double? amount) {
    return ElevatedContainer(
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 22.w, horizontal: 24.w),
        child: Row(
          children: <Widget>[
            evoImageProvider.asset(
              height: 24.w,
              width: 24.w,
              EvoImages.icBanknotes,
              color: evoColors.accent90,
            ),
            EvoDimension.space8,
            Text(
              EvoStrings.amountTitle,
              overflow: TextOverflow.ellipsis,
              style: evoTextStyles.bold(TextSize.sm, color: evoColors.textNormal),
            ),
            EvoDimension.space8,
            Expanded(
              child: Text(
                evoUtilFunction.evoFormatCurrency(amount),
                textAlign: TextAlign.right,
                overflow: TextOverflow.ellipsis,
                style: evoTextStyles.bold(TextSize.xl, color: evoColors.textNormal),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColumnDetails(TransactionDetailsEntity transaction) {
    final String? paidByStr = transaction.byVirtualCard == true
        ? '${EvoStrings.virtualCard} ···· ${transaction.paidBy}'
        : transaction.paidBy;

    return ElevatedContainer(
      child: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          children: <Widget>[
            _buildRowDetails(
              image: EvoImages.icArrowsRightLeft,
              title: EvoStrings.transactionIdTitle,
              value: transaction.id,
            ),
            _buildDetailsDivider(),
            _buildRowDetails(
              image: EvoImages.icChevronDoubleUp,
              title: EvoStrings.paidToTitle,
              value: transaction.paidTo,
            ),
            _buildDetailsDivider(),
            _buildRowDetails(
              image: EvoImages.icChevronDoubleDown,
              title: EvoStrings.paidByTitle,
              value: paidByStr,
            ),
            _buildDetailsDivider(),
            _buildRowDetails(
              image: EvoImages.icCalendarDays,
              title: EvoStrings.dateAndTimeTitle,
              value: transaction.dateTime?.toStringFormatDate(format: dateTimeFormatWithTime),
            ),
            _buildDetailsDivider(),
            _buildRowDetails(
              image: EvoImages.icDocumentText,
              title: EvoStrings.postingDateTitle,
              value: transaction.dateTime?.toStringFormatDate(format: dateTimeFormatWithTime),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailsDivider() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.w),
      child: Divider(
        height: 1,
        thickness: 1,
        color: evoColors.greyScale60,
      ),
    );
  }

  Widget _buildRowDetails({required String image, required String title, required String? value}) {
    return SizedBox(
      height: 40.w,
      child: Row(
        children: <Widget>[
          evoImageProvider.asset(
            height: 24.w,
            width: 24.w,
            image,
            color: evoColors.accent90,
          ),
          EvoDimension.space8,
          Text(
            title,
            style: evoTextStyles.regular(TextSize.sm, color: evoColors.textNormal),
          ),
          EvoDimension.space8,
          Expanded(
            child: Text(
              value ?? '-',
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.right,
              style: evoTextStyles.regular(TextSize.sm, color: evoColors.textNormal),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRowNeedHelp() {
    return ActionButtonWidget(
      title: EvoStrings.needHelp,
      icon: EvoImages.icQuestionMarkCircle,
      iconColor: evoColors.accent90,
      onPress: () {
        evoUtilFunction.openInAppWebView(
          title: EvoStrings.contactItemTitle,
          url: WebsiteUrl.evoContactUrl,
        );
      },
    );
  }
}
