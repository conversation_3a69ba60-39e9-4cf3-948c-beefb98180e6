import 'package:flutter_common_package/base/bloc_state.dart';

import '../../data/response/transaction_details_entity.dart';

sealed class TransactionDetailsState extends BlocState {}

class TransactionDetailsInitial extends TransactionDetailsState {}

class TransactionDetailsLoading extends TransactionDetailsState {}

class TransactionDetailsSuccess extends TransactionDetailsState {
  final TransactionDetailsEntity details;

  TransactionDetailsSuccess({required this.details});
}

class TransactionDetailsError extends TransactionDetailsState {}
