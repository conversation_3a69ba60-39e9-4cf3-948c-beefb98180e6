
/// handle multiple device login
/// Technical Document: https://trustingsocial1.atlassian.net/wiki/spaces/BPIRBank/pages/**********/Multiple+devices+handling
/// invalid_token: it represents the token (access token, refresh token, biometric token, device token) is expired, the user needs to re-login
/// forced_logout: it represents the user logged in on another device, previous device needs to be logged out
/// unknown: it represents the session is unauthorized, the user needs to re-login
enum UnauthorizedSessionState {
  invalidToken('invalid_token'),
  forcedLogout('forced_logout'),
  unknown('unknown');

  final String value;

  const UnauthorizedSessionState(this.value);

  static UnauthorizedSessionState mapFrom(String? value) {
    switch (value) {
      case 'invalid_token':
        return UnauthorizedSessionState.invalidToken;
      case 'forced_logout':
        return UnauthorizedSessionState.forcedLogout;
      default:
        return UnauthorizedSessionState.unknown;
    }
  }
}

abstract class AuthorizationSessionExpiredHandler {
  Future<void> emitUnauthorized(UnauthorizedSessionState state);

  Stream<UnauthorizedSessionState> getStreamSubscription();

  void close();
}
