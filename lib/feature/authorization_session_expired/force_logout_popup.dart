import 'package:flutter/foundation.dart';

import '../../model/evo_dialog_id.dart';
import '../../resources/resources.dart';
import '../../util/dialog_functions.dart';
import '../login/verify_username/verify_username_screen.dart';

class ForceLogoutPopup {

  @visibleForTesting
  bool isShowingPopup = false;

  bool checkCanShowPopup() {
    if (isShowingPopup) {
      return false;
    }

    return true;
  }

  Future<void> show() async {
    isShowingPopup = true;

    await evoDialogFunction.showDialogConfirm(
      alertType: DialogAlertType.warning,
      dialogId: EvoDialogId.forceLogoutDialog,
      textPositive: EvoStrings.ctaLogInAgain,
      title: EvoStrings.forceLogoutTitle,
      isDismissible: false,
      autoClosePopupWhenClickCTA: true,
      onClickPositive: () {
        VerifyUsernameScreen.goNamed();
      },
    );

    isShowingPopup = false;
  }
}
