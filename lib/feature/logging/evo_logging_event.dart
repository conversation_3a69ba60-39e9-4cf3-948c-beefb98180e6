import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';

/// EVO app Event Tracking document
/// https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3371303073/EVO+App+Event+Tracking
class EvoEventType extends EventType {
  /// used for all features which are related EKYC feature
  static const EvoEventType ekyc = EvoEventType._create('ekyc');

  /// used for all features which are related FirebaseRemoteConfig feature
  /// such as
  /// * lib/data/repo/firebase_remote_config_impl.dart
  static const EvoEventType firebaseRemoteConfig = EvoEventType._create('firebase_remote_config');

  /// used for all features which are related to biometrics authenticated
  /// such as
  /// * lib/feature/biometric/*
  /// * lib/util/biometrics/*
  static const EvoEventType biometrics = EvoEventType._create('biometrics');

  /// Used when call downloadFile method from AndroidDownloaderPlatform
  static const EvoEventType downloadFile = EvoEventType._create('download_file');

  /// in common case, if exception doesn't belong to specified feature, this eventType would be used
  static const EvoEventType commonPlatformException =
      EvoEventType._create('common_platform_exception');

  /// Used when call [urlLauncherWrapper.launchUrl] method from [easy_pdf_viewer] package
  static const EvoEventType loadPdfFromUrl = EvoEventType._create('load_pdf_from_url');

  /// Used when call [urlLauncherWrapper.launchUrl] method
  static const EvoEventType launchUrl = EvoEventType._create('launch_url');

  /// Used for all feature which related to [mobile_scanner] library
  static const EvoEventType qrCodeScanner = EvoEventType._create('qr_code_scanner');

  /// Used for all feature which related to [share_plus] library
  static const EvoEventType sharingFeature = EvoEventType._create('sharing_feature');

  /// Used for all feature which related to [CancelableFutureController]
  static const EvoEventType cancelableFutureFeature =
      EvoEventType._create('cancelable_future_feature');

  /// Used for all feature which related to [AppsFlyersSDK]
  static const EvoEventType appsflyer = EvoEventType._create('appsflyer');

  /// Used for all feature which related to [DeepLink]
  static const EvoEventType deepLink = EvoEventType._create('deep_link');

  static const EvoEventType dopRecaptcha = EvoEventType._create('dop_recaptcha');

  static const EvoEventType dopNFC = EvoEventType._create('dop_nfc');

  const EvoEventType._create(super.name);
}
