abstract class EvoEventTrackingUtils {
  /// [eventActionId] is {SECTION C} in document
  /// Format: {SECTION A}.{SECTION B}.{SECTION C}
  /// Refer: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3380117684/Event+Tracking+Framework#Format
  void sendEvoUserEvent({
    required String eventActionId,
    Map<String, dynamic>? metaData,
  });

  /// [eventActionId] is {SECTION C} in document
  /// Format: {SECTION A}.0000.{SECTION C}
  /// Refer: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3380117684/Event+Tracking+Framework#Special-events
  void sendEvoSpecialEvent({
    required String eventActionId,
    Map<String, dynamic>? metaData,
  });
}
