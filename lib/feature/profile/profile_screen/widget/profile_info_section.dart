import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../../data/response/user_information_entity.dart';
import '../../../../resources/resources.dart';
import '../../../../widget/elevated_container.dart';

class ProfileInfoSection extends StatelessWidget {
  final UserInformationEntity? user;

  const ProfileInfoSection({required this.user, super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        _getFullNameText(),
        const SizedBox(
          height: 24,
        ),
        _getUserInformationBanner(),
      ],
    );
  }

  Widget _getFullNameText() {
    final String? fullName = user?.fullName;
    String text = '${EvoStrings.hi}!';

    if (fullName != null) {
      text = '${EvoStrings.hi}, $fullName!';
    }

    return Text(
      text,
      style: evoTextStyles.bold(
        TextSize.xl2,
        color: evoColors.screenTitle,
      ),
    );
  }

  Widget _getUserInformationBanner() {
    return ElevatedContainer(
        child: Padding(
      padding: const EdgeInsets.symmetric(horizontal: 19, vertical: 17),
      child: Row(
        children: <Widget>[
          _getAvatarWidget(user?.avatarUrl),
          const SizedBox(width: 8),
          Expanded(child: _getUserGeneralInfoWidget())
        ],
      ),
    ));
  }

  Widget _getAvatarWidget(String? avatarUrl) {
    Widget child;

    if (avatarUrl?.isNotEmpty == true) {
      child = evoImageProvider.network(
        avatarUrl,
        fit: BoxFit.cover,
      );
    } else {
      child = evoImageProvider.asset(
        EvoImages.icDefaultAvatar,
        fit: BoxFit.cover,
      );
    }

    return SizedBox.square(
      dimension: 64,
      child: Padding(
        padding: const EdgeInsets.all(6),
        child: child,
      ),
    );
  }

  String getFormattedPhone(String? phoneNumber) {
    return '+ ${phoneNumber?.applyStringFormat(
          prefixGroup: 2,
          stringFormatType: StringFormatType.phone,
        ) ?? ''}';
  }

  Widget _getUserGeneralInfoWidget() {
    if (user == null) {
      return const SizedBox.shrink();
    }

    final List<Widget> children = <Widget>[];
    final (String? fullName, String? phoneNumber, String? email) = (
      user?.fullName,
      user?.phoneNumber,
      user?.email,
    );

    if (fullName != null && fullName.isNotEmpty) {
      children.add(
        Text(
          fullName,
          style: evoTextStyles.bold(TextSize.base, color: evoColors.textNormal),
        ),
      );
    }

    if (phoneNumber != null && phoneNumber.isNotEmpty) {
      children.add(
        Text(
          getFormattedPhone(user?.phoneNumber),
          style: evoTextStyles.regular(TextSize.base, color: evoColors.textNormal),
        ),
      );
    }

    if (email != null && email.isNotEmpty) {
      children.add(
        Text(
          email,
          style: evoTextStyles.regular(TextSize.base, color: evoColors.textNormal),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }
}
