import 'package:flutter/cupertino.dart';

import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/resources.dart';
import '../../../../widget/action_button_widget.dart';
import '../../../../widget/evo_switch.dart';
import '../../../pin/models/change_pin_status.dart';

enum BiometricActivationStatus {
  activated,
  deactivated,
  unavailable,
}

class ProfilePrivacySecuritySection extends StatelessWidget {
  final BiometricActivationStatus biometricStatus;
  final ValueChanged<bool> onToggleBiometric;
  final VoidCallback onChangePin;

  const ProfilePrivacySecuritySection({
    required this.biometricStatus,
    required this.onToggleBiometric,
    required this.onChangePin,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        _getBiometricSwitchTile(),
        const SizedBox(height: 16),
        _getChangePinTile(),
      ],
    );
  }

  Widget _getBiometricSwitchTile() {
    if (biometricStatus == BiometricActivationStatus.unavailable) {
      return const SizedBox.shrink();
    }

    return ActionButtonWidget(
      title: EvoStrings.enableBiometrics,
      icon: EvoImages.icProfileBiometric,
      trailing: EvoSwitch(
        value: biometricStatus == BiometricActivationStatus.activated,
        onToggle: onToggleBiometric,
      ),
    );
  }

  Widget _getChangePinTile() {
    final AppState appState = getIt.get<AppState>();

    return ValueListenableBuilder<ChangePinStatus>(
      valueListenable: appState.changePinStatusNotifier,
      builder: (BuildContext context, ChangePinStatus status, Widget? child) {
        final bool mpinLocked = status == ChangePinStatus.locked;
        final Color? disabledColor = mpinLocked ? evoColors.greyScale70 : null;
        return ActionButtonWidget(
          iconColor: disabledColor,
          trailingColor: disabledColor,
          titleColor: disabledColor,
          onPress: onChangePin,
          icon: EvoImages.icMpin,
          title: EvoStrings.changeMPIN,
        );
      },
    );
  }
}
