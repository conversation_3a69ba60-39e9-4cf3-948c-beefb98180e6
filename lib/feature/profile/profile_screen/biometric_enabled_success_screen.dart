import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../resources/resources.dart';
import '../../../util/functions.dart';
import '../../../widget/appbar/evo_appbar.dart';

class BiometricEnabledSuccessScreen extends PageBase {
  const BiometricEnabledSuccessScreen({super.key});

  @override
  EvoPageStateBase<BiometricEnabledSuccessScreen> createState() =>
      _BiometricEnabledSuccessScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.biometricEnabledSuccessScreen.name);

  static Future<void> pushNamed() async {
    return navigatorContext?.pushNamed(
      Screen.biometricEnabledSuccessScreen.name,
    );
  }
}

class _BiometricEnabledSuccessScreenState extends EvoPageStateBase<BiometricEnabledSuccessScreen> {
  static const double _imageHeightPercentage = 256.41 / EvoDimension.figmaScreenHeight;

  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      backgroundColor: evoColors.primary100,
      appBar: EvoAppBar(
        leading: null,
        backgroundColor: evoColors.primary100,
        statusBarIconForAndroid: Brightness.light,
        statusBarIconForIos: Brightness.light,
      ),
      body: PopScope(
        canPop: false,
        child: Padding(
          padding: const EdgeInsets.only(left: 24, right: 24, bottom: 24),
          child: SizedBox.expand(
            child: Column(
              children: <Widget>[
                const Spacer(),
                _getImage(context),
                ..._getTitleDesc(),
                const Spacer(),
                _getCTA(),
                const SizedBox(height: 64),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _getImage(BuildContext context) {
    return evoImageProvider.asset(
      EvoImages.imgEnableBiometric,
      height: evoUtilFunction.calculateVerticalSpace(
        context: context,
        heightPercentage: _imageHeightPercentage,
      ),
    );
  }

  List<Widget> _getTitleDesc() {
    return <Widget>[
      Text(
        EvoStrings.enableBiometrics,
        style: evoTextStyles.bold(
          TextSize.xl2,
          color: evoColors.defaultWhite,
        ),
      ),
      Text(
        EvoStrings.activateBiometricsSuccessDesc,
        textAlign: TextAlign.center,
        style: evoTextStyles.regular(
          TextSize.base,
          color: evoColors.defaultWhite,
        ),
      )
    ];
  }

  Widget _getCTA() {
    return CommonButton(
      isWrapContent: false,
      onPressed: () {
        navigatorContext?.pop();
      },
      style: evoButtonStyles.primary(
        ButtonSize.large,
        brightness: Brightness.light,
      ),
      child: const Text(EvoStrings.gotIt),
    );
  }
}
