part of 'sign_out_cubit.dart';

abstract class SignOutState extends BlocState {}

class SignOutInitial extends SignOutState {
  SignOutInitial();
}

class SignOutLoading extends SignOutState {
  SignOutLoading();
}

class SignOutSuccess extends SignOutState {
  final BaseEntity? baseEntity;

  SignOutSuccess({this.baseEntity});
}

class SignOutFail extends SignOutState {
  final ErrorUIModel? error;

  SignOutFail({this.error});
}
