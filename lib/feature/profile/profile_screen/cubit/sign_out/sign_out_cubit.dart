import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import 'mock_signout_file_name.dart';

part 'sign_out_state.dart';

class SignOutCubit extends CommonCubit<SignOutState> {
  final AuthenticationRepo authenticationRepo;

  SignOutCubit(this.authenticationRepo) : super(SignOutInitial());

  Future<void> signOut() async {
    emit(SignOutLoading());

    final BaseEntity? baseEntity = await authenticationRepo.logout(
      mockConfig: MockConfig(
        enable: true,
        fileName: getSignOutMockFileName(MockSignOutUseCase.success),
      ),
    );

    if (baseEntity?.statusCode == CommonHttpClient.SUCCESS) {
      await evoUtilFunction.clearUserDataOnLogout();

      emit(SignOutSuccess(baseEntity: baseEntity));
    } else {
      emit(SignOutFail(error: ErrorUIModel.fromEntity(baseEntity)));
    }
  }
}
