import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../../../prepare_for_app_initiation.dart';
import '../../../../../resources/resources.dart';
import '../../../../biometric/biometric_token_module/biometric_token_usability_mixin.dart';
import '../../../../biometric/biometric_token_module/biometrics_token_module.dart';
import '../../../../biometric/utils/bio_auth_result.dart';
import '../../../../biometric/utils/biometric_functions.dart';
import '../../../../biometric/utils/biometrics_authenticate.dart';

part 'activate_biometric_state.dart';

class ActivateBiometricCubit extends CommonCubit<ActivateBiometricState>
    with BiometricTokenUsabilityMixin
    implements BiometricTokenModuleCallback {
  final BiometricsAuthenticate bioAuth;
  final EvoLocalStorageHelper secureStorageHelper;
  final AppState appState;

  ActivateBiometricCubit({
    required this.bioAuth,
    required this.secureStorageHelper,
    required this.appState,
  }) : super(ActivateBiometricInitState());

  Future<void> initialize() async {
    appState.biometricStatusChangeNotifier.addListener(listenOnBiometricStatusChanged);

    final bool isDeviceSupportBioMetrics = await bioAuth.isDeviceSupportBiometrics();

    if (!isDeviceSupportBioMetrics) {
      emit(BiometricUnsupported());
      return;
    }

    emit(BiometricSupported());
  }

  Future<void> checkBioAuthState() async {
    final [
      bool isBiometricTokenUnUsable,
      bool isEnableAuthByBiometrics,
    ] = await Future.wait(<Future<bool>>[
      checkAndHandleBiometricTokenUnUsable(),
      secureStorageHelper.isEnableBiometricAuthenticator()
    ]);

    if (isEnableAuthByBiometrics && !isBiometricTokenUnUsable) {
      emit(BiometricActivated());
      return;
    }

    emit(BiometricDeactivated());
  }

  Future<void> toggleBioAuth(bool value) async {
    final bool hasEnrolledBiometrics = await biometricsTokenModule.hasEnrolledBiometrics();
    if (hasEnrolledBiometrics) {
      if (value) {
        biometricsTokenModule.enable(callback: this);
      } else {
        await biometricsTokenModule.disableBiometricAuthenticatorFeature();
        checkBioAuthState();
      }
    } else {
      biometricFunctions.handleBioError(bioError: BioAuthError.notEnrolled);
    }
  }

  @override
  void onRetrieveTokenError({
    required BiometricTokenModuleErrorType type,
    String? userMessage,
    ErrorUIModel? error,
    BioAuthError? bioError,
  }) {
    switch (type) {
      case BiometricTokenModuleErrorType.biometrics:
        emit(RetrieveBiometricTokenBioAuthFailure(error: bioError));
        break;
      case BiometricTokenModuleErrorType.apiError:
        emit(RetrieveBiometricTokenFailure(
          errorMessage: error?.userMessage ?? EvoStrings.unknownError,
        ));

        break;
      default:
        emit(RetrieveBiometricTokenFailure(
          errorMessage: userMessage ?? EvoStrings.unknownError,
        ));
        break;
    }
  }

  @override
  Future<void> onRetrieveTokenSuccess() async {
    await checkBioAuthState();
    emit(RetrieveBiometricTokenSuccess());
  }

  @override
  Future<void> close() {
    appState.biometricStatusChangeNotifier.removeListener(listenOnBiometricStatusChanged);
    return super.close();
  }

  @visibleForTesting
  void listenOnBiometricStatusChanged() {
    /// check biometric auth again for update state
    checkBioAuthState();
  }
}
