part of 'activate_biometric_cubit.dart';

sealed class ActivateBiometricState implements BlocState {}

class ActivateBiometricInitState extends ActivateBiometricState {}

class BiometricSupported extends ActivateBiometricState {
  BiometricSupported();
}

class BiometricUnsupported extends ActivateBiometricState {
  BiometricUnsupported();
}

class BiometricActivated extends ActivateBiometricState {
  BiometricActivated();
}

class BiometricDeactivated extends ActivateBiometricState {
  BiometricDeactivated();
}

class RetrieveBiometricTokenSuccess extends ActivateBiometricState {}

class RetrieveBiometricTokenBioAuthFailure extends ActivateBiometricState {
  final BioAuthError? error;

  RetrieveBiometricTokenBioAuthFailure({this.error});
}

class RetrieveBiometricTokenFailure extends ActivateBiometricState {
  final String? errorMessage;

  RetrieveBiometricTokenFailure({this.errorMessage});
}
