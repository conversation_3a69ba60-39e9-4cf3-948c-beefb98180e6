part of 'profile_change_pin_cubit.dart';

sealed class <PERSON>ChangePinState extends BlocState {}

class ChangePinInitialState extends ProfileChangePinState {}

class ChangePinLoading extends ProfileChangePinState {}

class ChangePinAvailable extends ProfileChangePinState {
  final String sessionToken;
  final AuthChallengeType? type;

  ChangePinAvailable({
    required this.sessionToken,
    this.type = AuthChallengeType.none,
  });
}

class ChangePinLocked extends ProfileChangePinState {
  final ErrorUIModel? error;

  ChangePinLocked({required this.error});
}

class ChangePinFailure extends ProfileChangePinState {
  final ErrorUIModel error;

  ChangePinFailure({required this.error});
}
