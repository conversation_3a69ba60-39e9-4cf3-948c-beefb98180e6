import 'package:flutter/widgets.dart';

import '../../../resources/resources.dart';
import '../../../util/screen_util.dart';
import '../../../widget/buttons.dart';

class IntroductionPage extends StatelessWidget {
  final String image;
  final String description;
  final VoidCallback? onSkip;

  const IntroductionPage({
    required this.image,
    required this.description,
    required this.onSkip,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        top: 24.w,
        bottom: 16.w,
        left: EvoDimension.screenHorizontalPadding,
        right: EvoDimension.screenHorizontalPadding,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          Text(
            description,
            style: evoTextStyles.semibold(TextSize.h0, color: evoColors.white),
          ),
          EvoDimension.space24,
          Expanded(
            child: Align(
              alignment: Alignment.topCenter,
              child: evoImageProvider.asset(image, width: double.infinity, fit: BoxFit.contain),
            ),
          ),
          if (onSkip != null)
            Padding(
              padding: EdgeInsets.only(top: 24.w, bottom: 26.w),
              child: PrimaryButton(text: EvoStrings.introductionNext, onTap: onSkip),
            ),
        ],
      ),
    );
  }
}
