import 'package:flutter/material.dart';

import '../../../../resources/resources.dart';
import '../../../../util/screen_util.dart';
import '../introduction_screen.dart';

class DotIndicator extends StatelessWidget {
  final int count;
  final int currentIndex;

  const DotIndicator({
    required this.count,
    required this.currentIndex,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final List<Widget> dots = <Widget>[_buildDot(0)];

    for (int i = 1; i < count; i++) {
      dots.add(EvoDimension.space8);
      dots.add(_buildDot(i));
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: dots,
    );
  }

  Widget _buildDot(int index) {
    return AnimatedContainer(
      duration: IntroductionScreen.animationDuration,
      curve: IntroductionScreen.animationCurve,
      width: 16.w,
      height: 10.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(EvoDimension.borderRadius),
        color: currentIndex >= index ? evoColors.white : evoColors.white.withOpacity(0.2),
      ),
    );
  }
}
