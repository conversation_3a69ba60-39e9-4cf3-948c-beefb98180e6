import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/widget/refreshable_view.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../data/response/transaction_list_entity.dart';
import '../../../resources/resources.dart';
import '../../../util/functions.dart';
import '../../../util/screen_util.dart';
import '../../../widget/appbar/need_help_support_appbar.dart';
import 'cubit/transaction_group_cubit.dart';
import 'cubit/transaction_group_state.dart';
import 'widget/transaction_group_widget.dart';

class UsagePage extends PageBase {
  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.usagePage.routeName);

  const UsagePage({super.key});

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  State<UsagePage> createState() => UsagePageState();
}

class UsagePageState extends EvoPageStateBase<UsagePage> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  late final TransactionGroupCubit _cubit = TransactionGroupCubit();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _cubit.getTransactionGroups();
    });
  }

  void _listenTransactionGroupState(TransactionGroupState state) {
    if (state is TransactionGroupLoading) {
      evoUtilFunction.showHudLoading();
    } else {
      evoUtilFunction.hideHudLoading();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return buildVisibilityDetectorPage(context);
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<TransactionGroupCubit>(
      create: (_) => _cubit,
      child: Scaffold(
        appBar: NeedHelpSupportAppbar(),
        body: SafeArea(
          child: RefreshableView(
            controller: RefreshController(),
            onRefresh: () {},
            child: BlocConsumer<TransactionGroupCubit, TransactionGroupState>(
              listener: (_, TransactionGroupState state) => _listenTransactionGroupState(state),
              builder: (_, TransactionGroupState state) {
                if (state is TransactionGroupSuccess) {
                  return _buildBody(state.groups);
                }
                return const SizedBox.shrink();
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Text(
        EvoStrings.allTransactionTitle,
        style: evoTextStyles.bold(TextSize.xl2, color: evoColors.screenTitle),
      ),
    );
  }

  Widget _buildBody(List<TransactionGroupEntity> groups) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: <Widget>[
        _buildTitle(),
        Expanded(
          child: ListView.separated(
            padding: EdgeInsets.all(24.w),
            itemCount: groups.length,
            separatorBuilder: (_, __) => EvoDimension.space24,
            itemBuilder: (_, int i) => TransactionGroupWidget(group: groups[i]),
          ),
        ),
      ],
    );
  }
}
