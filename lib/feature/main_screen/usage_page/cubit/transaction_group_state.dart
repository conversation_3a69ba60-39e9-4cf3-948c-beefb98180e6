import 'package:flutter_common_package/base/bloc_state.dart';

import '../../../../data/response/transaction_list_entity.dart';

sealed class TransactionGroupState extends BlocState {}

class TransactionGroupInitial extends TransactionGroupState {}

class TransactionGroupLoading extends TransactionGroupState {}

class TransactionGroupSuccess extends TransactionGroupState {
  final List<TransactionGroupEntity> groups;

  TransactionGroupSuccess({required this.groups});
}

class TransactionGroupError extends TransactionGroupState {}
