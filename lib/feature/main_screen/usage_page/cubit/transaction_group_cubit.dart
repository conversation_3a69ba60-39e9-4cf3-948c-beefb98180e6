import 'package:flutter_common_package/common_package/common_package.dart';

import '../../../../data/response/transaction_list_entity.dart';
import 'transaction_group_state.dart';

class TransactionGroupCubit extends BlocBase<TransactionGroupState> {
  TransactionGroupCubit() : super(TransactionGroupInitial());

  Future<void> getTransactionGroups() async {
    emit(TransactionGroupLoading());
    await Future<void>.delayed(Duration(seconds: 1));
    emit(TransactionGroupSuccess(groups: <TransactionGroupEntity>[
      _fakeGroup3,
      _fakeGroup2,
      _fakeGroup1,
    ]));
  }

  late final TransactionGroupEntity _fakeGroup1 = TransactionGroupEntity(
    name: 'January 2024',
    total: 63000,
    transactions: _fakeTransactions,
  );

  late final TransactionGroupEntity _fakeGroup2 = TransactionGroupEntity(
    name: 'February 2024',
    total: 21000,
    transactions: _fakeTransactions,
  );

  late final TransactionGroupEntity _fakeGroup3 = TransactionGroupEntity(
    name: 'March 2024',
    total: 30000,
    transactions: _fakeTransactions,
  );

  late final List<TransactionGroupItemEntity> _fakeTransactions = <TransactionGroupItemEntity>[
    TransactionGroupItemEntity(
      id: '0',
      paidTo: 'Spotify',
      amount: 149.99,
      paidBy: 'via ···· 5638',
      dateTime: DateTime(2024, 3, 15),
    ),
    TransactionGroupItemEntity(
      id: '1',
      paidTo: 'Netfix.com',
      amount: 349.99,
      paidBy: 'via ···· 5638',
      dateTime: DateTime(2024, 3, 16),
    ),
    TransactionGroupItemEntity(
      id: '2',
      paidTo: 'VIU',
      amount: 249.99,
      paidBy: 'via ···· 5638',
      dateTime: DateTime(2024, 3, 16),
    ),
  ];
}
