import 'package:flutter/material.dart';

import '../../../../data/response/transaction_list_entity.dart';
import '../../../../resources/resources.dart';
import '../../../../util/screen_util.dart';
import '../../../../widget/elevated_container.dart';
import 'transaction_group_item_widget.dart';

class TransactionGroupItemListWidget extends StatelessWidget {
  final List<TransactionGroupItemEntity> items;

  const TransactionGroupItemListWidget({required this.items, super.key});

  @override
  Widget build(BuildContext context) {
    if (items.isEmpty) {
      return const SizedBox.shrink();
    }

    final List<Widget> widgets = <Widget>[
      TransactionGroupItemWidget(item: items[0]),
    ];

    for (int i = 1; i < items.length; i++) {
      widgets.add(const TransactionDivider());
      widgets.add(TransactionGroupItemWidget(item: items[i]));
    }

    if (widgets.length == 1) {
      return ElevatedContainer(child: widgets[0]);
    }

    return ElevatedContainer(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: widgets,
      ),
    );
  }
}

@visibleForTesting
class TransactionDivider extends StatelessWidget {
  const TransactionDivider({super.key});

  @override
  Widget build(BuildContext context) {
    return Divider(
      color: evoColors.greyScale60,
      height: 1,
      thickness: 1,
      indent: 16.w,
      endIndent: 16.w,
    );
  }
}
