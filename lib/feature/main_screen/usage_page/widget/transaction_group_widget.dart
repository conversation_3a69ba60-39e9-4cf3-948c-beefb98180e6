import 'package:flutter/material.dart';

import '../../../../data/response/transaction_list_entity.dart';
import '../../../../resources/dimensions.dart';
import 'transaction_group_header_widget.dart';
import 'transaction_group_item_list_widget.dart';

class TransactionGroupWidget extends StatelessWidget {
  final TransactionGroupEntity group;

  const TransactionGroupWidget({required this.group, super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        TransactionGroupHeaderWidget(name: group.name ?? '', total: group.total),
        EvoDimension.space24,
        TransactionGroupItemListWidget(items: group.transactions),
      ],
    );
  }
}
