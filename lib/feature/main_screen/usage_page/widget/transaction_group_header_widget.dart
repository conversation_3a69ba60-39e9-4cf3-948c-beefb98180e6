import 'package:flutter/material.dart';

import '../../../../resources/resources.dart';
import '../../../../util/functions.dart';

class TransactionGroupHeaderWidget extends StatelessWidget {
  final String name;
  final double? total;

  const TransactionGroupHeaderWidget({
    required this.name,
    required this.total,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: <Widget>[
        Expanded(
          child: Text(
            name,
            style: evoTextStyles.bold(TextSize.base, color: evoColors.greyScale90),
          ),
        ),
        Text(
          EvoStrings.totalMonthSpend,
          style: evoTextStyles.regular(TextSize.sm, color: evoColors.greyScale80),
        ),
        Text(
          evoUtilFunction.evoFormatCurrency(total),
          style: evoTextStyles.bold(TextSize.sm, color: evoColors.greyScale90),
        ),
      ],
    );
  }
}
