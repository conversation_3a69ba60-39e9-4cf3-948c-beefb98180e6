import 'package:flutter/material.dart';

import '../../../../data/response/transaction_list_entity.dart';
import '../../../../resources/resources.dart';
import '../../../../util/extension.dart';
import '../../../../util/functions.dart';
import '../../../../util/screen_util.dart';
import '../../../transaction_details/transaction_details_screen.dart';

class TransactionGroupItemWidget extends StatelessWidget {
  final TransactionGroupItemEntity item;

  const TransactionGroupItemWidget({required this.item, super.key});

  @override
  Widget build(BuildContext context) {
    return Material(
      borderRadius: BorderRadius.circular(8.w),
      color: evoColors.defaultWhite,
      child: InkWell(
        borderRadius: BorderRadius.circular(8.w),
        onTap: () {
          TransactionDetailsScreen.pushNamed(transactionId: item.id ?? '');
        },
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Row(
            children: <Widget>[
              _buildLeadingIcon(),
              EvoDimension.space8,
              Expanded(child: _buildBodyWidget()),
              _buildTrailingIcon(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLeadingIcon() {
    return evoImageProvider.asset(
      EvoImages.icCard,
      color: evoColors.accent90,
      fit: BoxFit.contain,
      width: 24.w,
      height: 24.w,
    );
  }

  Widget _buildTrailingIcon() {
    return evoImageProvider.asset(
      EvoImages.icArrowRight,
      color: evoColors.screenTitle,
      fit: BoxFit.contain,
      width: 20.w,
      height: 20.w,
    );
  }

  Widget _buildText(String? text) {
    if (text == null || text.isEmpty) {
      return const SizedBox.shrink();
    }

    return Text(
      text,
      style: evoTextStyles.regular(TextSize.sm, color: evoColors.textNormal),
    );
  }

  Widget _buildBodyWidget() {
    return Column(
      children: <Widget>[
        Row(
          children: <Widget>[
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(right: 8.w),
                child: _buildText(item.paidTo),
              ),
            ),
            Text(
              evoUtilFunction.evoFormatCurrency(item.amount),
              style: evoTextStyles.bold(TextSize.base, color: evoColors.textNormal),
            ),
          ],
        ),
        EvoDimension.space4,
        Row(
          children: <Widget>[
            Expanded(child: _buildText(item.dateTime?.toStringFormatDate())),
            _buildText(item.paidBy),
          ],
        ),
      ],
    );
  }
}
