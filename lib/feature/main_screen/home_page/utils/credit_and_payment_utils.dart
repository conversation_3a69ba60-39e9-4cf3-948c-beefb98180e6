import '../../../../resources/ui_strings.dart';
import '../../../../util/extension.dart';

class CreditAndPaymentUtils {
  String getNearlyDueTagText({
    required DateTime? dueDay,
  }) {
    if (dueDay == null) {
      return '';
    }

    final int remainingDays = dueDay.startOfDay().difference(DateTime.now().startOfDay()).inDays;

    if (remainingDays < 0) {
      return '';
    }

    if (remainingDays == 0) {
      return EvoStrings.payToday;
    }

    if (remainingDays == 1) {
      return EvoStrings.payTomorrow;
    }

    return getDueDateText(dueDay);
  }

  String getDueDateText(DateTime? date) {
    if (date == null) {
      return '';
    }

    return '${EvoStrings.dueDate}: ${date.toStringFormatDate()}';
  }
}
