import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../data/response/user_information_entity.dart';
import '../../../resources/resources.dart';
import '../../../util/screen_util.dart';
import '../../../widget/appbar/evo_appbar.dart';
import '../../../widget/transaction_widget/transaction_section.dart';
import '../main_screen_controller.dart';
import 'cubit/home_page_cubit.dart';
import 'widgets/credit_limit_panel.dart';
import 'widgets/icon_with_red_dot_widget.dart';
import 'widgets/inactive_card_panel.dart';
import 'widgets/payment_summary_panel/payment_summary_panel.dart';

class HomePage extends PageBase {
  final MainScreenController? mainScreenController;

  const HomePage({super.key, this.mainScreenController});

  @override
  State<HomePage> createState() => _HomePageState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.homePage.routeName);
}

class _HomePageState extends EvoPageStateBase<HomePage> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  final HomePageCubit _cubit = HomePageCubit();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _cubit.init();
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return buildVisibilityDetectorPage(context);
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
        appBar: _getAppBar(),
        body: BlocProvider<HomePageCubit>(
          create: (BuildContext context) => _cubit,
          child: ListView(
            padding: EdgeInsets.symmetric(horizontal: 24.w),
            children: <Widget>[
              _getGreetingTitle(),
              EvoDimension.space24,
              _getCreditWidgets(),
              SizedBox(height: 27.w),
              _buildRecentTransactions(),
            ],
          ),
        ));
  }

  Widget _getGreetingTitle() {
    return ValueListenableBuilder<UserInformationEntity?>(
        valueListenable: appState.userInfo,
        builder: (_, UserInformationEntity? user, __) {
          final String? userName = user?.fullName;

          if (userName == null) {
            return const SizedBox.shrink();
          }

          return Text(
            '${EvoStrings.hi}, $userName!',
            style: evoTextStyles.bold(TextSize.xl2),
          );
        });
  }

  PreferredSizeWidget _getAppBar() {
    final double appbarPaddingLeft = 24.w;
    final double leadingWidth = 77.4.w + appbarPaddingLeft;

    return EvoAppBar(
      leadingWidth: leadingWidth,
      leading: Padding(
        padding: EdgeInsets.only(left: appbarPaddingLeft),
        child: imageProvider.asset(
          EvoImages.imgBrandName,
          width: leadingWidth,
          fit: BoxFit.fitWidth,
        ),
      ),
      actions: <Widget>[
        IconWithDotRedWidget(EvoImages.icNotify, hasDotRed: true, onIconClick: () {}),
      ],
    );
  }

  Widget _getCreditWidgets() {
    return BlocBuilder<HomePageCubit, HomePageState>(
      buildWhen: (_, HomePageState state) => state is! GetTransactionsSuccess,
      builder: (_, HomePageState state) {
        List<Widget> widgets = <Widget>[];
        if (state is InactiveCardPanelState) {
          widgets = <Widget>[
            InactiveCardPanel(
              onTap: () => widget.mainScreenController?.jumpToPage(MainScreenSubPage.cards),
            ),
          ];
        } else if (state is GetCreditDataSuccess) {
          widgets = <Widget>[
            CreditLimitPanel(
              uiModel: state.creditUiModel,
            ),
            const SizedBox(height: 32),
            PaymentSummaryPanel(
              uiModel: state.paymentUiModel,
            ),
          ];
        }

        return Column(
          mainAxisSize: MainAxisSize.min,
          children: widgets,
        );
      },
    );
  }

  Widget _buildRecentTransactions() {
    return BlocBuilder<HomePageCubit, HomePageState>(
      buildWhen: (_, HomePageState state) => state is GetTransactionsSuccess,
      builder: (_, HomePageState state) {
        if (state is! GetTransactionsSuccess) {
          return const SizedBox.shrink();
        }
        return TransactionSection(
          items: state.transactions,
          onViewMoreClicked: () {
            widget.mainScreenController?.jumpToPage(MainScreenSubPage.usage);
          },
        );
      },
    );
  }
}
