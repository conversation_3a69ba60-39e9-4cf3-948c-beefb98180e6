import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../../resources/resources.dart';
import '../../../../util/screen_util.dart';
import '../../../../widget/elevated_container.dart';

class InactiveCardPanel extends StatelessWidget {
  final VoidCallback onTap;

  const InactiveCardPanel({required this.onTap, super.key});

  @override
  Widget build(BuildContext context) {
    return ElevatedContainer(
        surfaceColor: evoColors.primary,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 32.h),
          child: Row(
            children: <Widget>[
              Expanded(child: _getDescriptionColumn()),
              Si<PERSON><PERSON>ox(width: 32.w),
              evoImageProvider.asset(EvoImages.imgIntroduction1,
                  width: 100.w, height: 87.4.h, fit: BoxFit.contain),
            ],
          ),
        ));
  }

  Widget _getDescriptionColumn() {
    final ButtonStyle buttonStyle = evoButtonStyles
        .primary(
          ButtonSize.small,
          brightness: Brightness.light,
        )
        .copyWith(
            shape: const WidgetStatePropertyAll<OutlinedBorder>(StadiumBorder()),
            textStyle: WidgetStatePropertyAll<TextStyle>(evoTextStyles.bold(TextSize.sm)),
            foregroundColor: WidgetStatePropertyAll<Color>(evoColors.primary));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: <Widget>[
        Text(
          EvoStrings.activateCardDesc,
          style: evoTextStyles.bold(TextSize.base, color: evoColors.defaultWhite),
        ),
        const SizedBox(height: 16),
        CommonButton(
          onPressed: onTap,
          style: buttonStyle,
          child: const Text(EvoStrings.activeCTAText),
        )
      ],
    );
  }
}
