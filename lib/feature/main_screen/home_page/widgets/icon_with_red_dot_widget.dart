import 'package:flutter/material.dart';

import '../../../../resources/resources.dart';

class IconWithDotRedWidget extends StatefulWidget {
  final VoidCallback? onIconClick;
  final bool hasDotRed;
  final bool hasBackground;
  final double iconSize;
  final String pathIcon;
  final Color? color;

  const IconWithDotRedWidget(this.pathIcon,
      {super.key,
      this.iconSize = 24,
      this.hasDotRed = false,
      this.hasBackground = true,
      this.color,
      this.onIconClick});

  @override
  State<IconWithDotRedWidget> createState() => _IconWithDotRedWidgetState();
}

class _IconWithDotRedWidgetState extends State<IconWithDotRedWidget> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.onIconClick,
      child: Container(
          height: 48,
          width: 48,
          padding: EdgeInsets.all(widget.hasBackground ? 12 : 6),
          decoration: widget.hasBackground
              ? BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: evoColors.foreground.withOpacity(0.04))
              : null,
          child: _itemBody()),
    );
  }

  Widget _itemBody() => Stack(children: <Widget>[
        Center(
            child: evoImageProvider.asset(widget.pathIcon,
                width: widget.iconSize, fit: BoxFit.fitWidth, color: widget.color)),
        if (widget.hasDotRed) Align(alignment: Alignment.topRight, child: _itemDot())
      ]);

  Widget _itemDot() {
    final Widget dotRed = Container(
        height: 12,
        width: 12,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(90), color: evoColors.error));
    return widget.hasBackground
        ? dotRed
        : Container(
            decoration:
                BoxDecoration(borderRadius: BorderRadius.circular(90), color: evoColors.background),
            padding: const EdgeInsets.all(1),
            child: dotRed,
          );
  }
}
