part of 'payment_summary_panel.dart';

class PaymentSummaryBody extends StatelessWidget {
  final PaymentSummaryUiModel uiModel;

  const PaymentSummaryBody({
    required this.uiModel,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final Widget totalAmountItem = getTotalAmountItem(uiModel);
    final Widget paidAmountItem = getPaidAmountItem(uiModel);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: <Widget>[
        totalAmountItem,
        paidAmountItem,
      ],
    );
  }

  Widget getTotalAmountItem(PaymentSummaryUiModel uiModel) {
    final String title = uiModel.status == PaymentStatus.forceClosed
        ? EvoStrings.foreClosedTitle
        : EvoStrings.totalAmountTitle;

    return PaymentAmountItem(
      title: title,
      amount: uiModel.totalAmount,
    );
  }

  Widget getPaidAmountItem(PaymentSummaryUiModel uiModel) {
    if (uiModel.status == PaymentStatus.forceClosed) {
      return const SizedBox.shrink();
    }

    String title = EvoStrings.paidAmountTitle;

    if (uiModel.status == PaymentStatus.paid) {
      title = EvoStrings.lastAmountPaidTitle;
      return PaymentAmountItem(
        title: title,
        amount: uiModel.paidAmount ?? 0,
      );
    }

    return PaymentAmountItem(
      title: title,
      amount: uiModel.minAmount ?? 0,
    );
  }
}
