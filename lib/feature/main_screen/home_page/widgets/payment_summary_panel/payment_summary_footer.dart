import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../../../resources/resources.dart';
import '../../../../../util/screen_util.dart';
import '../../models/payment_summary_ui_model.dart';

class PaymentSummaryFooter extends StatelessWidget {
  final PaymentStatus status;

  const PaymentSummaryFooter({required this.status, super.key});

  @override
  Widget build(BuildContext context) {
    if (status == PaymentStatus.paid) {
      return const SizedBox.shrink();
    }

    return Column(
      children: <Widget>[
        SizedBox(height: 16.h),
        _getDivider(),
        SizedBox(height: 16.h),
        Align(
          alignment: Alignment.centerRight,
          child: _getCTA(),
        )
      ],
    );
  }

  Widget _getDivider() => Divider(
        color: evoColors.greyScale60,
        height: 0,
      );

  Widget _getCTA() => CommonButton(
        onPressed: () {},
        style: evoButtonStyles.utility(ButtonSize.small),
        child: Row(mainAxisSize: MainAxisSize.min, children: <Widget>[
          evoImageProvider.asset(
            EvoImages.icBanknotes,
            height: 20.h,
            width: 20.w,
            color: evoColors.primary,
          ),
          SizedBox(width: 8.w),
          const Text(EvoStrings.payNow)
        ]),
      );
}
