part of 'payment_summary_panel.dart';

class PaymentSummaryHeader extends StatelessWidget {
  final PaymentSummaryUiModel uiModel;

  const PaymentSummaryHeader({
    required this.uiModel,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: <Widget>[
        _getTitleWidget(),
        Sized<PERSON><PERSON>(height: 16.h),
        PaymentDueTag.fromUiModel(uiModel),
      ],
    );
  }

  Widget _getTitleWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          EvoStrings.amountToPayTitle,
          style: evoTextStyles.bold(TextSize.base, color: evoColors.greyScale90),
        ),
        Text(EvoStrings.amountToPayDesc, style: evoTextStyles.regular(TextSize.xs)),
      ],
    );
  }
}
