import 'package:flutter/material.dart';

import '../../../../../resources/resources.dart';
import '../../models/payment_summary_ui_model.dart';
import '../../utils/credit_and_payment_utils.dart';

class PaymentDueTag extends StatelessWidget {
  final PaymentStatus status;
  final DateTime? dueDay;

  const PaymentDueTag({
    required this.status,
    this.dueDay,
    super.key,
  });

  PaymentDueTag.fromUiModel(PaymentSummaryUiModel uiModel, {Key? key})
      : this(
          status: uiModel.status,
          dueDay: uiModel.dueDay,
          key: key,
        );

  @override
  Widget build(BuildContext context) {
    final String text = _getText();
    final Color surfaceColor = _getTagColor();

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
      decoration: ShapeDecoration(
        shape: RoundedRectangleBorder(
          side: BorderSide(color: surfaceColor),
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: Text(
        text,
        style: evoTextStyles.bold(
          TextSize.xs,
          color: surfaceColor,
        ),
      ),
    );
  }

  Color _getTagColor() {
    return switch (status) {
      PaymentStatus.overdue || PaymentStatus.forceClosed => evoColors.error,
      PaymentStatus.nearlyDue => evoColors.warning100,
      PaymentStatus.preDue => evoColors.accent90,
      PaymentStatus.paid => evoColors.primary,
    };
  }

  String _getText() {
    final CreditAndPaymentUtils utils = CreditAndPaymentUtils();

    return switch (status) {
      PaymentStatus.overdue || PaymentStatus.forceClosed => EvoStrings.overDue,
      PaymentStatus.nearlyDue => utils.getNearlyDueTagText(dueDay: dueDay),
      PaymentStatus.preDue => utils.getDueDateText(dueDay),
      PaymentStatus.paid => EvoStrings.paid,
    };
  }
}
