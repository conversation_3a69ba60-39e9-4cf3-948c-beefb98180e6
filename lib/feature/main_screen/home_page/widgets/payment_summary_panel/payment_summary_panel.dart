import 'package:flutter/material.dart';

import '../../../../../resources/resources.dart';
import '../../../../../util/functions.dart';
import '../../../../../util/screen_util.dart';
import '../../../../../widget/elevated_container.dart';
import '../../models/payment_summary_ui_model.dart';
import 'payment_due_tag.dart';
import 'payment_summary_footer.dart';

part 'payment_amount_item.dart';
part 'payment_summary_body.dart';
part 'payment_summary_header.dart';

class PaymentSummaryPanel extends StatelessWidget {
  final PaymentSummaryUiModel uiModel;

  const PaymentSummaryPanel({
    required this.uiModel,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedContainer(
      child: Padding(
        padding: EdgeInsets.symmetric(
          vertical: 17.h,
          horizontal: 19.w,
        ),
        child: Column(
          children: <Widget>[
            PaymentSummaryHeader(uiModel: uiModel),
            const SizedBox(height: 16),
            PaymentSummaryBody(uiModel: uiModel),
            PaymentSummaryFooter(status: uiModel.status),
          ],
        ),
      ),
    );
  }
}
