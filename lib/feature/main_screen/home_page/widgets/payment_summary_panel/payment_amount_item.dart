part of 'payment_summary_panel.dart';

class PaymentAmountItem extends StatelessWidget {
  final String title;
  final double amount;

  const PaymentAmountItem({
    required this.title,
    required this.amount,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Flexible(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          _getTitle(title),
          Text(
            evoUtilFunction.evoFormatCurrency(amount),
            style: evoTextStyles.bold(TextSize.base, color: evoColors.textNormal),
          ),
        ],
      ),
    );
  }

  Widget _getTitle(String title) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        Flexible(
            child: Text(
          title,
          style: evoTextStyles.regular(TextSize.sm, color: evoColors.textNormal),
        )),
        SizedBox(width: 4.w),
        evoImageProvider.asset(
          EvoImages.icInfoBanner,
          width: 16.w,
          height: 16.w,
        )
      ],
    );
  }
}
