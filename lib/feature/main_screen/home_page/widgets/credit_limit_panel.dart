import 'package:flutter/material.dart';

import '../../../../resources/resources.dart';
import '../../../../util/extension.dart';
import '../../../../util/functions.dart';
import '../../../../util/screen_util.dart';
import '../../../../widget/elevated_container.dart';
import '../models/credit_limit_ui_model.dart';

class CreditLimitPanel extends StatelessWidget {
  final CreditLimitUiModel uiModel;

  const CreditLimitPanel({
    required this.uiModel,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    const SizedBox spacing4 = SizedBox(height: 4);

    return ElevatedContainer(
        surfaceColor: evoColors.primary,
        child: Padding(
          padding: EdgeInsets.all(19.0.w),
          child: Column(
            children: <Widget>[
              _getTitleRow(),
              spacing4,
              _getCreditInfoRow(uiModel),
              spacing4,
              LinearProgressIndicator(
                value: uiModel.availableCredit / uiModel.totalCredit,
                color: evoColors.primary00,
              ),
              spacing4,
              _getDescription(uiModel),
            ],
          ),
        ));
  }

  Widget _getTitleRow() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: <Widget>[
        _getTitle(EvoStrings.availableCredit),
        _getTitle(EvoStrings.cutOffDate),
      ],
    );
  }

  Widget _getCreditInfoRow(CreditLimitUiModel uiModel) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          evoUtilFunction.evoFormatCurrency(uiModel.availableCredit),
          style: evoTextStyles.bold(
            TextSize.xl2,
            color: evoColors.defaultWhite,
          ),
        ),
        Text(
          uiModel.cutOffDate.toStringFormatDate(),
          style: evoTextStyles.bold(
            TextSize.base,
            color: evoColors.defaultWhite,
          ),
        )
      ],
    );
  }

  Widget _getTitle(String title) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        Text(
          title,
          style: evoTextStyles.regular(TextSize.sm, color: evoColors.defaultWhite),
        ),
        const SizedBox(width: 4),
        evoImageProvider.asset(
          EvoImages.icInfoFilled,
          width: 16.w,
          height: 16.w,
        )
      ],
    );
  }

  Widget _getDescription(CreditLimitUiModel uiModel) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Text.rich(TextSpan(
          children: <InlineSpan>[
            const TextSpan(text: '${EvoStrings.creditLimitProgressPrefix} '),
            TextSpan(
                text: evoUtilFunction.evoFormatCurrency(uiModel.totalCredit),
                style: evoTextStyles.bold(
                  TextSize.sm,
                  color: evoColors.defaultWhite,
                )),
            const TextSpan(text: ' ${EvoStrings.creditLimitProgressSuffix}'),
          ],
          style: evoTextStyles.regular(
            TextSize.sm,
            color: evoColors.defaultWhite,
          ))),
    );
  }
}
