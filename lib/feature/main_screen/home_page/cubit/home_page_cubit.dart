import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';

import '../../../../widget/transaction_widget/transaction_section.dart';
import '../../card_page/cubits/cards_page_cubit.dart';
import '../../card_page/models/transaction_model.dart';
import '../models/credit_limit_ui_model.dart';
import '../models/payment_summary_ui_model.dart';

part 'home_page_state.dart';

class HomePageCubit extends CommonCubit<HomePageState> {
  HomePageCubit() : super(InitialHomePageState());

  Future<void> getCreditAndPaymentInfo() async {
    /// TODO: hoang-nguyen2 remove mock after integrate api
    emit(GetCreditDataSuccess(
      paymentUiModel: _geMockPaymentUiModel(),
      creditUiModel: _getMockCreditLimitUiModel(),
    ));
    await Future<void>.delayed(const Duration(seconds: 1));
    getTransactions();
  }

  /// TODO: update with API integration
  Future<void> getTransactions() async {
    emit(GetTransactionsSuccess(TransactionSection.mockData));
  }

  CreditLimitUiModel _getMockCreditLimitUiModel() {
    /// TODO: hoang-nguyen2 remove mock after integrate api
    return CreditLimitUiModel(
      availableCredit: 40,
      totalCredit: 100,
      cutOffDate: DateTime.now(),
    );
  }

  PaymentSummaryUiModel _geMockPaymentUiModel() {
    /// TODO: hoang-nguyen2 remove mock after integrate api
    return PaymentSummaryUiModel(
      totalAmount: 10000,
      paidAmount: 10,
      minAmount: 20,
      dueDay: DateTime.now().add(const Duration(days: 3)),
      status: PaymentStatus.preDue,
    );
  }

  // TODO: Remove after Demo phase
  Future<void> init() async {
    cardData.addListener(getCreditAndPaymentInfo);

    emit(InactiveCardPanelState());
    await Future<void>.delayed(const Duration(milliseconds: 500));
    emit(GetTransactionsSuccess(<TransactionModel>[]));
  }

  // TODO: Remove after Demo phase
  @override
  Future<void> close() async {
    cardData.removeListener(getCreditAndPaymentInfo);
    await super.close();
  }
}
