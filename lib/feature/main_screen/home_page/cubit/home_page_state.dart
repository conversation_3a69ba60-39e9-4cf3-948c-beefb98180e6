part of 'home_page_cubit.dart';

sealed class HomePageState extends BlocState {}

class InitialHomePageState extends HomePageState {}

class InactiveCardPanelState extends HomePageState {}

class GetCreditDataSuccess extends HomePageState {
  PaymentSummaryUiModel paymentUiModel;
  CreditLimitUiModel creditUiModel;

  GetCreditDataSuccess({
    required this.paymentUiModel,
    required this.creditUiModel,
  });
}

class GetTransactionsSuccess extends HomePageState {
  final List<TransactionModel> transactions;

  GetTransactionsSuccess(this.transactions);
}
