import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../resources/resources.dart';


class PayCardPage extends PageBase {
  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.payCardPage.routeName);

  const PayCardPage({super.key});

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  State<PayCardPage> createState() => PayCardPageState();
}

class PayCardPageState extends EvoPageStateBase<PayCardPage> with AutomaticKeepAliveClientMixin {

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return buildVisibilityDetectorPage(context);
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      backgroundColor: evoColors.background,
      body: Safe<PERSON>rea(
        child: Center(
          child: Text('Pay card page', style: evoTextStyles.bold(TextSize.base)),
        )
      ),
    );
  }
}
