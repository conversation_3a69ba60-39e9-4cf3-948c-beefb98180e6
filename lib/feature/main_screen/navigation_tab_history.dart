import 'main_screen_controller.dart';

class NavigationHistoryStack {
  static const int defaultSizeOfStack = 1;
  final int size;
  final List<MainScreenSubPage> _history = <MainScreenSubPage>[];
  final MainScreenSubPage defaultPage;

  NavigationHistoryStack({required this.defaultPage, this.size = defaultSizeOfStack})
      : assert(
          size >= defaultSizeOfStack,
          'size must be greater than $defaultSizeOfStack',
        );

  void push(MainScreenSubPage value) {
    if (size == 0) {
      return;
    }

    if (_preventAddValueSameAsPeekValue(value)) {
      return;
    }

    if (_history.length >= size) {
      _history.removeAt(0);
    }

    _history.add(value);
  }

  MainScreenSubPage pop() {
    if (_history.isNotEmpty) {
      _history.removeLast();
    }

    if (_history.isEmpty) {
      return defaultPage;
    } else {
      return top;
    }
  }

  MainScreenSubPage get top => _history.last;

  MainScreenSubPage get first => _history.first;

  bool get isEmpty => _history.isEmpty;

  int get length => _history.length;

  bool _preventAddValueSameAsPeekValue(MainScreenSubPage value) {
    return _history.isNotEmpty && top == value;
  }
}
