import 'package:flutter/material.dart';

/// [FeaturesWithDialogDisplay] defines the features that may display a dialog.
/// The WelcomeBackDialog must be shown after all such features have been processed.
enum FeaturesWithDialogDisplay {
  forceUpdate,
  requestActiveBiometric,
}

class MainScreenDialogHandler {
  /// The WelcomeBackDialog must be shown after all features requiring dialog display have been processed.
  /// The [features] parameter is used to verify that all features have been processed.
  @visibleForTesting
  final Set<FeaturesWithDialogDisplay> features = <FeaturesWithDialogDisplay>{
    FeaturesWithDialogDisplay.forceUpdate,
    FeaturesWithDialogDisplay.requestActiveBiometric,
  };

  bool get isAllFeaturesWithDialogProcessed => features.isEmpty;

  void handleFeatureProcessed(FeaturesWithDialogDisplay feature) {
    features.remove(feature);
  }
}
