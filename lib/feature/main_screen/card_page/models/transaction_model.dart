import 'package:flutter_common_package/util/functions.dart';

/// TODO nam-pham-ts this model will be updated when the API is ready
class TransactionModel {
  final String merchantName;
  final String purchaseAt;
  final double amount;
  final String fourLastDigitOfCardNumberUsed;

  TransactionModel({
    required this.merchantName,
    required this.purchaseAt,
    required this.amount,
    required this.fourLastDigitOfCardNumberUsed,
  });

  DateTime? get purchaseAtDateTime => commonUtilFunction.toDateTime(purchaseAt);
}