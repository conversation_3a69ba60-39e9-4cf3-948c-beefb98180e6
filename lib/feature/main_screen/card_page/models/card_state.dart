enum CardState {
  /// card is ACTIVE, doesn't show full information
  active,

  /// card is INACTIVE
  inactive,

  /// card is FRO<PERSON><PERSON>
  frozen,

  /// new card has been replaced and initially in inactive status
  replaced,

  /// card is blocked by user
  userBlocked,

  /// card is blocked by bank
  bankBlocked;

  bool isCardInactivated() {
    return this == CardState.inactive || this == CardState.replaced;
  }

  bool isCardBlocked() {
    return this == CardState.userBlocked || this == CardState.bankBlocked;
  }

  bool isCardFreeze() {
    return this == CardState.frozen;
  }
}