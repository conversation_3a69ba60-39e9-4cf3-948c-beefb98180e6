import '../widgets/card_widget.dart';
import 'card_information_model.dart';
import 'card_state.dart';

class CardModel {
  CardType type;
  CardDetailsVisibility visibility;
  CardState state;
  CardInformationModel? cardInformation;

  CardModel({
    required this.type,
    required this.visibility,
    required this.state,
    this.cardInformation,
  });

  CardModel copyWith({
    CardType? type,
    CardDetailsVisibility? visibility,
    CardState? state,
    CardInformationModel? cardInformation,
  }) {
    return CardModel(
      type: type ?? this.type,
      visibility: visibility ?? this.visibility,
      state: state ?? this.state,
      cardInformation: cardInformation ?? this.cardInformation,
    );
  }
}
