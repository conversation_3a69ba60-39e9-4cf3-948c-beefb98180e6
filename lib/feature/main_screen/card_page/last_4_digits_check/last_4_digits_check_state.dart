import 'package:flutter_common_package/base/bloc_state.dart';

abstract class Last4DigitsCheckState implements BlocState {}

class Last4DigitsCheckInitial extends Last4DigitsCheckState {}

class Last4DigitsCheckLoading extends Last4DigitsCheckState {}

class Last4DigitsCheckSuccess extends Last4DigitsCheckState {}

class Last4DigitsCheckFailure extends Last4DigitsCheckState {
  final String error;

  Last4DigitsCheckFailure({required this.error});
}
