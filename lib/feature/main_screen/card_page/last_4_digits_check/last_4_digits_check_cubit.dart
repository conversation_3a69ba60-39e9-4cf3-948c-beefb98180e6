import 'package:flutter_common_package/base/common_cubit.dart';

import 'last_4_digits_check_state.dart';

class Last4DigitsCheckCubit extends CommonCubit<Last4DigitsCheckState> {
  Last4DigitsCheckCubit() : super(Last4DigitsCheckInitial());

  // TODO: update after API integration
  Future<void> check(String digits) async {
    emit(Last4DigitsCheckLoading());
    await Future<void>.delayed(const Duration(seconds: 1));
    if (digits == '1234') {
      emit(Last4DigitsCheckFailure(error: 'Error messages here'));
    } else {
      emit(Last4DigitsCheckSuccess());
    }
  }

  void onInputChange(String input) {
    if (input.isEmpty) {
      emit(Last4DigitsCheckInitial());
    }
  }
}
