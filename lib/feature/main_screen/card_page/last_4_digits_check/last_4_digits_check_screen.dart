import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:go_router/go_router.dart';

import '../../../../base/evo_page_state_base.dart';
import '../../../../resources/resources.dart';
import '../../../../util/functions.dart';
import '../../../../util/screen_util.dart';
import '../../../../widget/appbar/evo_support_appbar.dart';
import '../../../../widget/evo_pin_text_field.dart';
import 'last_4_digits_check_cubit.dart';
import 'last_4_digits_check_state.dart';

class Last4DigitsCheckScreenArg extends PageBaseArg {
  final VoidCallback onSuccess;

  Last4DigitsCheckScreenArg({required this.onSuccess});
}

class Last4DigitsCheckScreen extends PageBase {
  final VoidCallback onSuccess;

  const Last4DigitsCheckScreen({required this.onSuccess, super.key});

  static void pushNamed({required VoidCallback onSuccess}) {
    navigatorContext?.pushNamed(
      Screen.last4DigitsCheckScreen.name,
      extra: Last4DigitsCheckScreenArg(onSuccess: onSuccess),
    );
  }

  @override
  State<Last4DigitsCheckScreen> createState() => _Last4DigitsCheckScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.last4DigitsCheckScreen.routeName);
}

class _Last4DigitsCheckScreenState extends EvoPageStateBase<Last4DigitsCheckScreen> {
  late final Last4DigitsCheckCubit _cubit = Last4DigitsCheckCubit();

  final TextEditingController _controller = TextEditingController();

  void _listenState(Last4DigitsCheckState state) {
    if (state is Last4DigitsCheckLoading) {
      evoUtilFunction.showHudLoading();
      return;
    }
    evoUtilFunction.hideHudLoading();

    if (state is Last4DigitsCheckSuccess) {
      navigatorContext?.pop();
      widget.onSuccess();
    }
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<Last4DigitsCheckCubit>(
      create: (_) => _cubit,
      child: BlocListener<Last4DigitsCheckCubit, Last4DigitsCheckState>(
        listener: (_, Last4DigitsCheckState state) => _listenState(state),
        child: Scaffold(
          appBar: EvoSupportAppbar(),
          body: SafeArea(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 24.w),
              child: Column(
                children: <Widget>[
                  Text(
                    EvoStrings.last4DigitsCheckTitle,
                    style: evoTextStyles.bold(TextSize.xl2, color: evoColors.screenTitle),
                  ),
                  EvoDimension.space4,
                  Text(
                    EvoStrings.last4DigitsCheckDesc,
                    style: evoTextStyles.regular(TextSize.base, color: evoColors.textActive),
                  ),
                  EvoDimension.space24,
                  _buildLastDigitsTextField(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLastDigitsTextField() {
    return BlocBuilder<Last4DigitsCheckCubit, Last4DigitsCheckState>(
      buildWhen: (_, Last4DigitsCheckState state) => state is! Last4DigitsCheckLoading,
      builder: (_, Last4DigitsCheckState state) {
        final String? error = state is Last4DigitsCheckFailure ? state.error : null;

        return EvoPinTextField(
          textEditingController: _controller,
          pinLength: 4,
          errorMessage: error,
          onChange: _cubit.onInputChange,
          onSubmit: _cubit.check,
        );
      },
    );
  }
}
