import 'package:flutter_common_package/common_package/common_package.dart';

import '../../../../widget/transaction_widget/transaction_section.dart';
import '../models/card_model.dart';
import 'recent_transactions_state.dart';

class RecentTransactionsCubit extends BlocBase<RecentTransactionsState> {
  RecentTransactionsCubit() : super(RecentTransactionsInitial());

  // TODO: integrate API
  Future<void> getTransactions(CardModel card) async {
    emit(RecentTransactionsLoading());
    await Future<void>.delayed(Duration(milliseconds: 500));
    emit(RecentTransactionsSuccess(transactions: TransactionSection.mockData));
  }

  void reset() {
    emit(RecentTransactionsInitial());
  }
}
