import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../privilege_action/privilege_access_guard_module.dart';
import '../../../privilege_action/privilege_action_handler/get_card_information_handler.dart';
import '../../../privilege_action/privilege_action_handler/privilege_action_factory.dart';
import '../../../privilege_action/privilege_action_handler/unfreeze_card_handler.dart';
import '../models/card_information_model.dart';
import '../models/card_model.dart';
import '../models/card_state.dart';
import '../widgets/card_widget.dart';

part 'cards_page_state.dart';

class CardsPageCubit extends CommonCubit<CardsPageState> implements PrivilegeAccessGuardCallback {
  final PrivilegeAccessGuardModule privilegeAccessGuardModule;

  @visibleForTesting
  CardDetailsVisibility visibility = CardDetailsVisibility.hidden;

  @visibleForTesting
  CardType cardType = CardType.virtual;

  CardsPageCubit({required this.privilegeAccessGuardModule}) : super(CardPageInitial());

  Future<void> initializeCardType({required CardType cardType}) async {
    this.cardType = cardType;
    emit(CardInfoLoading());
    await Future<void>.delayed(const Duration(milliseconds: 500));
    _updateCard();
  }

  // TODO: Remove after Demo phase
  void _updateCard([CardModel Function(CardModel)? update]) {
    final CardModel current = cardData.get(cardType);
    final CardModel updated = update?.call(current) ?? current;
    cardData.set(updated);
    emit(CardInfoLoaded(cardModel: updated.copyWith(visibility: visibility)));
  }

  void toggleCardDetailsVisibility() {
    if (visibility == CardDetailsVisibility.visible) {
      visibility = CardDetailsVisibility.hidden;
      _updateCard();
    } else {
      privilegeAccessGuardModule.confirm(
          callback: this, type: PrivilegeActionType.showCardInformation);
    }
  }

  void hideCardDetails() {
    visibility = CardDetailsVisibility.hidden;
    _updateCard();
  }

  @override
  void onPrivilegeActionSuccess(dynamic response) {
    if (response is GetCardInformationResponse) {
      final CardInformationModel cardInformationModel = CardInformationModel(
        cardNumber: response.cardNumber,
        cardHolderName: response.cardHolderName,
        expiryDate: response.cardExpiryDate,
        cvv: response.cvv,
      );
      visibility = CardDetailsVisibility.visible;
      _updateCard((CardModel card) => card.copyWith(cardInformation: cardInformationModel));
      return;
    }
    if (response is UnfreezeCardActionData) {
      _updateCard((CardModel card) => card.copyWith(state: CardState.active));
      return;
    }
  }

  @override
  void onPrivilegeActionError(ErrorUIModel error) {
    /// TODO should update another state
    emit(CardActivateFailure(error: error));
  }

  void onVerifyOtpSuccess() {
    if (cardType == CardType.physical) {
      emit(CardCheckingLast4Digits());
    } else {
      emit(CardActivateSuccess(cardType: CardType.virtual));
      _updateCard((CardModel card) => card.copyWith(state: CardState.active));
    }
  }

  void onCheckLast4DigitsSuccess() {
    emit(CardActivateSuccess(cardType: CardType.physical));
    _updateCard((CardModel card) => card.copyWith(state: CardState.active));
  }

  /// TODO: update this when integrating with BE
  Future<void> toggleCardFreezeStatus() async {
    final CardsPageState current = this.state;
    if (current is! CardInfoLoaded) {
      return;
    }
    final CardState state = current.cardModel.state;

    if (state == CardState.active) {
      if (cardType == CardType.physical) {
        emit(CardFreezeFailure());
        return;
      }
      emit(CardFreezeLoading());
      await Future<void>.delayed(const Duration(seconds: 1));
      _updateCard((CardModel card) => card.copyWith(state: CardState.frozen));
    } else if (state.isCardFreeze()) {
      privilegeAccessGuardModule.confirm(callback: this, type: PrivilegeActionType.unfreezeCard);
    }
  }
}

final CardData cardData = CardData();

// TODO: Remove after Demo phase
class CardData extends ValueNotifier<bool> {
  CardData() : super(false);

  CardModel _virtual = CardModel(
    type: CardType.virtual,
    visibility: CardDetailsVisibility.hidden,
    state: CardState.inactive,
    cardInformation: CardInformationModel.mockCardInformation,
  );

  CardModel _physical = CardModel(
    type: CardType.physical,
    visibility: CardDetailsVisibility.hidden,
    state: CardState.replaced,
    cardInformation: CardInformationModel.mockCardInformation,
  );

  CardModel get(CardType type) {
    return type == CardType.virtual ? _virtual : _physical;
  }

  void set(CardModel card) {
    if (card.type == CardType.virtual) {
      _virtual = card;
      _notifyVirtualCardState();
    } else {
      _physical = card;
    }
  }

  void _notifyVirtualCardState() {
    if (_virtual.state == CardState.active) {
      value = true;
    }
  }
}
