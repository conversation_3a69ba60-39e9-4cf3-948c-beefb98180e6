import 'package:flutter_common_package/base/bloc_state.dart';

import '../models/transaction_model.dart';

sealed class RecentTransactionsState extends BlocState {}

class RecentTransactionsInitial extends RecentTransactionsState {}

class RecentTransactionsLoading extends RecentTransactionsState {}

class RecentTransactionsSuccess extends RecentTransactionsState {
  final List<TransactionModel> transactions;

  RecentTransactionsSuccess({required this.transactions});
}
