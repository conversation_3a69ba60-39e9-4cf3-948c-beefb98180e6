part of 'cards_page_cubit.dart';

sealed class CardsPageState extends BlocState {}

class CardPageInitial extends CardsPageState {}

class CardInfoLoading extends CardsPageState {}

class CardInfoLoaded extends CardsPageState {
  final CardModel cardModel;

  CardInfoLoaded({required this.cardModel});
}

class Card<PERSON>heckingLast4Digits extends CardsPageState {}

class CardActivateSuc<PERSON> extends CardsPageState {
  final CardType cardType;

  CardActivateSuccess({required this.cardType});
}

class CardActivateFailure extends CardsPageState {
  final ErrorUIModel error;

  CardActivateFailure({required this.error});
}

class ShowCardInfoDetailedError extends CardsPageState {
  final ErrorUIModel error;

  ShowCardInfoDetailedError({required this.error});
}

class CardFreezeLoading extends CardsPageState {}

class CardFreezeFailure extends CardsPageState {}