import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/util/device_info_plugin_wrapper/device_info_plugin_wrapper.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/resources.dart';
import '../../../../util/evo_snackbar.dart';
import '../../../../util/functions.dart';
import '../../../../util/screen_util.dart';
import '../../../../widget/elevated_container.dart';
import '../../../copy_to_clipboard/copy_to_clipboard_cubit.dart';
import '../../../copy_to_clipboard/copy_to_clipboard_util.dart';
import '../models/card_information_model.dart';
import 'card_widget.dart';

class ActiveCardWidget extends StatefulWidget {
  final CardInformationModel cardInformationModel;
  final CardDetailsVisibility visibility;
  final CardType cardType;
  final void Function()? onClickOnClipBoard;

  const ActiveCardWidget({
    required this.cardInformationModel,
    required this.visibility,
    required this.cardType,
    this.onClickOnClipBoard,
    super.key,
  });

  @override
  State createState() => _ActiveCardWidgetState();
}

class _ActiveCardWidgetState extends State<ActiveCardWidget> {
  final CopyToClipboardCubit _clipboardCubit = CopyToClipboardCubit();
  final CopyToClipboardUtil _copyToClipboardUtil = CopyToClipboardUtil(
    deviceInfoPluginWrapper: getIt.get<DeviceInfoPluginWrapper>(),
    evoSnackBar: getIt.get<EvoSnackBar>(),
  );

  @override
  Widget build(BuildContext context) {
    final String hiddenBackgroundImage =
        _getHiddenBackgroundImageCardType(cardType: widget.cardType);
    final String detailsBackgroundImage =
        _getDetailsBackgroundImageCardType(cardType: widget.cardType);
    return BlocProvider<CopyToClipboardCubit>(
      create: (BuildContext context) => _clipboardCubit,
      child: BlocListener<CopyToClipboardCubit, CopyToClipBoardState>(
        listener: _onHandleCopyToClipboardState,
        child: AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            transitionBuilder: (Widget child, Animation<double> animation) {
              return FadeTransition(opacity: animation, child: child);
            },
            child: widget.visibility == CardDetailsVisibility.visible
                ? _frameWithVisibleDetail(
                    key: ValueKey<CardDetailsVisibility>(widget.visibility),
                    backgroundImage: detailsBackgroundImage)
                : _frameWithHiddenDetail(
                    key: ValueKey<CardDetailsVisibility>(widget.visibility),
                    backgroundImage: hiddenBackgroundImage,
                  ) // Empty container when hidden
            ),
      ),
    );
  }

  Widget _frameWithHiddenDetail({required Key key, required String backgroundImage}) {
    return Stack(
      key: key,
      children: <Widget>[
        // Background image
        Positioned.fill(
          child: evoImageProvider.asset(
            backgroundImage,
            fit: BoxFit.fill,
          ),
        ),
        Positioned(
          left: 1,
          bottom: 1,
          child: Padding(
            padding: EdgeInsets.only(left: 24.w, bottom: 14.h),
            child: Text(
              /// TODO nam-pham-ts update this text when integrate with real data
              EvoStrings.maskTransactionCardNumber.replaceVariableByValue(
                <String>['1234' ?? ''],
              ),
              style: evoTextStyles.bold(
                TextSize.base,
                color: evoColors.defaultWhite,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _frameWithVisibleDetail({required Key key, required String backgroundImage}) {
    return Stack(
      key: key,
      children: <Widget>[
        // Background image
        Positioned.fill(
          child: evoImageProvider.asset(
            backgroundImage,
            fit: BoxFit.fill,
          ),
        ),
        Positioned(
          top: 1,
          right: 1,
          child: Padding(
            padding: EdgeInsets.only(top: 26.h, right: 17.w),
            child: Text(
              _getCardTitleOfCardType(cardType: widget.cardType),
              style: evoTextStyles.regular(
                TextSize.base,
                color: evoColors.defaultWhite,
              ),
            ),
          ),
        ),
        Positioned(
          top: 1,
          left: 1,
          child: Padding(
            padding: EdgeInsets.only(top: 63.h, left: 24.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                _buildCardNumberSection(),
                SizedBox(height: 8.h),
                _buildExpiryDateAndCVVSection(),
                SizedBox(height: 8.h),
                _buildCardHolderNameSection(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCardNumberSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          EvoStrings.cardNumber,
          style: evoTextStyles.regular(
            TextSize.sm,
            color: _getTitleColor(cardType: widget.cardType),
          ),
        ),
        Row(
          children: <Widget>[
            Text(
              widget.cardInformationModel.cardNumber,
              style: evoTextStyles.bold(
                TextSize.xl,
                color: evoColors.defaultWhite,
              ),
            ),
            SizedBox(width: 8.w),
            ElevatedContainer(
              surfaceColor: evoColors.defaultTransparent,
              onTap: () {
                _clipboardCubit.copyToClipboard(widget.cardInformationModel.cardNumber);
              },
              child: evoImageProvider.asset(
                EvoImages.icCopy,
                width: 24.w,
                height: 24.w,
                fit: BoxFit.fill,
              ),
            )
          ],
        ),
      ],
    );
  }

  Widget _buildExpiryDateAndCVVSection() {
    return Row(
      children: <Widget>[
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              EvoStrings.expiryDate,
              style: evoTextStyles.regular(
                TextSize.sm,
                color: _getTitleColor(cardType: widget.cardType),
              ),
            ),
            Text(
              widget.cardInformationModel.expiryDate,
              style: evoTextStyles.bold(
                TextSize.base,
                color: evoColors.defaultWhite,
              ),
            ),
          ],
        ),
        const SizedBox(
          width: 30,
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              EvoStrings.cvv,
              style: evoTextStyles.regular(
                TextSize.sm,
                color: _getTitleColor(cardType: widget.cardType),
              ),
            ),
            Text(
              widget.cardInformationModel.cvv,
              style: evoTextStyles.bold(
                TextSize.base,
                color: evoColors.defaultWhite,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCardHolderNameSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          EvoStrings.nameOnCard,
          style: evoTextStyles.regular(
            TextSize.sm,
            color: _getTitleColor(cardType: widget.cardType),
          ),
        ),
        Text(
          widget.cardInformationModel.cardHolderName,
          style: evoTextStyles.bold(
            TextSize.base,
            color: evoColors.defaultWhite,
          ),
        ),
      ],
    );
  }

  String _getHiddenBackgroundImageCardType({required CardType cardType}) {
    return cardType == CardType.virtual ? EvoImages.frameVirtualCard : EvoImages.framePhysicalCard;
  }

  String _getDetailsBackgroundImageCardType({required CardType cardType}) {
    return cardType == CardType.virtual
        ? EvoImages.frameVirtualCardDetails
        : EvoImages.framePhysicalCardDetails;
  }

  String _getCardTitleOfCardType({required CardType cardType}) {
    return cardType == CardType.virtual ? EvoStrings.virtualCard : EvoStrings.physicalCard;
  }

  Color _getTitleColor({required CardType cardType}) {
    return widget.cardType == CardType.virtual ? evoColors.greyScale75 : evoColors.greyScale60;
  }

  void _onHandleCopyToClipboardState(BuildContext context, CopyToClipBoardState state) {
    switch (state) {
      case CopiedSuccess():
        _copyToClipboardUtil.showFeedback(toastMsg: EvoStrings.copiedToClipboard);
        break;
    }
  }
}
