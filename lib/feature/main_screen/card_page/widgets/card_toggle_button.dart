import 'dart:ffi';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../../resources/resources.dart';

class CardToggleButton extends StatefulWidget {
  final List<String> titleButtons;
  final int selectedIndex;
  final void Function(int selectedIndex)? onIndexChanged;

  const CardToggleButton({required this.titleButtons, this.selectedIndex = 0, this.onIndexChanged, super.key});

  @override
  State<CardToggleButton> createState() => CardToggleButtonState();
}

@visibleForTesting
class CardToggleButtonState extends State<CardToggleButton> {
  late int selectedIndex;

  @override
  void initState() {
    super.initState();
    selectedIndex = widget.selectedIndex;
  }

  @override
  Widget build(BuildContext context) {
    final List<Widget> buttons = widget.titleButtons.mapIndexed((int index, String text) {
      final EdgeInsets insets = getInsetsPaddingByIndex(index);
      return Padding(
        padding: insets,
        child: CommonButton(
          onPressed: () {
            onHandleToggle(index);
          },
          style: getButtonStyleByIndex(index),
          child: Text(text),
        ),
      );
    }).toList();

    return Row(
      children: buttons,
    );
  }

  @visibleForTesting
  EdgeInsets getInsetsPaddingByIndex(int index) {
    /// Exclude last button, other buttons should be added padding right.
    if (index == widget.titleButtons.length - 1) {
      return EdgeInsets.zero;
    }
    return const EdgeInsets.only(right: 8);
  }

  @visibleForTesting
  ButtonStyle getButtonStyleByIndex(int index) {
    final bool isSelected = selectedIndex == index;
    return evoButtonStyles.utility(ButtonSize.small).copyWith(
          foregroundColor: WidgetStateProperty.resolveWith(
            (Set<WidgetState> states) {
              // text color
              return isSelected ? evoColors.defaultWhite : evoColors.greyScale100;
            },
          ),
          backgroundColor: WidgetStateProperty.resolveWith(
            // background color
            (Set<WidgetState> states) {
              return isSelected ? evoColors.greyScale100 : evoColors.utilityButtonBg;
            },
          ),
          textStyle: WidgetStateProperty.resolveWith(
            (Set<WidgetState> states) {
              return isSelected ? evoTextStyles.bold(TextSize.sm) : evoTextStyles.bold(TextSize.sm);
            },
          ),
          overlayColor: WidgetStateProperty.all(evoColors.greyScale90),
        );
  }

  @visibleForTesting
  void onHandleToggle(int index) {
    if (selectedIndex == index) {
      return;
    }

    setState(() {
      selectedIndex = index;
    });
    widget.onIndexChanged?.call(selectedIndex);
  }
}
