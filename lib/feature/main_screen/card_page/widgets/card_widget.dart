import 'package:flutter/widgets.dart';

import '../../../../resources/resources.dart';
import '../models/card_information_model.dart';
import '../models/card_state.dart';
import 'active_card_widget.dart';

enum CardType {
  virtual(0),
  physical(1);

  final int value;

  const CardType(this.value);

  static CardType fromValue(int value) {
    return CardType.values.firstWhere((CardType element) => element.value == value);
  }
}

enum CardDetailsVisibility {
  hidden,
  visible,
  unknown,
}

class CardWidget extends StatelessWidget {
  final CardState cardState;
  final CardType cardType;
  final CardDetailsVisibility cardDetailsVisibility;
  final String? fourLastDigitNumbers;

  const CardWidget({
    required this.cardState,
    required this.cardType,
    super.key,
    this.cardDetailsVisibility = CardDetailsVisibility.unknown,
    this.fourLastDigitNumbers,
  });

  @override
  Widget build(BuildContext context) {
    /// Card section with background image
    /// aspect ratio 342:216 is referred from figma design
    return AspectRatio(
      aspectRatio: EvoDimension.defaultCardRatio,
      child: _buildCardByState(context: context, state: cardState),
    );
  }

  Widget _buildCardByState({required BuildContext context, required CardState state}) {
    switch (state) {
      case CardState.active:
        return ActiveCardWidget(
          cardInformationModel: CardInformationModel.mockCardInformation,
          visibility: cardDetailsVisibility,
          cardType: cardType,
        );
      case CardState.replaced:
      case CardState.inactive:
        return _buildDefaultCardWidget(
          title: EvoStrings.cardStateInactiveTitle,
          cardType: cardType,
        );
      case CardState.frozen:
        return _buildDefaultCardWidget(
          title: EvoStrings.cardStateFrozenTitle,
          cardType: cardType,
        );
      case CardState.userBlocked || CardState.bankBlocked:
        return _buildDefaultCardWidget(
          title: EvoStrings.cardStateBlockedTitle,
          cardType: cardType,
        );
    }
  }

  Widget _buildDefaultCardWidget({
    required String title,
    required CardType cardType,
  }) {
    final String backgroundImage = switch (cardType) {
      CardType.physical => EvoImages.framePhysicalCardInactive,
      CardType.virtual => EvoImages.frameVirtualCardInactive,
    };

    return Stack(
      children: <Widget>[
        // Background image
        Positioned.fill(
          child: evoImageProvider.asset(
            backgroundImage,
            fit: BoxFit.fill,
          ),
        ),
        Positioned(
          left: 1,
          bottom: 1,
          child: Padding(
            padding: const EdgeInsets.only(left: 24, bottom: 14),
            child: Text(
              title,
              style: evoTextStyles.bold(
                TextSize.base,
                color: evoColors.defaultWhite,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
