import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

import '../../../../resources/resources.dart';
import '../../../../util/screen_util.dart';
import '../../../../widget/elevated_container.dart';
import '../../../../widget/evo_switch.dart';

enum FreezeCardState { freeze, unfreeze }

enum ShowDetailsState { show, hide }

class CardActionWidget extends StatelessWidget {
  final FreezeCardState freezeCardState;
  final ShowDetailsState showDetailsState;
  final VoidCallback? onFreezeCardClick;
  final VoidCallback? onShowDetailsClick;

  const CardActionWidget({
    this.freezeCardState = FreezeCardState.unfreeze,
    this.showDetailsState = ShowDetailsState.hide,
    this.onFreezeCardClick,
    this.onShowDetailsClick,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: <Widget>[
        Expanded(
          child: _buildFreezeCard(context: context),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildShowDetails(context: context),
        ),
      ],
    );
  }

  Widget _buildFreezeCard({required BuildContext context}) {
    return _buildActionContainer(
      context: context,
      onTap: onFreezeCardClick,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          AbsorbPointer(
            child: EvoSwitch(
              width: 52.w,
              value: _isCardFreeze(),
              onToggle: (bool value) {
                // todo something
              },
            ),
          ),
          const SizedBox(width: 8),
          Flexible(
            child: Text(
              EvoStrings.ctaFreezeCard,
              style: evoTextStyles.bold(
                TextSize.sm,
                color: evoColors.textNormal,
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildShowDetails({required BuildContext context}) {
    final Color textColor = _isCardFreeze() ? evoColors.greyScale70 : evoColors.textNormal;
    final Color iconColor = _isCardFreeze() ? evoColors.greyScale70 : evoColors.accent90;
    return _buildActionContainer(
      context: context,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          SizedBox.square(
            dimension: 24,
            child: evoImageProvider.asset(
              showDetailsState == ShowDetailsState.show
                  ? EvoImages.icEyeVisibilityOff
                  : EvoImages.icEyeVisibilityOn,
              color: iconColor,
              fit: BoxFit.scaleDown,
            ),
          ),
          const SizedBox(width: 8),
          Flexible(
            child: Text(
              EvoStrings.ctaShowDetails,
              style: evoTextStyles.bold(TextSize.sm, color: textColor),
            ),
          ),
        ],
      ),
      onTap: _isCardFreeze() ? null : onShowDetailsClick,
    );
  }

  /// UI background for action container
  Widget _buildActionContainer(
      {required BuildContext context, required Widget child, void Function()? onTap}) {
    return ElevatedContainer(
      height: 50.h,
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        child: child,
      ),
    );
  }

  bool _isCardFreeze() {
    return freezeCardState == FreezeCardState.freeze;
  }
}
