// coverage:ignore-file
import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_common_package/widget/refreshable_view.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/functions.dart';
import '../../../util/screen_util.dart';
import '../../../widget/action_button_widget.dart';
import '../../../widget/appbar/need_help_support_appbar.dart';
import '../../../widget/banners/status_banner/status_banner_widget.dart';
import '../../../widget/transaction_widget/transaction_section.dart';
import '../../privilege_action/privilege_access_guard_module.dart';
import '../../verify_otp/cubit/verify_otp_cubit.dart';
import '../../verify_otp/verify_otp_page.dart';
import '../main_screen_controller.dart';
import 'activate_card_success_screen.dart';
import 'cubits/cards_page_cubit.dart';
import 'cubits/recent_transactions_cubit.dart';
import 'cubits/recent_transactions_state.dart';
import 'last_4_digits_check/last_4_digits_check_screen.dart';
import 'models/card_model.dart';
import 'models/card_state.dart';
import 'widgets/card_action_widget.dart';
import 'widgets/card_toggle_button.dart';
import 'widgets/card_widget.dart';

class CardsPage extends PageBase {
  final MainScreenController mainPageController;

  const CardsPage({
    required this.mainPageController,
    super.key,
  });

  @override
  State<CardsPage> createState() => _CardsPageState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.cardsPage.routeName);
}

class _CardsPageState extends EvoPageStateBase<CardsPage> with AutomaticKeepAliveClientMixin {
  Widget defaultHeight = EvoDimension.space24;

  final CardsPageCubit _cardsCubit = CardsPageCubit(
    privilegeAccessGuardModule: getIt.get<PrivilegeAccessGuardModule>(),
  );

  final RecentTransactionsCubit _transactionsCubit = RecentTransactionsCubit();

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((Duration duration) {
      _cardsCubit.initializeCardType(cardType: CardType.virtual);
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return buildVisibilityDetectorPage(context);
  }

  List<bool> isSelected = <bool>[false, false];

  final RefreshController _refreshController = RefreshController();

  @override
  void onPagePaused() {
    super.onPagePaused();
    _cardsCubit.hideCardDetails();
  }

  @override
  void onTopVisiblePageChanged(bool isOnTopAndVisible) {
    if (!isOnTopAndVisible) {
      _cardsCubit.hideCardDetails();
    }
  }

  @override
  Widget getContentWidget(BuildContext context) {
    final List<Widget> bodyItems = _buildItems();
    return Scaffold(
      appBar: NeedHelpSupportAppbar(),
      body: MultiBlocProvider(
        providers: <BlocProvider<dynamic>>[
          BlocProvider<CardsPageCubit>(create: (_) => _cardsCubit),
          BlocProvider<RecentTransactionsCubit>(create: (_) => _transactionsCubit),
        ],
        child: BlocListener<CardsPageCubit, CardsPageState>(
          listenWhen: _listenWhenCardState,
          listener: (_, CardsPageState state) => _onCardStateChanged(state),
          child: SafeArea(
            child: RefreshableView(
              onRefresh: () {},
              controller: _refreshController,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 24.w),
                child: ListView.builder(
                  itemCount: bodyItems.length,
                  itemBuilder: (BuildContext context, int index) {
                    return bodyItems[index];
                  },
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  List<Widget> _buildItems() {
    return <Widget>[
      Text(
        EvoStrings.cardPageTitle,
        style: evoTextStyles.bold(TextSize.xl2),
      ),
      defaultHeight,
      CardToggleButton(
        titleButtons: const <String>[
          EvoStrings.virtualCard,
          EvoStrings.physicalCard,
        ],
        onIndexChanged: (int index) {
          final CardType cardType = CardType.fromValue(index);
          _cardsCubit.initializeCardType(cardType: cardType);
        },
      ),
      defaultHeight,
      _buildTitle(),
      _buildBanner(),
      _buildCard(),
      defaultHeight,
      _buildCardAction(),
      defaultHeight,
      _buildTransaction(),
    ];
  }

  Widget _buildTitle() {
    return BlocBuilder<CardsPageCubit, CardsPageState>(
      buildWhen: (CardsPageState previous, CardsPageState current) {
        return current is CardInfoLoaded;
      },
      builder: (BuildContext context, CardsPageState state) {
        if (state is! CardInfoLoaded) {
          return const SizedBox.shrink();
        }

        final CardType cardType = state.cardModel.type;
        String text = '';

        final CardState cardState = state.cardModel.state;
        if (cardState.isCardInactivated() || cardState.isCardBlocked()) {
          text = cardType == CardType.virtual ? 'Virtual Card · · · ·' : 'Physical Card · · · ·';
        } else {
          text = cardType == CardType.virtual ? 'Virtual Card 5638' : 'Physical Card 5638';
        }

        return Text(
          text,
          style: evoTextStyles.bold(TextSize.xl, color: evoColors.greyScale90),
        );
      },
    );
  }

  Widget _buildCard() {
    return BlocBuilder<CardsPageCubit, CardsPageState>(
      buildWhen: (CardsPageState previous, CardsPageState current) {
        return current is CardInfoLoaded;
      },
      builder: (BuildContext context, CardsPageState state) {
        if (state is! CardInfoLoaded) {
          return const SizedBox.shrink();
        }
        final CardModel model = state.cardModel;
        return CardWidget(
          cardState: model.state,
          cardType: model.type,
          cardDetailsVisibility: model.visibility,
          fourLastDigitNumbers: '5638',
        );
      },
    );
  }

  Widget _buildCardAction() {
    return BlocBuilder<CardsPageCubit, CardsPageState>(
      buildWhen: (CardsPageState previous, CardsPageState current) {
        return current is CardInfoLoaded;
      },
      builder: (BuildContext context, CardsPageState state) {
        if (state is! CardInfoLoaded) {
          return const SizedBox.shrink();
        }

        final CardState cardState = state.cardModel.state;

        if (cardState.isCardInactivated()) {
          return _buildCardActionForCardInactivated();
        }

        if (cardState.isCardBlocked()) {
          return _buildCardActionForCardBlocked();
        }

        return _buildCardActionActiveState(state.cardModel);
      },
    );
  }

  Widget _buildCardActionForCardInactivated() {
    return CommonButton(
        onPressed: () {
          /// TODO: nam-pham-ts update this function when integrate with BE
          VerifyOtpPage.pushNamed(
            verifyOtpType: VerifyOtpType.activateCard,
            contactInfo: '9178919821',
            otpResendSecs: 120,
            otpValiditySecs: 120,
            onPopSuccess: (VerifyOtpState state) {
              if (state is VerifyOtpSuccess) {
                _cardsCubit.onVerifyOtpSuccess();
              }
            },
          );
        },
        style: evoButtonStyles.primary(
          ButtonSize.medium,
        ),
        child: const Text(EvoStrings.activateCard));
  }

  Widget _buildCardActionForCardBlocked() {
    return ActionButtonWidget(
      title: EvoStrings.contactItemTitle,
      icon: EvoImages.icChatBubble,
      iconColor: evoColors.accent90,
      onPress: () {
        evoUtilFunction.openInAppWebView(
          title: EvoStrings.contactItemTitle,
          url: WebsiteUrl.evoContactUrl,
        );
      },
    );
  }

  /// Card Action when card with below state:
  /// * Active
  /// * Freeze
  Widget _buildCardActionActiveState(CardModel cardModel) {
    final ShowDetailsState showDetailState = cardModel.visibility == CardDetailsVisibility.visible
        ? ShowDetailsState.show
        : ShowDetailsState.hide;

    final FreezeCardState freezeCardState =
        cardModel.state == CardState.frozen ? FreezeCardState.freeze : FreezeCardState.unfreeze;
    return CardActionWidget(
      showDetailsState: showDetailState,
      freezeCardState: freezeCardState,
      onFreezeCardClick: () {
        _cardsCubit.toggleCardFreezeStatus();
      },
      onShowDetailsClick: () {
        _cardsCubit.toggleCardDetailsVisibility();
      },
    );
  }

  /// Determines when recent transactions data should be fetched
  bool _listenWhenCardState(CardsPageState previous, CardsPageState current) {
    // when user switching tabs
    if (current is CardInfoLoading) {
      _transactionsCubit.reset();
      return true;
    }

    // first successful card data fetch
    if (previous is CardInfoLoading && current is CardInfoLoaded) {
      final CardState state = current.cardModel.state;
      if (state == CardState.active || state == CardState.frozen) {
        _transactionsCubit.getTransactions(current.cardModel);
      }
    }

    // after successful card activation
    if (previous is CardActivateSuccess && current is CardInfoLoaded) {
      _transactionsCubit.getTransactions(current.cardModel);
      return true;
    }

    return true;
  }

  Widget _buildTransaction() {
    return BlocBuilder<RecentTransactionsCubit, RecentTransactionsState>(
      builder: (_, RecentTransactionsState state) {
        if (state is! RecentTransactionsSuccess) {
          return const SizedBox.shrink();
        }
        return TransactionSection(
          onViewMoreClicked: () {
            widget.mainPageController.jumpToPage(MainScreenSubPage.usage);
          },
          items: state.transactions,
        );
      },
    );
  }

  Widget _buildBanner() {
    return BlocBuilder<CardsPageCubit, CardsPageState>(
        buildWhen: (CardsPageState previous, CardsPageState current) {
      return current is CardInfoLoaded ||
          current is CardActivateFailure ||
          current is CardFreezeFailure;
    }, builder: (BuildContext context, CardsPageState state) {
      return switch (state) {
        /// case activate card failure
        CardActivateFailure(error: final ErrorUIModel error) => Padding(
            padding: EdgeInsets.symmetric(vertical: 24.w),
            child: StatusBannerWidget.create(
              type: StatusBannerType.error,
              title: EvoStrings.bannerCardActivateFailedTitle,
              description: error.userMessage,
            ),
          ),
        CardFreezeFailure() => _buildErrorBannerIfFreezeCardFailed(),
        CardInfoLoaded() => _buildBannerWithCardInfoLoaded(state.cardModel),
        _ => defaultHeight,
      };
    });
  }

  Widget _buildBannerWithCardInfoLoaded(CardModel card) {
    return switch (card.state) {
      /// case new card has been replaced and initially in inactive status
      CardState.replaced => Padding(
          padding: EdgeInsets.symmetric(vertical: 24.w),
          child: StatusBannerWidget.create(
            type: StatusBannerType.success,
            title: EvoStrings.bannerCardNewCardTitle,
            description: EvoStrings.bannerCardNewCardDesc,
          ),
        ),
      CardState.userBlocked => Padding(
          padding: EdgeInsets.symmetric(vertical: 24.w),
          child: StatusBannerWidget.create(
            type: StatusBannerType.error,
            title: EvoStrings.blockedCardBannerTitle,
            description: EvoStrings.blockedCardBannerByUserDesc,
          ),
        ),
      CardState.bankBlocked => Padding(
          padding: EdgeInsets.symmetric(vertical: 24.w),
          child: StatusBannerWidget.create(
            type: StatusBannerType.error,
            title: EvoStrings.blockedCardBannerTitle,
            description: EvoStrings.blockedCardBannerByBankDesc,
          ),
        ),
      _ => defaultHeight,
    };
  }

  void _onCardStateChanged(CardsPageState state) {
    if (state is CardInfoLoading || state is CardFreezeLoading) {
      evoUtilFunction.showHudLoading();
      return;
    }

    evoUtilFunction.hideHudLoading();

    switch (state) {
      case ShowCardInfoDetailedError(error: final ErrorUIModel error):
        handleEvoApiError(error);
        break;
      case CardCheckingLast4Digits():
        Last4DigitsCheckScreen.pushNamed(
          onSuccess: _cardsCubit.onCheckLast4DigitsSuccess,
        );
      case CardActivateSuccess(cardType: final CardType cardType):
        ActivateCardSuccessScreen.pushNamed(cardType);
      default:

        /// do nothing
        break;
    }
  }

  Widget _buildErrorBannerIfFreezeCardFailed() {
    /// custom textStyle to more space between underlines and text
    /// refer this link: https://github.com/flutter/flutter/issues/30541#issuecomment-*********
    final TextStyle defaultTextStyle =
        evoTextStyles.regular(TextSize.sm, color: Colors.transparent).copyWith(
      shadows: <Shadow>[
        Shadow(
          color: evoColors.greyScale100,
          offset: const Offset(0, -2),
        ),
      ],
      decorationColor: evoColors.greyScale100,
    );
    return GestureDetector(
      onTap: () {
        evoUtilFunction.openInAppWebView(
          title: EvoStrings.contactItemTitle,
          url: WebsiteUrl.evoContactUrl,
        );
      },
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 24.w),
        child: StatusBannerWidget.create(
          type: StatusBannerType.error,
          title: EvoStrings.freezeCarErrorTitle,
          descriptionWidget: RichText(
            text: TextSpan(style: defaultTextStyle, children: <InlineSpan>[
              const TextSpan(
                text: EvoStrings.freezeCarErrorDesc1,
              ),
              TextSpan(
                text: EvoStrings.freezeCarErrorDesc2,
                style: defaultTextStyle.copyWith(
                  decoration: TextDecoration.underline,
                ),
              )
            ]),
          ),
        ),
      ),
    );
  }
}
