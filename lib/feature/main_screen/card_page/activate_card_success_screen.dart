import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../resources/resources.dart';
import '../../../util/screen_util.dart';
import 'widgets/card_widget.dart';

class ActivateCardSuccessArg extends PageBaseArg {
  final CardType cardType;

  ActivateCardSuccessArg(this.cardType);
}

class ActivateCardSuccessScreen extends PageBase {
  final CardType cardType;

  const ActivateCardSuccessScreen({required this.cardType, super.key});

  @override
  State<StatefulWidget> createState() => _ActivateCardSuccessScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.activateCardSuccessScreen.routeName);

  static void pushNamed(CardType cardType) {
    navigatorContext?.pushNamed(
      Screen.activateCardSuccessScreen.name,
      extra: ActivateCardSuccessArg(cardType),
    );
  }
}

class _ActivateCardSuccessScreenState extends EvoPageStateBase<ActivateCardSuccessScreen> {
  @override
  Widget getContentWidget(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (_, __) => _goToCards(),
      child: ColoredBox(
        color: evoColors.primary,
        child: SafeArea(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 24.w),
            child: Stack(
              alignment: Alignment.center,
              children: <Widget>[
                _buildImageText(),
                Positioned(
                  left: 0,
                  right: 0,
                  bottom: 64.w,
                  child: _buildButton(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildImageText() {
    final bool isVirtual = widget.cardType == CardType.virtual;
    final String image = isVirtual
        ? EvoImages.imgActivateVirtualCardSuccess
        : EvoImages.imgActivatePhysicalCardSuccess;
    final String text =
        isVirtual ? EvoStrings.activateVirtualCardSuccess : EvoStrings.activatePhysicalCardSuccess;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        evoImageProvider.asset(
          image,
          width: 234.w,
          fit: BoxFit.contain,
        ),
        EvoDimension.space32,
        Text(
          text,
          textAlign: TextAlign.center,
          style: evoTextStyles.bold(TextSize.xl2, color: evoColors.activateCardSuccessText),
        ),
      ],
    );
  }

  Widget _buildButton() {
    return CommonButton(
      isWrapContent: false,
      onPressed: _goToCards,
      style: evoButtonStyles.primary(ButtonSize.medium, brightness: Brightness.light),
      child: const Text(EvoStrings.goToCardsButton),
    );
  }

  void _goToCards() {
    /// TODO: Consider using MainScreen.goNamed(isLoggedIn: true, initialPage: MainScreenPage.cards)
    /// TODO: when the flow is defined more clearly
    navigatorContext?.popUntilNamed(Screen.mainScreen.name);
  }
}
