import 'package:flutter/material.dart';

import '../../resources/resources.dart';

class BottomBarItemWidget extends StatelessWidget {
  final double _iconSize = 24;

  final String icon;
  final String label;
  final bool isSelected;
  final GestureTapCallback? onTap;

  const BottomBarItemWidget({
    required this.icon,
    required this.label,
    required this.isSelected,
    this.onTap,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      splashColor: Colors.transparent,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          evoImageProvider.asset(
            icon,
            color: isSelected
                ? evoColors.bottomSheetSelectedItem
                : evoColors.bottomSheetUnselectedItem,
            width: _iconSize,
            height: _iconSize,
            fit: BoxFit.scaleDown,
          ),
          const SizedBox(height: 6),
          Text(
            label,
            textAlign: TextAlign.center,
            style: evoTextStyles
                .bold(
                  TextSize.sm,
                  color: isSelected
                      ? evoColors.bottomSheetSelectedItem
                      : evoColors.bottomSheetUnselectedItem,
                ),
          ),
        ],
      ),
    );
  }
}
