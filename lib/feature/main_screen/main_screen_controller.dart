enum MainScreenSubPage {
  home(0),
  cards(1),
  payCard(2),
  usage(3),
  profile(4);

  final int pageIndex;

  const MainScreenSubPage(this.pageIndex);

  static MainScreenSubPage getByIndex(int index) {
    switch (index) {
      case 0:
        return MainScreenSubPage.home;
      case 1:
        return MainScreenSubPage.cards;
      case 2:
        return MainScreenSubPage.payCard;
      case 3:
        return MainScreenSubPage.usage;
      case 4:
        return MainScreenSubPage.profile;
      default:
        return MainScreenSubPage.home;
    }
  }
}

abstract class MainScreenController {
  void jumpToPage(MainScreenSubPage screenChild);

  MainScreenSubPage getCurrentSubPage();
}
