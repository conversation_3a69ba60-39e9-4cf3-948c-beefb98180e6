import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/base/common_cubit.dart';

import '../../../prepare_for_app_initiation.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../biometric/model/biometric_status_change_notifier.dart';
import '../../biometric/request_user_active_biometric/request_user_active_biometric_handler.dart';
import '../../biometric/request_user_active_biometric/request_user_active_biometric_handler_impl.dart';
import '../main_screen_dialog_handler/main_screen_dialog_handler.dart';

part 'main_state.dart';

class MainCubit extends CommonCubit<MainState> {
  final AppState appState;
  final MainScreenDialogHandler _mainScreenDialogHandler;

  MainCubit({
    required this.appState,
    required MainScreenDialogHandler mainScreenDialogHandler,
  })  : _mainScreenDialogHandler = mainScreenDialogHandler,
        super(MainInitial());

  void handleFeatureProcessed(FeaturesWithDialogDisplay featureDialog) {
    _mainScreenDialogHandler.handleFeatureProcessed(featureDialog);
    if (_mainScreenDialogHandler.isAllFeaturesWithDialogProcessed) {
      emit(AllFeaturesWithDialogProcessed());
    }
  }
}
