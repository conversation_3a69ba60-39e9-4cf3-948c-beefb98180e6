import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../activate_biometric/confirm_pin/confirm_pin_screen.dart';
import '../base/ext_biometric_token_entity.dart';
import 'biometric_token_action.dart';
import 'biometrics_token_module.dart';

abstract class BiometricChallengeCallback {
  void onBioChallengeSuccess(String? biometricToken);

  void onBioChallengeError(ErrorUIModel error);

  void onBioChallengeCancel();
}

class BiometricTokenChallengeHandler {
  BiometricTokenChallengeHandler({
    required this.callback,
    required this.onError,
  });

  final BiometricChallengeCallback callback;
  final void Function(ActionError error) onError;

  Future<void> handle({
    required ChallengeType challengeType,
  }) async {
    switch (challengeType) {
      case ChallengeType.pin:
        ConfirmPinScreen.pushNamed(callback: callback);
        return;
      default:
        onError(ActionError(
          errorType: BiometricTokenModuleErrorType.noSupportExtraChallenge,
        ));
    }
  }
}
