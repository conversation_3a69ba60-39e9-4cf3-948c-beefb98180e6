import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../base/ext_biometric_token_entity.dart';
import '../utils/bio_auth_result.dart';
import 'biometrics_token_module.dart';

sealed class BiometricTokenAction {}

class ActionSuccess extends BiometricTokenAction {
  ActionSuccess(this.biometricToken);

  final String? biometricToken;
}

class ActionChallenge extends BiometricTokenAction {
  ActionChallenge(this.challengeType);

  final ChallengeType challengeType;
}

class ActionError extends BiometricTokenAction {
  ActionError({
    required this.errorType,
    this.errorMessage,
  });

  final BiometricTokenModuleErrorType errorType;
  final String? errorMessage;
}

class ActionApiError extends BiometricTokenAction {
  ActionApiError({required this.error});

  final ErrorUIModel error;
  final BiometricTokenModuleErrorType errorType = BiometricTokenModuleErrorType.apiError;

  String? get errorMessage => error.userMessage;
}

class ActionBiometricError extends BiometricTokenAction {
  ActionBiometricError({required this.error});

  final BioAuthError? error;

  final BiometricTokenModuleErrorType errorType = BiometricTokenModuleErrorType.biometrics;
}
