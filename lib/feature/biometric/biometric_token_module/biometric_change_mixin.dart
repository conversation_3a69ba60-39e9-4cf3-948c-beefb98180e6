import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../model/biometric_status_change_notifier.dart';
import '../utils/bio_auth_result.dart';
import '../utils/biometric_functions.dart';
import '../utils/biometric_status_helper.dart';
import 'biometrics_token_module.dart';

mixin BiometricChangeMixin {
  final AppState _appState = getIt.get<AppState>();
  final BiometricsTokenModule biometricsTokenModule = getIt.get<BiometricsTokenModule>();

  @visibleForTesting
  bool isShowBiometricChangedPopup = false;

  Future<void> handleBiometricChangedIfNeed() async {
    final bool hasBiometricChanged = await checkBiometricChange();

    if (hasBiometricChanged) {
      await disableBiometric();
      _appState.biometricStatusChangeNotifier.update(BiometricStatus.deviceSettingChanged);
    }

    if (hasBiometricChanged && _appState.isUserLogIn) {
      await showBiometricChangedPopupAndUpdateStatus();
    }
  }

  Future<bool> checkBiometricChange() async {
    /// check if Biometric Authenticator in-App feature is enabled
    final bool isEnableBiometric = await biometricsTokenModule.isEnableBiometricAuthenticator();

    /// if Biometric Authenticator in-App feature is disabled, don't need to check biometric change
    /// to avoid unnecessary check
    if (!isEnableBiometric) {
      return false;
    }

    /// check if there is enrolled biometrics in device settings
    final bool hasEnrolledBiometrics = await biometricsTokenModule.hasEnrolledBiometrics();

    /// check if biometrics entities in device settings has changed (adding/removing entities)
    final bool isHasChange = await biometricsTokenModule.getBiometricChanged();

    if (!hasEnrolledBiometrics || isHasChange) {
      return true;
    }
    return false;
  }

  Future<void>? disableBiometric() {
    /// Disable biometric token
    return biometricsTokenModule.disableBiometricAuthenticatorFeature();
  }

  @visibleForTesting
  Future<void> showBiometricChangedWarningPopup() async {
    if (isShowBiometricChangedPopup) {
      return;
    }

    isShowBiometricChangedPopup = true;

    /// Build warning message.
    final String biometricTypeName = _appState.bioTypeInfo.biometricTypeName;
    final String warningMessage = EvoStrings.biometricDeviceChangeWarring.replaceVariableByValue(
      <String>[
        biometricTypeName.uppercaseFirstLetter(),
        biometricTypeName,
      ],
    );

    await biometricFunctions.handleBioError(bioError: BioAuthError.notEnrolled);

    isShowBiometricChangedPopup = false;
  }

  Future<void> showBiometricChangedPopupAndUpdateStatus() async {
    await showBiometricChangedWarningPopup();
    await getIt.get<BiometricStatusHelper>().updateBiometricStatus();
  }
}
