// ignore_for_file: avoid_catches_without_on_clauses

import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/repository/logging/log_error_mixin.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:local_auth/local_auth.dart';
import 'package:ts_bio_detect_changed/ts_bio_detect_changed.dart';

import '../../../data/repository/user_repo.dart';
import '../../../data/response/biometric_token_entity.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/functions.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../util/token_utils/jwt_helper.dart';
import '../../logging/evo_logging_event.dart';
import '../base/ext_biometric_token_entity.dart';
import '../mock/mock_biometric_token_use_case.dart';
import '../model/biometric_status_change_notifier.dart';
import '../utils/bio_auth_result.dart';
import '../utils/biometrics_authenticate.dart';
import 'biometric_challenge_handler.dart';
import 'biometric_token_action.dart';

abstract class BiometricTokenModuleCallback {
  void onRetrieveTokenSuccess();

  void onRetrieveTokenError({
    required BiometricTokenModuleErrorType type,
    String? userMessage,
    ErrorUIModel? error,
    BioAuthError? bioError,
  });
}

enum BiometricTokenModuleErrorType {
  noSupportExtraChallenge,
  biometrics,
  ignoreExtraChallenge,
  apiError,
  unknown;
}

class BiometricsTokenModule with LogErrorMixin implements BiometricChallengeCallback {
  final BiometricsAuthenticate biometricsAuthenticate;
  final UserRepo userRepo;
  final EvoLocalStorageHelper secureStorageHelper;
  late BiometricTokenModuleCallback? callback;
  final TsBioDetectChanged bioDetectChanged;
  final JwtHelper jwtHelper;
  BiometricTokenChallengeHandler? challengeHandler;
  bool isProcessing = false;

  static const Key challengeWidgetKey = Key('challenge_widget_key');

  BiometricsTokenModule({
    required this.biometricsAuthenticate,
    required this.userRepo,
    required this.secureStorageHelper,
    required this.bioDetectChanged,
    required this.jwtHelper,
    BiometricTokenChallengeHandler? challengeHandler,
  }) {
    this.challengeHandler = challengeHandler ??
        BiometricTokenChallengeHandler(
          callback: this,
          onError: handleBiometricsToken,
        );
  }

  Future<void> enable({BiometricTokenModuleCallback? callback}) async {
    if (isProcessing) {
      return;
    }
    this.callback = callback;
    isProcessing = true;

    final BioAuthResult result = await biometricsAuthenticate.authenticate(
      localizedReason: EvoStrings.localizedReasonForUsingBiometrics,
      cancelButton: EvoStrings.ignoreTitle,
    );
    if (result.isAuthSuccess) {
      final BiometricTokenAction action = await _getBiometricToken();
      await handleBiometricsToken(action);
    } else {
      await handleBiometricsToken(ActionBiometricError(error: result.error));
    }
  }

  @visibleForTesting
  Future<void> enableBiometricAuthenticatorFeature(String? biometricToken) async {
    if (biometricToken != null && biometricToken.isNotEmpty) {
      evoUtilFunction.showHudLoading();
      await secureStorageHelper.setBiometricToken(biometricToken);
      await secureStorageHelper.setBiometricAuthenticator(true);
      await bioDetectChanged.initialize();

      getIt.get<AppState>().biometricStatusChangeNotifier.update(BiometricStatus.usable);
      evoUtilFunction.hideHudLoading();
    }
  }

  Future<void> disableBiometricAuthenticatorFeature() async {
    await evoUtilFunction.clearBiometricAuthenticationData();
  }

  Future<bool> isBiometricTokenUsable() async {
    final String? biometricToken = await secureStorageHelper.getBiometricToken();
    return jwtHelper.isCanUse(biometricToken);
  }

  @override
  void onBioChallengeError(ErrorUIModel error) {
    handleBiometricsToken(ActionApiError(error: error));
  }

  @override
  void onBioChallengeSuccess(String? biometricToken) {
    handleBiometricsToken(ActionSuccess(biometricToken));
  }

  @override
  void onBioChallengeCancel() {
    isProcessing = false;
  }

  Future<BiometricTokenAction> _getBiometricToken() async {
    evoUtilFunction.showHudLoading();

    final BiometricTokenEntity entity = await userRepo.getBiometricTokenByPin(
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockBiometricTokenFileNameByCase(
          MockTestBiometricTokenUseCase.getBiometricTokenWithChallengeType,
        ),
      ),
    );

    evoUtilFunction.hideHudLoading();

    if (entity.statusCode != CommonHttpClient.SUCCESS) {
      final ErrorUIModel errorModel = ErrorUIModel.fromEntity(entity);
      return ActionApiError(error: errorModel);
    }

    if (entity.isNeedChallenge()) {
      final ChallengeType challengeType = entity.getChallengeType();
      return ActionChallenge(challengeType);
    } else {
      return ActionSuccess(entity.biometricToken);
    }
  }

  @visibleForTesting
  Future<void> handleBiometricsToken(BiometricTokenAction action) async {
    if (action is! ActionChallenge) {
      isProcessing = false;
    }

    switch (action) {
      case ActionSuccess():
        await enableBiometricAuthenticatorFeature(action.biometricToken);
        callback?.onRetrieveTokenSuccess();
        break;
      case ActionChallenge():
        challengeHandler?.handle(
          challengeType: action.challengeType,
        );
        break;
      case ActionError():
        callback?.onRetrieveTokenError(
          type: action.errorType,
          userMessage: action.errorMessage,
        );
        break;
      case ActionApiError():
        callback?.onRetrieveTokenError(
          type: action.errorType,
          userMessage: action.errorMessage,
          error: action.error,
        );
        break;
      case ActionBiometricError():
        callback?.onRetrieveTokenError(
          type: action.errorType,
          bioError: action.error,
        );
        break;
    }
  }

  /// Pls note that, when [getBiometricChanged] is returns true,
  /// consider to call [disableBiometricAuthenticatorFeature]
  /// to clear all biometric feature
  Future<bool> getBiometricChanged() async {
    bool isBiometricsChanged;
    // Platform messages may fail, so we use a try/catch PlatformException.
    // We also handle the message potentially returning null.
    try {
      isBiometricsChanged = await bioDetectChanged.isBiometricChanged() ?? false;
    } catch (e) {
      logException(
        eventType: EvoEventType.biometrics,
        methodName: 'getBiometricChanged',
        exception: e,
      );
      isBiometricsChanged = false;
    }

    return isBiometricsChanged;
  }

  Future<bool> hasEnrolledBiometrics() async {
    final List<BiometricType> enrolledBiometrics =
        await biometricsAuthenticate.getAvailableBiometricType();
    return enrolledBiometrics.isNotEmpty;
  }

  Future<bool> isEnableBiometricAuthenticator() {
    return secureStorageHelper.isEnableBiometricAuthenticator();
  }
}
