import 'dart:async';

import 'package:evoapp/feature/biometric/request_user_active_biometric/request_user_active_biometric_popup.dart';
import 'package:flutter/widgets.dart';

import '../../../prepare_for_app_initiation.dart';
import '../utils/biometrics_authenticate.dart';
import 'request_user_active_biometric_handler.dart';
import 'request_user_active_biometric_util.dart';

/// Refer: https://trustingsocial1.atlassian.net/wiki/spaces/BPIRBank/pages/**********/Enabling+Biometrics
enum ActivateBiometricUseCase {
  // Use-case 1: show suggestion biometric popup after users log-in EVO app in new devices & successfully verify PIN
  newDevice,
}

enum RequestUserActivateBiometricStatus {
  success,
  fail,
  inProgress,
  unQualified,
}

class RequestUserActiveBiometricHandlerImp extends RequestUserActivateBiometricHandler {
  RequestUserActiveBiometricHandlerImp({
    required this.requestUserActiveBiometricUtil,
    this.biometricsAuthenticate,
  });

  final AppState? appState = getIt.get<AppState>();
  final BiometricsAuthenticate? biometricsAuthenticate;
  final RequestUserActiveBiometricUtil requestUserActiveBiometricUtil;

  /// For unit test
  /// true: dialog showing
  /// false: dialog hide
  @visibleForTesting
  bool isShowingPopupActiveBiometric = false;

  @override
  Future<RequestUserActivateBiometricStatus> start({
    ActivateBiometricUseCase? useCase,
  }) async {
    final bool? isHasSupportBiometric = await biometricsAuthenticate?.isDeviceSupportBiometrics();

    if (isHasSupportBiometric != true) {
      return RequestUserActivateBiometricStatus.unQualified;
    }

    final bool? isEnableBiometric =
        await requestUserActiveBiometricUtil.getEnableBiometricAuthenticator();
    if (isEnableBiometric == true) {
      return RequestUserActivateBiometricStatus.success;
    }

    switch (useCase) {
      case ActivateBiometricUseCase.newDevice:
        return await handleUseCaseNewDevice();
      default:
        return await handleUseCaseReLoginOnOldDevice();
    }
  }

  @visibleForTesting
  Future<RequestUserActivateBiometricStatus> handleUseCaseNewDevice() async {
    final bool isNewDevice = await requestUserActiveBiometricUtil.isNewDevice();
    if (isNewDevice) {
      return await showDialogActiveBiometric();
    }
    return RequestUserActivateBiometricStatus.unQualified;
  }

  @visibleForTesting
  Future<RequestUserActivateBiometricStatus> handleUseCaseReLoginOnOldDevice() async {
    final String? lastTimeLocal = await requestUserActiveBiometricUtil.getTimeShowBiometric();
    if (requestUserActiveBiometricUtil.checkTimeShowBiometric(lastTimeLocal)) {
      return await showDialogActiveBiometric();
    }

    return RequestUserActivateBiometricStatus.unQualified;
  }

  @visibleForTesting
  Future<RequestUserActivateBiometricStatus> showDialogActiveBiometric() async {
    /// Save time show dialog to local storage
    await requestUserActiveBiometricUtil.saveTimeShowBiometric();

    if (isShowingPopupActiveBiometric) {
      return RequestUserActivateBiometricStatus.inProgress;
    }

    isShowingPopupActiveBiometric = true;

    final RequestUserActiveBiometricPopup popup =
    requestUserActiveBiometricUtil.getRequestUserActiveBiometricPopup();

    final RequestUserActivateBiometricStatus status = await popup.showDialogActiveBiometric();
    isShowingPopupActiveBiometric = false;
    return status;
  }

}
