part of 'request_user_active_biometric_cubit.dart';

@immutable
abstract class RequestUserActiveBiometricState implements BlocState {}

class RequestUserActiveBiometricInitial extends RequestUserActiveBiometricState {}

class RequestUserActiveBiometricCompletedState extends RequestUserActiveBiometricState {
  final BiometricStatus status;

  RequestUserActiveBiometricCompletedState({required this.status});
}

