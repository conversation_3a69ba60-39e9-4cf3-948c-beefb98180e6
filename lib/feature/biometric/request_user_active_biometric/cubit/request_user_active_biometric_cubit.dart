import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';

import '../../../../prepare_for_app_initiation.dart';
import '../../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../biometric_token_module/biometric_change_mixin.dart';
import '../../biometric_token_module/biometric_token_usability_mixin.dart';
import '../../model/biometric_status_change_notifier.dart';
import '../request_user_active_biometric_handler.dart';
import '../request_user_active_biometric_handler_impl.dart';

part 'request_user_active_biometric_state.dart';

class RequestUserActiveBiometricCubit extends CommonCubit<RequestUserActiveBiometricState>
    with BiometricChangeMixin, BiometricTokenUsabilityMixin {
  final AppState appState;
  final RequestUserActivateBiometricHandler requestUserActiveBiometricHandler;
  final EvoLocalStorageHelper evoLocalStorageHelper;

  RequestUserActiveBiometricCubit({
    required this.appState,
    required this.requestUserActiveBiometricHandler,
    required this.evoLocalStorageHelper,
  }) : super(RequestUserActiveBiometricInitial());

  Future<void> requestActiveBiometricIfNeed({
    ActivateBiometricUseCase? activateBiometricUseCase,
  }) async {
    final RequestUserActivateBiometricStatus status = await requestUserActiveBiometricHandler.start(
      useCase: activateBiometricUseCase,
    );

    /// Don't handle [RequestUserActivateBiometricStatus.inProgress],
    /// Because it's not a final status, it's just a status to indicate that the process is in progress.
    /// Wait user to make decision.
    if (status == RequestUserActivateBiometricStatus.inProgress) {
      return;
    }

    final BiometricStatus biometricStatus = appState.biometricStatusChangeNotifier.value;
    await processBiometricStatus(status: biometricStatus);
    await setNewDevice(false);

    emit(RequestUserActiveBiometricCompletedState(status: biometricStatus));
  }

  Future<void> processBiometricStatus({required BiometricStatus status}) async {
    switch (status) {
      case BiometricStatus.deviceSettingChanged:
        await showBiometricChangedPopupAndUpdateStatus();
        break;

      case BiometricStatus.biometricTokenUnusable:
        await showBiometricTokenUnUsableToastAndUpdateStatus();
        break;

      case BiometricStatus.notSetup:
      case BiometricStatus.usable:
      default:
        break;
    }
  }

  Future<void> setNewDevice(bool isNewDevice) {
    return evoLocalStorageHelper.setNewDevice(isNewDevice);
  }
}
