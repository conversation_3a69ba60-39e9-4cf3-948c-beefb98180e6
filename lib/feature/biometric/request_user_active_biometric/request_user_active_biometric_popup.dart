import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../model/evo_dialog_id.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/dialog_functions.dart';
import '../../../util/evo_snackbar.dart';
import '../../biometric/biometric_token_module/biometrics_token_module.dart';
import '../model/biometric_ui_model.dart';
import '../utils/bio_auth_result.dart';
import '../utils/biometric_functions.dart';
import '../utils/biometrics_authenticate.dart';
import 'request_user_active_biometric_handler_impl.dart';

class RequestUserActiveBiometricPopup implements BiometricTokenModuleCallback {
  final AppState appState = getIt.get<AppState>();

  final BiometricsAuthenticate? biometricsAuthenticate = getIt.get<BiometricsAuthenticate>();

  final BiometricsTokenModule biometricsTokenModule = getIt.get<BiometricsTokenModule>();

  RequestUserActiveBiometricPopup();

  @visibleForTesting
  bool isTappedActivateBiometric = false;

  @visibleForTesting
  void Function(RequestUserActivateBiometricStatus)? onActiveBiometric;

  Future<RequestUserActivateBiometricStatus> showDialogActiveBiometric() async {
    final Completer<RequestUserActivateBiometricStatus> completer =
        Completer<RequestUserActivateBiometricStatus>();

    onActiveBiometric = (RequestUserActivateBiometricStatus status) {
      isTappedActivateBiometric = false;
      completer.complete(status);
    };

    await show();

    return completer.future;
  }

  @visibleForTesting
  Future<void> show() async {
    await showDialogActivateBiometric(
      onActive: onTapActive,
    );

    if (!isTappedActivateBiometric) {
      onActiveBiometric?.call(RequestUserActivateBiometricStatus.fail);
    }
  }

  Future<void> onTapActive() async {
    isTappedActivateBiometric = true;

    final bool? hasEnrolledBiometric = await biometricsAuthenticate?.hasEnrolledBiometric();
    if (hasEnrolledBiometric == true) {
      await biometricsTokenModule.enable(callback: this);
    } else {
      biometricFunctions.handleBioError(bioError: BioAuthError.notEnrolled);

      /// Return the status "un_qualified"
      /// Because the user's device does not have a biometric setup configured.
      onActiveBiometric?.call(RequestUserActivateBiometricStatus.unQualified);
    }
  }

  @override
  void onRetrieveTokenSuccess() {
    final BiometricTypeUIModel bioTypeInfo = getIt.get<AppState>().bioTypeInfo;
    final String alertMsg =
        '${EvoStrings.enableText} ${EvoStrings.authenticateText.toLowerCase()} ${bioTypeInfo.biometricTypeName}';
    getIt.get<EvoSnackBar>().show(alertMsg, durationInMilliSec: SnackBarDuration.short.value);

    onActiveBiometric?.call(RequestUserActivateBiometricStatus.success);
  }

  @override
  void onRetrieveTokenError({
    required BiometricTokenModuleErrorType type,
    String? userMessage,
    ErrorUIModel? error,
    BioAuthError? bioError,
  }) {
    switch (type) {
      case BiometricTokenModuleErrorType.biometrics:
        biometricFunctions.handleBioError(bioError: bioError);
        break;
      default:
        getIt.get<EvoSnackBar>().show(userMessage ?? CommonStrings.otherGenericErrorMessage,
            typeSnackBar: SnackBarType.error, durationInMilliSec: SnackBarDuration.short.value);
        break;
    }

    onActiveBiometric?.call(RequestUserActivateBiometricStatus.fail);
  }

  @visibleForTesting
  Future<void> showDialogActivateBiometric({
    required VoidCallback onActive,
    VoidCallback? onSkip,
  }) async {
    return await evoDialogFunction.showDialogConfirm(
      dialogId: EvoDialogId.requestEnableActiveBiometricBottomSheet,
      content: EvoStrings.activeBiometricDesc,
      title: EvoStrings.activeBiometricTitle,
      textPositive: EvoStrings.ctaActive,
      textNegative: EvoStrings.ctaSkip,
      dialogHorizontalPadding: 56.75,
      imageHeader: evoImageProvider.asset(EvoImages.imgEnableBiometric, fit: BoxFit.fitWidth),
      onClickNegative: () {
        onSkip?.call();

        /// dismiss popup
        navigatorContext?.pop();
      },
      onClickPositive: () {
        onActive();

        /// dismiss popup
        navigatorContext?.pop();
      },
    );
  }
}
