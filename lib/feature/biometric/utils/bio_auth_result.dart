import 'package:flutter_common_package/resources/resources.dart';

import '../../../resources/resources.dart';

class BioAuthResult {
  final bool isAuthSuccess;
  final BioAuthError? error;

  BioAuthResult({required this.isAuthSuccess, this.error});

  factory BioAuthResult.success() => BioAuthResult(isAuthSuccess: true);

  factory BioAuthResult.error(BioAuthError? error) =>
      BioAuthResult(isAuthSuccess: false, error: error);
}

enum BioAuthError {
  /// Indicates user click Cancel on button System Challenge Biometric Dialog.
  userDismiss,

  /// Indicates the API is locked out more persistently than [lockedOut].
  /// Strong authentication like PIN/Pattern/Password is required to unlock.
  /// user need to lock/unlock phone or go to settings app to unlock disable biometrics
  permanentlyLockedOut,

  /// for Android device: Indicates the API is temporarily locked out due to too many attempts.
  androidLockedOut,

  /// biometric is locked by system or has not enrolled any biometrics on the device.
  notEnrolled,

  ///unknown error
  unknown;

  static String getErrMsg(BioAuthError? error) {
    switch (error) {
      case BioAuthError.notEnrolled:
      case BioAuthError.androidLockedOut:
        return EvoStrings.lockedOutError;
      case BioAuthError.permanentlyLockedOut:
        return EvoStrings.permanentlyLockedOutError;
      case BioAuthError.unknown:
      default:
        return CommonStrings.otherGenericErrorMessage;
    }
  }

  bool isBiometricLocked() {
    return this == BioAuthError.androidLockedOut || this == BioAuthError.permanentlyLockedOut;
  }
}
