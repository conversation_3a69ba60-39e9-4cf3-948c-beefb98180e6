// ignore_for_file: avoid_catches_without_on_clauses, depend_on_referenced_packages
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/data/repository/logging/log_error_mixin.dart';
import 'package:local_auth/error_codes.dart' as auth_error;
import 'package:local_auth/local_auth.dart';
import 'package:local_auth_android/local_auth_android.dart';
import 'package:local_auth_darwin/local_auth_darwin.dart';

import '../../../resources/ui_strings.dart';
import '../../../util/evo_flutter_wrapper.dart';
import '../../logging/evo_logging_event.dart';
import 'bio_auth_result.dart';
import 'biometrics_authenticate.dart';

/// using 3rd library: local_auth
/// references link: https://pub.dev/packages/local_auth

/// In Android, we can’t detect if fingerprint is available on the device.
// It’s up to the OS to determine which kind of Biometric is Strong security.
// On most device model, Fingerprint is Strong security, but that is not always the case.
// We can only detect if a device has Strong type of biometric that enrolled.
/// set [isForceStrongBiometric] is [TRUE], when you want to force using Strong type
class BiometricAuthenticateImpl extends BiometricsAuthenticate with LogErrorMixin {
  static const AuthenticationOptions defaultAuthenticationOptions = AuthenticationOptions(
    stickyAuth: true,
    biometricOnly: true,
    useErrorDialogs: false,
  );
  static const AuthenticationOptions androidAuthenticationOptions = AndroidAuthenticationOptions(
    stickyAuth: true,
    biometricOnly: true,
    useErrorDialogs: false,
    strongBiometricOnly: true,
  );

  final LocalAuthentication localAuth;

  /// this configure just is apply to Android device
  bool isForceStrongBiometric;

  BiometricAuthenticateImpl({required this.localAuth, this.isForceStrongBiometric = false});

  @override
  Future<BioAuthResult> authenticate({
    required String localizedReason,
    String? cancelButton,
  }) async {
    bool authenticated = false;
    String? errorCode;
    try {
      final AuthenticationOptions options = _getAuthenticationsOptions();
      authenticated = await localAuth.authenticate(
        localizedReason: localizedReason,
        options: options,
        authMessages: getAuthMessages(
          cancelButton: cancelButton,
        ),
      );
    } on PlatformException catch (e) {
      errorCode = e.code;
      logException(
        eventType: EvoEventType.biometrics,
        methodName: 'authenticate',
        exception: e,
      );
    } catch (e) {
      /// log error if [localAuth] plugin throws Error or other Exceptions
      logException(
        eventType: EvoEventType.biometrics,
        methodName: 'authenticate',
        exception: e,
      );
    }

    if (evoFlutterWrapper.isIOS()) {
      return _handleBioErrorFromIOSDevice(authenticated: authenticated, errCode: errorCode);
    } else if (evoFlutterWrapper.isAndroid()) {
      return _handleBioErrorFromAndroidDevice(authenticated: authenticated, errCode: errorCode);
    } else {
      throw ArgumentError('this operation does not support this function');
    }
  }

  /// return [BioAuthError.userDismiss]. If user click Cancel on button System Challenge Biometric Dialog.
  /// return [BioAuthError.permanentlyLockedOut]. If biometric is locked by system
  BioAuthResult _handleBioErrorFromIOSDevice(
      {required bool authenticated, required String? errCode}) {
    if (authenticated) {
      return BioAuthResult.success();
    }

    if (errCode == null) {
      return BioAuthResult.error(BioAuthError.permanentlyLockedOut);
    } else if (errCode == auth_error.notAvailable) {
      return BioAuthResult.error(BioAuthError.userDismiss);
    } else {
      return BioAuthResult.error(BioAuthError.unknown);
    }
  }

  /// return [BioAuthError.userDismiss]. If user click Cancel on button System Challenge Biometric Dialog.
  /// return [BioAuthError.permanentlyLockedOut] | [BioAuthError.androidLockedOut]. If biometric is locked by system
  /// return [BioAuthError.notEnrolled]. If biometric is locked by system or has not enrolled any biometrics on the device.
  BioAuthResult _handleBioErrorFromAndroidDevice({required bool authenticated, String? errCode}) {
    if (authenticated) {
      return BioAuthResult.success();
    }

    if (errCode == null) {
      return BioAuthResult.error(BioAuthError.userDismiss);
    } else if (errCode == auth_error.permanentlyLockedOut) {
      return BioAuthResult.error(BioAuthError.permanentlyLockedOut);
    } else if (errCode == auth_error.lockedOut) {
      return BioAuthResult.error(BioAuthError.androidLockedOut);
    } else if (errCode == auth_error.notEnrolled) {
      return BioAuthResult.error(BioAuthError.notEnrolled);
    } else {
      return BioAuthResult.error(BioAuthError.unknown);
    }
  }

  @override
  Future<bool> hasEnrolledBiometric() async {
    final List<BiometricType> result = await getAvailableBiometricType();
    return result.isNotEmpty;
  }

  @override
  Future<bool> isDeviceSupportBiometrics() {
    return localAuth.canCheckBiometrics;
  }

  @override
  Future<List<BiometricType>> getAvailableBiometricType() async {
    final List<BiometricType> result = await localAuth.getAvailableBiometrics();
    if (isSupportAndroidStrongBiometric()) {
      return result.where((BiometricType element) => element == BiometricType.strong).toList();
    }
    return result;
  }

  bool isSupportAndroidStrongBiometric() => isForceStrongBiometric && evoFlutterWrapper.isAndroid();

  AuthenticationOptions _getAuthenticationsOptions() {
    return isSupportAndroidStrongBiometric()
        ? androidAuthenticationOptions
        : defaultAuthenticationOptions;
  }

  /// The system presents a fallback button when biometric authentication fails. To eliminate the fallback option, set the fallback title to an empty string.
  /// refer https://developer.apple.com/documentation/localauthentication/lacontext/1514183-localizedfallbacktitle
  @override
  @visibleForTesting
  List<AuthMessages> getAuthMessages({String? cancelButton}) {
    final String cancelButtonText = cancelButton ?? EvoStrings.useMPIN;
    return <AuthMessages>[
      IOSAuthMessages(
        localizedFallbackTitle: '',
        authCancelButton: cancelButtonText,
      ),
      AndroidAuthMessages(
        cancelButton: cancelButtonText,
      )
    ];
  }
}
