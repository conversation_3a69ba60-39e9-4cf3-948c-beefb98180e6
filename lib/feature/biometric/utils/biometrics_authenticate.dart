import 'package:local_auth_darwin/local_auth_darwin.dart';

import 'bio_auth_result.dart';

abstract class BiometricsAuthenticate {
  Future<bool> isDeviceSupportBiometrics();

  Future<BioAuthResult> authenticate({
    required String localizedReason,
    String? cancelButton,
  });

  Future<bool> hasEnrolledBiometric();

  Future<List<BiometricType>> getAvailableBiometricType();

  List<AuthMessages> getAuthMessages({String? cancelButton});
}

enum TsBiometricType {
  face,
  finger,
  androidBio, //this type just used for Android device
  unknown,
}
