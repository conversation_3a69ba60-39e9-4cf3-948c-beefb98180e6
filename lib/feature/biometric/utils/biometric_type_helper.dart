import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:local_auth/local_auth.dart';

import 'biometrics_authenticate.dart';

class BiometricTypeHelper {
  /// list of device support fingerId
  /// refer: https://en.wikipedia.org/wiki/Touch_ID#:~:text=As%20of%20August%202022%2C%20the,devices%20which%20use%20the%20second
  /// last updated: 09/02/2023 - 15:00
  List<String> defaultIOSUtsNameMachineSupportFingerprint = <String>[
    'iPhone6,1',
    'iPhone6,2',
    'iPhone8,4',
    'iPhone7,1',
    'iPhone7,2',
    'iPhone8,1',
    'iPhone8,2',
    'iPhone9,1',
    'iPhone9,2',
    'iPhone9,3',
    'iPhone9,4',
    'iPhone10,1',
    'iPhone10,2',
    'iPhone10,4',
    'iPhone10,5',
    'iPhone12,8',
    'iPhone14,6',
  ];

  final BiometricsAuthenticate bioAuth;
  final DeviceInfoPlugin deviceInfo;
  final DevicePlatform platform;

  BiometricTypeHelper(this.bioAuth, this.deviceInfo, this.platform);

  Future<TsBiometricType> getTsBiometricType() async {
    final List<BiometricType> types = await bioAuth.getAvailableBiometricType();

    /// for iOS, at now we just support 2 type: Finger & Face
    if (platform.isIOS()) {
      return _handleIOSPlatform(types);
    }

    /// Android device can't determine specified biometric type. there are [BiometricType.Strong]
    /// & [BiometricType.weak]
    if (platform.isAndroid()) {
      return TsBiometricType.androidBio;
    }

    return TsBiometricType.unknown;
  }

  Future<TsBiometricType> _handleIOSPlatform(List<BiometricType> types) async {
    if (types.contains(BiometricType.face)) {
      return TsBiometricType.face;
    } else if (types.contains(BiometricType.fingerprint)) {
      return TsBiometricType.finger;
    } else {
      final IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      final List<String> fingerIOsDevices = defaultIOSUtsNameMachineSupportFingerprint;
      return fingerIOsDevices.contains(iosInfo.utsname.machine)
          ? TsBiometricType.finger
          : TsBiometricType.face;
    }
  }
}
