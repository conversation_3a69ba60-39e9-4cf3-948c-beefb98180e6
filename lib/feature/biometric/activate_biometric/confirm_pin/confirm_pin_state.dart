part of 'confirm_pin_cubit.dart';

abstract class ConfirmPinState implements BlocState {}

class ConfirmPinInitState extends ConfirmPinState {}

class ConfirmPinLoadingState extends ConfirmPinState {}

class ConfirmPinSuccess extends ConfirmPinState {
  final String? biometricToken;

  ConfirmPinSuccess({required this.biometricToken});
}

class ConfirmPinBadRequest extends ConfirmPinState {
  final String? userMessage;

  ConfirmPinBadRequest({required this.userMessage});
}

class ConfirmPinFailure extends ConfirmPinState {
  final ErrorUIModel error;

  ConfirmPinFailure({
    required this.error,
  });
}
