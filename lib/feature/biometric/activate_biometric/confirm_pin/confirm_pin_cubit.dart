import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../data/repository/user_repo.dart';
import '../../../../data/response/biometric_token_entity.dart';
import '../../../pin/mock/mock_pin_use_case.dart';

part 'confirm_pin_state.dart';

class ConfirmPinCubit extends CommonCubit<ConfirmPinState> {
  final UserRepo userRepo;

  ConfirmPinCubit({
    required this.userRepo,
  }) : super(ConfirmPinInitState());

  Future<void> getBiometricTokenByPin(String? pin) async {
    emit(ConfirmPinLoadingState());

    final BiometricTokenEntity entity = await userRepo.getBiometricTokenByPin(
      pin: pin,
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockPinFileNameByCase(MockPinUseCase.getVerifyPinInvalidCredential),
        statusCode: CommonHttpClient.UNKNOWN_ERRORS,
      ),
    );

    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      emit(ConfirmPinSuccess(
        biometricToken: entity.biometricToken,
      ));
      return;
    }

    if (entity.statusCode == CommonHttpClient.BAD_REQUEST) {
      emit(ConfirmPinBadRequest(userMessage: entity.userMessage));
      return;
    }

    emit(ConfirmPinFailure(error: ErrorUIModel.fromEntity(entity)));
  }
}
