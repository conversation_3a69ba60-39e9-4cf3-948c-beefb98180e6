import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../base/evo_page_state_base.dart';
import '../../../../data/repository/user_repo.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/resources.dart';
import '../../../../util/functions.dart';
import '../../../../widget/appbar/evo_support_appbar.dart';
import '../../../../widget/evo_mpin_code/evo_mpin_code_widget.dart';
import '../../../pin/reset_pin/reset_pin_handler.dart';
import '../../biometric_token_module/biometric_challenge_handler.dart';
import 'confirm_pin_cubit.dart';

class ConfirmPinScreenArgs extends PageBaseArg {
  ConfirmPinScreenArgs({required this.callback});

  final BiometricChallengeCallback? callback;
}

class ConfirmPinScreen extends PageBase {
  const ConfirmPinScreen({required this.callback, super.key});

  final BiometricChallengeCallback? callback;

  @override
  EvoPageStateBase<ConfirmPinScreen> createState() => _ConfirmPinScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.confirmPinScreen.name);

  static Future<void> pushNamed({required BiometricChallengeCallback callback}) async {
    return navigatorContext?.pushNamed(Screen.confirmPinScreen.name,
        extra: ConfirmPinScreenArgs(
          callback: callback,
        ));
  }
}

class _ConfirmPinScreenState extends EvoPageStateBase<ConfirmPinScreen> {
  final ConfirmPinCubit _cubit = ConfirmPinCubit(userRepo: getIt.get<UserRepo>());

  TextEditingController pinTextController = TextEditingController();

  @override
  void dispose() {
    widget.callback?.onBioChallengeCancel();
    super.dispose();
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      appBar: EvoSupportAppbar(),
      body: BlocProvider<ConfirmPinCubit>(
        create: (_) => _cubit,
        child: BlocListener<ConfirmPinCubit, ConfirmPinState>(
          listener: (BuildContext context, ConfirmPinState currState) {
            _onStateChanged(currState);
          },
          child: _getContent(),
        ),
      ),
    );
  }

  Widget _getContent() {
    return BlocBuilder<ConfirmPinCubit, ConfirmPinState>(
        buildWhen: (ConfirmPinState prevState, ConfirmPinState currState) =>
            currState is! ConfirmPinLoadingState,
        builder: (BuildContext context, ConfirmPinState state) {
          String? errMessage;

          if (state is ConfirmPinBadRequest) {
            errMessage = state.userMessage ?? CommonStrings.otherGenericErrorMessage;
          }

          return Container(
            padding: const EdgeInsets.all(20),
            child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Text(
                    EvoStrings.inputPinTitle,
                    style: evoTextStyles.bold(TextSize.xl2),
                  ),
                  EvoMPINCodeWidget(
                    textEditingController: pinTextController,
                    title: EvoStrings.inputPinDesc,
                    onSubmit: _verifyPin,
                    errorMessage: errMessage,
                    onResetPin: _onRequestResetPin,
                  )
                ]),
          );
        });
  }

  void _verifyPin(String pin) {
    _cubit.getBiometricTokenByPin(pin);
  }

  void _onStateChanged(ConfirmPinState currState) {
    if (currState is ConfirmPinLoadingState) {
      evoUtilFunction.showHudLoading();
      return;
    }

    evoUtilFunction.hideHudLoading();

    if (currState is ConfirmPinSuccess) {
      navigatorContext?.pop();
      widget.callback?.onBioChallengeSuccess(currState.biometricToken);
      return;
    }

    if (currState is ConfirmPinFailure) {
      navigatorContext?.pop();
      widget.callback?.onBioChallengeError(currState.error);
      return;
    }
  }

  void _onRequestResetPin() {
    final String? phoneNumber = appState.userInfo.value?.phoneNumber;

    getIt.get<ResetPinHandler>().requestResetPin(
          phoneNumber: phoneNumber,
          onError: handleEvoApiError,
          entryScreenName: widget.routeSettings.name,
        );
  }
}
