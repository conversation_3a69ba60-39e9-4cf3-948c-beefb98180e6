import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_common_package/widget/widgets.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/screen_util.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../widget/appbar/evo_support_appbar.dart';
import '../biometric_token_module/biometrics_token_module.dart';
import '../utils/bio_auth_result.dart';
import '../utils/biometric_functions.dart';
import '../utils/biometrics_authenticate.dart';
import 'active_biometric_cubit.dart';
import 'active_biometric_state.dart';

class ActiveBiometricScreenArg extends PageBaseArg {
  void Function() onSuccess;

  ActiveBiometricScreenArg({
    required this.onSuccess,
  });
}

class ActiveBiometricScreen extends PageBase {
  final void Function() onSuccess;

  static Future<void> goNamed({
    required void Function() onSuccess,
  }) async {
    return navigatorContext?.goNamed(
      Screen.activeBiometric.name,
      extra: ActiveBiometricScreenArg(onSuccess: onSuccess),
    );
  }

  static Future<void> pushNamed({
    required void Function() onSuccess,
  }) async {
    return navigatorContext?.pushNamed(
      Screen.activeBiometric.name,
      extra: ActiveBiometricScreenArg(onSuccess: onSuccess),
    );
  }

  const ActiveBiometricScreen({
    required this.onSuccess,
    super.key,
  });

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.activeBiometric.routeName);

  @override
  EvoPageStateBase<ActiveBiometricScreen> createState() => ActiveBiometricScreenState();
}

class ActiveBiometricScreenState extends EvoPageStateBase<ActiveBiometricScreen>
    implements BiometricTokenModuleCallback {
  late ActiveBiometricCubit activeBiometricCubit;

  final BiometricsTokenModule _biometricsTokenModule = getIt.get<BiometricsTokenModule>();

  @override
  void initState() {
    super.initState();

    activeBiometricCubit = context.read<ActiveBiometricCubit?>() ??
        ActiveBiometricCubit(
          bioAuth: getIt.get<BiometricsAuthenticate>(),
          localStorage: getIt.get<EvoLocalStorageHelper>(),
        );
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<ActiveBiometricCubit>(
      create: (_) => activeBiometricCubit,
      child: Scaffold(
        appBar: EvoSupportAppbar(leading: null),
        backgroundColor: evoColors.background,
        body: SafeArea(
          child: BlocListener<ActiveBiometricCubit, ActiveBiometricState>(
            listener: (BuildContext context, ActiveBiometricState currState) {
              _listenActiveBiometric(currState);
            },
            child: PopScope(
              canPop: false,
              child: Padding(
                padding: const EdgeInsets.only(left: 24, right: 24, bottom: 24),
                child: Column(
                  children: <Widget>[
                    _buildTitle(),
                    const SizedBox(height: 4),
                    _buildDesc(),
                    _buildImage(),
                    ..._buildCTAs(),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Text _buildTitle() {
    return Text(
      EvoStrings.activeBiometricTitle,
      style: evoTextStyles.bold(
        TextSize.xl2,
        color: evoColors.screenTitle,
      ),
    );
  }

  Text _buildDesc() {
    return Text(
      EvoStrings.activeBiometricDesc,
      style: evoTextStyles.regular(
        TextSize.base,
        color: evoColors.textActive,
      ),
    );
  }

  Widget _buildImage() {
    return Expanded(
      child: evoImageProvider.asset(
        EvoImages.imgEnableBiometric,
        height: 256.41.h,
      ),
    );
  }

  List<Widget> _buildCTAs() {
    return <Widget>[
      CommonButton(
        isWrapContent: false,
        onPressed: () {
          activeBiometricCubit.checkEnrolledBiometric();
        },
        style: evoButtonStyles.primary(ButtonSize.medium),
        child: const Text(EvoStrings.ctaYes),
      ),
      const SizedBox(height: 16),
      CommonButton(
        isWrapContent: false,
        onPressed: () {
          _onComplete();
        },
        style: evoButtonStyles.tertiary(ButtonSize.medium),
        child: Text(
          EvoStrings.ctaSkip,
          style: evoTextStyles.bold(
            TextSize.base,
            color: evoColors.primary,
          ),
        ),
      ),
    ];
  }

  void _listenActiveBiometric(ActiveBiometricState currState) {
    if (currState is EnrolledBiometricState) {
      if (currState.hasEnrolledBiometrics) {
        _biometricsTokenModule.enable(callback: this);
      } else {
        biometricFunctions.handleBioError(
          bioError: BioAuthError.notEnrolled,
        );
      }
    }
  }

  @override
  void onRetrieveTokenError({
    required BiometricTokenModuleErrorType type,
    String? userMessage,
    ErrorUIModel? error,
    BioAuthError? bioError,
  }) {
    switch (type) {
      case BiometricTokenModuleErrorType.biometrics:
        biometricFunctions.handleBioError(
          bioError: bioError,
        );
        break;
      case BiometricTokenModuleErrorType.apiError:
        handleEvoApiError(error);
        break;
      default:
        showSnackBarError(userMessage ?? CommonStrings.otherGenericErrorMessage);
        break;
    }
  }

  @override
  void onRetrieveTokenSuccess() {
    _onComplete();
  }

  void _onComplete() {
    navigatorContext?.pop();
    widget.onSuccess();
  }
}
