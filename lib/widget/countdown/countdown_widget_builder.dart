import 'dart:async';

import 'package:flutter/material.dart';

part 'countdown_controller.dart';

class CountdownWidgetBuilder extends StatefulWidget {
  const CountdownWidgetBuilder({
    required this.builder,
    required this.controller,
    required this.duration,
    super.key,
  });

  final Widget Function(BuildContext context, Duration duration) builder;
  final CountdownController controller;
  final Duration duration;

  @override
  State<CountdownWidgetBuilder> createState() => _CountdownWidgetBuilderState();
}

class _CountdownWidgetBuilderState extends State<CountdownWidgetBuilder> {
  Timer? timer;
  ValueNotifier<Duration>? durationNotifier;
  final Duration _tickDuration = const Duration(seconds: 1);

  @override
  void initState() {
    super.initState();
    durationNotifier = ValueNotifier<Duration>(widget.duration);
    _setupController();
  }

  @override
  void dispose() {
    _clearTimer();
    durationNotifier?.dispose();
    durationNotifier = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (durationNotifier != null) {
      return ValueListenableBuilder<Duration>(
        valueListenable: durationNotifier!,
        builder: (BuildContext context, Duration duration, _) {
          return widget.builder(
            context,
            duration,
          );
        },
      );
    }

    return const SizedBox.shrink();
  }

  void _setupController() {
    widget.controller.cancel = _cancel;
    widget.controller.pause = _clearTimer;
    widget.controller.resume = _resume;
    widget.controller.start = _start;
  }

  void _cancel() {
    _clearTimer();
    durationNotifier?.value = Duration.zero;
  }

  void _clearTimer() {
    timer?.cancel();
    timer = null;
  }

  void _start() {
    if (!mounted) {
      return;
    }

    durationNotifier?.value = widget.duration;

    _resume();
  }

  Future<void> _resume() async {
    if (!mounted) {
      return;
    }

    _clearTimer();

    timer = Timer.periodic(_tickDuration, (_) => _onTick());
  }

  void _onTick() {
    final Duration durationValue = durationNotifier?.value ?? Duration.zero;
    final Duration next = durationValue - _tickDuration;

    if (next.isNegative) {
      _cancel();
      widget.controller.onDone();
      return;
    }

    durationNotifier?.value = next;
  }
}
