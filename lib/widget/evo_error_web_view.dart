import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/widget/widgets.dart';

import '../resources/resources.dart';

class EvoErrorWebViewWidget extends StatelessWidget {
  final String? errorText;
  final VoidCallback? onReloadWebView;

  const EvoErrorWebViewWidget({super.key, this.errorText, this.onReloadWebView});

  @override
  Widget build(BuildContext context) {
    return Container(
        color: evoColors.background,
        child: Center(
            child: Column(children: <Widget>[
          Flexible(
              fit: FlexFit.tight,
              flex: 4,
              child: Center(child: evoImageProvider.asset(EvoImages.icErrorWebView))),
          Flexible(
              fit: FlexFit.tight,
              flex: 6,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  children: <Widget>[
                    Text(EvoStrings.webViewErrorTitle,
                        textAlign: TextAlign.center,
                        style: evoTextStyles.h500(color: evoColors.textPassive2)),
                    Padding(
                      padding: const EdgeInsets.only(top: 16, bottom: 24),
                      child: Text(errorText ?? EvoStrings.webViewErrorDescription,
                          textAlign: TextAlign.center,
                          style: evoTextStyles.bodyLarge(evoColors.webViewProgressBg)),
                    ),
                    CommonButton(
                        onPressed: onReloadWebView,
                        isWrapContent: false,
                        style: evoButtonStyles.primary(ButtonSize.xLarge),
                        child: const Text(EvoStrings.retry))
                  ],
                ),
              ))
        ])));
  }
}
