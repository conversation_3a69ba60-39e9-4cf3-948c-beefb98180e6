import 'package:flutter/material.dart';

import 'hud_loading.dart';
import 'hud_loading_overlay_entry.dart';

class HudLoadingWidget extends StatefulWidget {
  final Widget? child;

  const HudLoadingWidget({
    required this.child,
    super.key,
  });

  @override
  State<HudLoadingWidget> createState() => _HudLoadingWidgetState();
}

class _HudLoadingWidgetState extends State<HudLoadingWidget> {
  late HudLoadingOverlayEntry _overlayEntry;

  @override
  void initState() {
    super.initState();
    _overlayEntry = HudLoadingOverlayEntry(
      builder: (BuildContext context) => HudLoading.instance.container ?? const SizedBox.shrink(),
    );
    HudLoading.instance.overlayEntry = _overlayEntry;
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Overlay(
        initialEntries: <OverlayEntry>[
          HudLoadingOverlayEntry(
            builder: (BuildContext context) {
              if (widget.child != null) {
                return widget.child!;
              } else {
                return const SizedBox.shrink();
              }
            },
          ),
          _overlayEntry,
        ],
      ),
    );
  }
}
