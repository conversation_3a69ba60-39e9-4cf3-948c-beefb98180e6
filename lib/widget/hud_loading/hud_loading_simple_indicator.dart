import 'package:flutter/material.dart';

import '../../resources/resources.dart';

class HubLoadingSimpleIndicator extends StatelessWidget {
  @visibleForTesting
  static const double defaultSize = 90;

  @visibleForTesting
  static const double defaultPadding = 21;

  @visibleForTesting
  static const double defaultBorderRadius = 13;

  final Widget animation;

  const HubLoadingSimpleIndicator({
    required this.animation,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: defaultSize,
      height: defaultSize,
      padding: const EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        color: evoColors.defaultWhite,
        borderRadius: const BorderRadius.all(Radius.circular(defaultBorderRadius)),
      ),
      child: animation,
    );
  }
}
