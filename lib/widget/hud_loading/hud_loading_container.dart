import 'package:flutter/material.dart';

class HudLoadingContainer extends StatefulWidget {
  final Widget indicator;
  final Color? maskColor;

  const HudLoadingContainer({
    required this.indicator,
    super.key,
    this.maskColor,
  });

  @override
  HudLoadingContainerState createState() => HudLoadingContainerState();
}

class HudLoadingContainerState extends State<HudLoadingContainer>
    with SingleTickerProviderStateMixin {
  Color? _maskColor;

  @override
  void initState() {
    super.initState();
    if (!mounted) {
      return;
    }

    _maskColor = widget.maskColor ?? Colors.black.withOpacity(0.2);
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: AlignmentDirectional.center,
      children: <Widget>[
        IgnorePointer(
          ignoring: false,
          child: Container(
            width: double.infinity,
            height: double.infinity,
            color: _maskColor,
          ),
        ),
        widget.indicator,
      ],
    );
  }
}
