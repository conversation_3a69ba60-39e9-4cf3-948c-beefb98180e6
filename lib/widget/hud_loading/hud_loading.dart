import 'dart:async';

import 'package:flutter/material.dart';
import 'hud_loading_container.dart';
import 'hud_loading_overlay_entry.dart';
import 'hud_loading_widget.dart';

class HudLoading {
  Widget? _container;

  Widget? get container => _container;

  GlobalKey<HudLoadingContainerState>? _key;

  GlobalKey<HudLoadingContainerState>? get key => _key;

  HudLoadingOverlayEntry? overlayEntry;

  factory HudLoading() => _instance;
  static final HudLoading _instance = HudLoading._internal();

  HudLoading._internal();

  static HudLoading get instance => _instance;

  /// init HudLoading
  TransitionBuilder init({
    TransitionBuilder? builder,
  }) {
    return (BuildContext context, Widget? child) {
      if (builder != null) {
        return builder(context, HudLoadingWidget(child: child));
      } else {
        return HudLoadingWidget(child: child);
      }
    };
  }

  Future<void> show({
    required Widget indicator,
    Color? maskColor,
  }) async {
    assert(
      overlayEntry != null,
      'You should call HudLoading.init() in your MaterialApp',
    );

    if (_key != null) {
      await dismiss();
    }

    _key = GlobalKey<HudLoadingContainerState>();
    _container = HudLoadingContainer(
      key: _key,
      indicator: indicator,
      maskColor: maskColor,
    );
    _markNeedsBuild();
  }

  Future<void> dismiss() async {
    _reset();
  }

  void _reset() {
    _container = null;
    _key = null;
    _markNeedsBuild();
  }

  void _markNeedsBuild() {
    overlayEntry?.markNeedsBuild();
  }
}
