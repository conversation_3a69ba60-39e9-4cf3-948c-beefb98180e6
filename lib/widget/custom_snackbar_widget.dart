import 'package:flutter/material.dart';

import '../resources/resources.dart';

class CustomSnackBarWidget extends StatelessWidget {
  final String text;
  final String? description;
  final TextStyle? textStyleTitle;
  final TextStyle? textStyleDescription;
  final Color? background;
  final Color? borderColor;
  final Widget? leadingIcon;
  final VoidCallback? onClose;

  const CustomSnackBarWidget({
    required this.text,
    super.key,
    this.textStyleTitle,
    this.textStyleDescription,
    this.background,
    this.borderColor,
    this.leadingIcon,
    this.onClose,
    this.description,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: const EdgeInsets.only(left: 14.0, right: 6.0, top: 8.0, bottom: 8.0),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.0),
            color: background ?? evoColors.positive70,
            border: Border.all(color: borderColor ?? evoColors.positive70)),
        child: Row(children: <Widget>[
          leadingIcon ?? const SizedBox(),
          const SizedBox(width: 10.0),
          _itemTitleAndDescription(),
          InkWell(
              onTap: onClose,
              child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: evoImageProvider.asset(EvoImages.icClear, width: 20, height: 20)))
        ]));
  }

  Widget _itemTitleAndDescription() => Expanded(
          child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(text,
              style:
                  textStyleTitle ?? evoTextStyles.h200(color: Colors.black).copyWith(height: 1.4)),
          if (description != null)
            Text(description!,
                style: textStyleDescription ??
                    evoTextStyles.bodySmall(color: Colors.black).copyWith(height: 1.4))
        ],
      ));
}
