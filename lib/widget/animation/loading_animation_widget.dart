import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class LoadingAnimationWidget extends StatelessWidget {
  static const double defaultSize = 48;
  final double size;
  final Color? color;

  const LoadingAnimationWidget({
    this.size = defaultSize,
    this.color,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        backgroundColor: evoColors.defaultTransparent,
        color: color ?? evoColors.accent90,
        strokeCap: StrokeCap.round,
      ),
    );
  }
}
