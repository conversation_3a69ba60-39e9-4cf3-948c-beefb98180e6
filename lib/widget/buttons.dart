import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../resources/resources.dart';

class PrimaryButton extends StatelessWidget {
  final String text;
  final VoidCallback? onTap;

  const PrimaryButton({required this.text, this.onTap, super.key});

  @override
  Widget build(BuildContext context) {
    return CommonButton(
      isWrapContent: false,
      style: evoButtonStyles.primary(ButtonSize.large),
      onPressed: onTap,
      child: Text(text),
    );
  }
}

class SecondaryButton extends StatelessWidget {
  final String text;
  final VoidCallback? onTap;

  const SecondaryButton({required this.text, this.onTap, super.key});

  @override
  Widget build(BuildContext context) {
    return CommonButton(
      isWrapContent: false,
      style: evoButtonStyles.secondary(ButtonSize.large),
      onPressed: onTap,
      child: Text(text),
    );
  }
}

class TertiaryButton extends StatelessWidget {
  final String text;
  final VoidCallback? onTap;

  const TertiaryButton({required this.text, this.onTap, super.key});

  @override
  Widget build(BuildContext context) {
    return CommonButton(
      isWrapContent: false,
      style: evoButtonStyles.tertiary(ButtonSize.large),
      onPressed: onTap,
      child: Text(text),
    );
  }
}
