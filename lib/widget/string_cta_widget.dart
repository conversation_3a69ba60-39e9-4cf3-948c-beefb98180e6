import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../resources/resources.dart';

class StringCtaWidget extends StatelessWidget {
  final String question;
  final String cta;
  final VoidCallback? onTap;

  const StringCtaWidget({
    required this.cta,
    required this.question,
    required this.onTap,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final bool isEnabled = onTap != null;
    return Row(
      children: <Widget>[
        Text(
          question,
          style: evoTextStyles.regular(TextSize.base, color: evoColors.textActive),
        ),
        EvoDimension.space8,
        CommonButton(
          onPressed: onTap,
          style: evoButtonStyles.tertiary(ButtonSize.medium, padding: EdgeInsets.zero),
          child: Text(
            cta,
            style: TextStyle(color: isEnabled ? evoColors.primary : evoColors.greyScale70),
          ),
        ),
      ],
    );
  }
}
