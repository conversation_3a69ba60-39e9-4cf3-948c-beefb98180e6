import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../resources/resources.dart';
import '../evo_pin_text_field.dart';
import 'evo_mpin_code_config.dart';

class EvoMPINCodeWidget extends StatelessWidget {
  final TextEditingController textEditingController;

  final String title;

  final String? errorMessage;

  final bool autoFocus, autoUnFocus;
  final FocusNode? focusNode;

  final void Function(String)? onChange;
  final void Function(String)? onSubmit;
  final VoidCallback? onResetPin;

  const EvoMPINCodeWidget({
    required this.textEditingController,
    required this.title,
    this.errorMessage,
    this.autoFocus = true,
    this.autoUnFocus = false,
    this.focusNode,
    this.onSubmit,
    this.onChange,
    this.onResetPin,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          _buildTitle(),
          const SizedBox(height: 24),
          _buildMPINCode(),
          const SizedBox(height: 24),
          _buildResetMPIN()
        ],
      ),
    );
  }

  Widget _buildTitle() {
    return Text(
      title,
      style: evoTextStyles.regular(TextSize.base),
    );
  }

  Widget _buildMPINCode() {
    return EvoPinTextField(
      focusNode: focusNode,
      textEditingController: textEditingController,
      autoFocus: autoFocus,
      autoUnFocus: autoUnFocus,
      pinLength: EvoMPINCodeConfig.defaultMPINCodeLength,
      onChange: onChange,
      onSubmit: onSubmit,
      isObscureText: true,
      errorMessage: errorMessage,
    );
  }

  Widget _buildResetMPIN() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          EvoStrings.forgotMPINQuestion,
          style: evoTextStyles.regular(
            TextSize.base,
            color: evoColors.textActive,
          ),
        ),
        const SizedBox(width: 8),
        CommonButton(
          onPressed: onResetPin,
          style: evoButtonStyles.tertiary(ButtonSize.medium, padding: EdgeInsets.zero),
          child: Text(
            EvoStrings.reset,
            style: TextStyle(color: evoColors.primary100),
          ),
        ),
      ],
    );
  }
}
