import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../resources/resources.dart';
import '../util/screen_util.dart';

typedef PrefixSuffixWidgetBuilder = Widget Function(
  EdgeInsets innerPadding,
);

class EvoTextField extends StatefulWidget {
  static double horizontalSpacing = 12.w;
  static double verticalSpacing = 12.w;
  static final BoxConstraints iconBoxConstraints = BoxConstraints();
  static final double borderRadius = 8.w;
  static final double outerFocusedBorderPadding = 2.w;
  static final double outerFocusedBorderWidth = 2.w;
  static final double focusedBorderPadding = outerFocusedBorderPadding + outerFocusedBorderWidth;

  final PrefixSuffixWidgetBuilder? prefixBuilder;
  final PrefixSuffixWidgetBuilder? suffixBuilder;
  final bool? isRequired;

  final VoidCallback? onClickSuffixIcon;

  final String? label;
  final Color? cursorColor;
  final TextInputType? keyboardType;

  final String? hintText;
  final TextStyle? hintTextStyle;

  final bool isEnabled;
  final String? initialValue;
  final String? errMessage;

  final FocusNode? focusNode;
  final int? maxLines;
  final int? minLines;
  final int? maxLength;
  final TextInputAction? textInputAction;

  final List<TextInputFormatter>? inputFormatters;
  final void Function(String)? onSubmitted;
  final void Function(String)? onChanged;
  final TextEditingController? textEditingController;
  final TextSelectionControls? selectionControls;

  const EvoTextField({
    this.prefixBuilder,
    this.suffixBuilder,
    this.isRequired,
    this.label,
    this.cursorColor,
    this.keyboardType,
    this.hintText,
    this.hintTextStyle,
    this.isEnabled = true,
    this.initialValue,
    this.errMessage,
    this.onChanged,
    this.onSubmitted,
    this.inputFormatters,
    this.onClickSuffixIcon,
    this.focusNode,
    this.maxLines,
    this.minLines,
    this.textInputAction,
    this.textEditingController,
    this.selectionControls,
    this.maxLength,
    super.key,
  });

  @override
  State<EvoTextField> createState() => _EvoTextFieldState();
}

class _EvoTextFieldState extends State<EvoTextField> {
  late final TextEditingController _textEditingController;
  late final WidgetStatesController _widgetStatesController;
  late final TextSelectionControls _selectionControls;

  late FocusNode _focusNode;

  bool get hasError => widget.errMessage?.isNotEmpty == true;

  @override
  void initState() {
    super.initState();

    /// initialize the focus node on text field
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_listenFocusOnTextField);

    /// Initialize the states controller
    /// if the widget is disabled  -> add [WidgetState.disabled]
    /// if the widget has error message -> add [WidgetState.error]
    _widgetStatesController = WidgetStatesController(<WidgetState>{
      if (!widget.isEnabled) WidgetState.disabled,
      if (hasError) WidgetState.error,
    });

    /// Initialize the text editing controller with the initial value
    _textEditingController =
        widget.textEditingController ?? TextEditingController(text: widget.initialValue);
    _selectionControls = widget.selectionControls ?? MaterialTextSelectionControls();
  }

  @override
  void didUpdateWidget(covariant EvoTextField oldWidget) {
    super.didUpdateWidget(oldWidget);
    commonLog('didUpdateWidget controller ${_widgetStatesController.value.toString()}');

    /// Update the states controller based on the widget properties
    if (widget.isEnabled != oldWidget.isEnabled) {
      _widgetStatesController.update(WidgetState.disabled, !widget.isEnabled);
    }

    if (widget.errMessage != oldWidget.errMessage) {
      _widgetStatesController.update(
        WidgetState.error,
        hasError,
      );
    }
  }

  @override
  void dispose() {
    _focusNode.removeListener(_listenFocusOnTextField);
    _focusNode.dispose();

    _textEditingController.dispose();
    _widgetStatesController.dispose();
    super.dispose();
  }

  void _listenFocusOnTextField() {
    if (!mounted) {
      return;
    }

    /// ensure the focus state is updated after the frame is rendered
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _widgetStatesController.update(WidgetState.focused, _focusNode.hasFocus);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        _buildLabelWidget(),
        _buildTextField(),
        _buildErrorWidget(),
      ],
    );
  }

  Widget _buildTextField() {
    return ValueListenableBuilder<Set<WidgetState>>(
      valueListenable: _widgetStatesController,
      builder: (_, Set<WidgetState> states, __) {
        return Container(
          padding: EdgeInsets.all(EvoTextField.focusedBorderPadding),
          foregroundDecoration: _getFocusedBoxDecoration(states),
          child: TextField(
            onChanged: widget.onChanged,
            controller: _textEditingController,
            focusNode: _focusNode,
            maxLines: widget.maxLines,
            minLines: widget.minLines,
            selectionControls: _selectionControls,
            textInputAction: widget.textInputAction,
            maxLength: widget.maxLength,
            enabled: !states.contains(WidgetState.disabled),
            decoration: _buildInputDecoration(states),
            keyboardType: widget.keyboardType ?? TextInputType.text,
            cursorColor: widget.cursorColor,
            cursorErrorColor: evoColors.errorBase,
            onSubmitted: widget.onSubmitted,
            inputFormatters: widget.inputFormatters,
            style: evoTextStyles.regular(TextSize.base),
            textAlignVertical: TextAlignVertical.center,
          ),
        );
      },
    );
  }

  InputDecoration _buildInputDecoration(Set<WidgetState> states) {
    final InputBorder defaultBorder = _getBorder(color: evoColors.grayBorders);
    final InputBorder errorBorder = _getBorder(color: evoColors.errorBase);
    final InputBorder disableBorder = _getBorder(color: evoColors.grayBorders.withOpacity(0.5));

    return InputDecoration(
      errorBorder: errorBorder,
      focusedErrorBorder: errorBorder,
      disabledBorder: disableBorder,
      focusedBorder: defaultBorder,
      enabledBorder: defaultBorder,
      error: hasError ? SizedBox.shrink() : null,
      border: defaultBorder,
      fillColor: evoColors.defaultTransparent,
      prefixIcon: _getPrefixWidget(states),
      suffixIcon: _getSuffixWidget(states),
      prefixIconConstraints: EvoTextField.iconBoxConstraints,
      suffixIconConstraints: EvoTextField.iconBoxConstraints,
      hintText: widget.hintText,
      hintStyle: widget.hintTextStyle ??
          evoTextStyles.regular(
            TextSize.base,
            color: evoColors.grayBase,
          ),
      isDense: true,
      contentPadding: EdgeInsets.symmetric(
        vertical: EvoTextField.verticalSpacing,
      ),
    );
  }

  BoxDecoration _getFocusedBoxDecoration(Set<WidgetState> states) {
    final Border? border = states.contains(WidgetState.focused)
        ? Border.all(
            width: EvoTextField.outerFocusedBorderWidth,
            color: evoColors.secondaryBase,
          )
        : null;

    return BoxDecoration(
      borderRadius: BorderRadius.circular(
        EvoTextField.focusedBorderPadding + EvoTextField.borderRadius,
      ),
      border: border,
    );
  }

  InputBorder _getBorder({Color? color, double? width}) {
    return OutlineInputBorder(
      borderRadius: BorderRadius.circular(EvoTextField.borderRadius),
      borderSide: BorderSide(
        color: color ?? Colors.transparent,
        width: width ?? 1.w,
      ),
    );
  }

  Widget _buildLabelWidget() {
    final String? label = widget.label;
    if (label == null || label.isEmpty) {
      return SizedBox.shrink();
    }

    return Padding(
        padding: EdgeInsets.only(
          bottom: 4.w,
          left: EvoTextField.focusedBorderPadding,
          right: EvoTextField.focusedBorderPadding,
        ),
        child: RichText(
            text: TextSpan(
                style: evoTextStyles.medium(
                  TextSize.base,
                  color: evoColors.grayText,
                ),
                children: <InlineSpan>[
              TextSpan(text: label),
              if (widget.isRequired == true)
                TextSpan(
                    text: ' ${EvoStrings.textFieldLabelRequiredMark}',
                    style: evoTextStyles.medium(
                      TextSize.sm,
                      color: evoColors.error,
                    ))
            ])));
  }

  Widget? _getPrefixWidget(Set<WidgetState> states) {
    final PrefixSuffixWidgetBuilder? prefixBuilder = widget.prefixBuilder;
    Widget? child;

    if (prefixBuilder != null) {
      final EdgeInsets padding = EdgeInsets.only(left: EvoTextField.horizontalSpacing);
      child = prefixBuilder(padding);
    }

    return _wrapPrefixSuffixWidget(states, child);
  }

  Widget? _getSuffixWidget(Set<WidgetState> states) {
    final PrefixSuffixWidgetBuilder? suffixBuilder = widget.suffixBuilder;
    Widget? child;

    if (suffixBuilder != null) {
      final EdgeInsets padding = EdgeInsets.only(right: EvoTextField.horizontalSpacing);
      child = suffixBuilder(padding);
    }

    return _wrapPrefixSuffixWidget(states, child);
  }

  Widget _wrapPrefixSuffixWidget(Set<WidgetState> states, Widget? child) {
    /// if prefix / suffix is null return a SizedBox as padding
    if (child == null) {
      return SizedBox(width: EvoTextField.horizontalSpacing);
    }

    final Color color =
        states.contains(WidgetState.focused) ? evoColors.textActive : evoColors.grayBase;

    /// override icon and text rendered in this prefix / suffix
    return IconTheme(
      data: IconThemeData(color: evoColors.grayBase),
      child: DefaultTextStyle.merge(
        style: evoTextStyles.regular(
          TextSize.base,
          color: color,
        ),
        child: child,
      ),
    );
  }

  Widget _buildErrorWidget() {
    final String? errMessage = widget.errMessage;

    if (errMessage == null) {
      return SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsets.only(
        left: EvoTextField.focusedBorderPadding,
        right: EvoTextField.focusedBorderPadding,
        top: 4.w,
      ),
      child: Text(
        errMessage,
        style: evoTextStyles.medium(TextSize.s, color: evoColors.error),
      ),
    );
  }
}
