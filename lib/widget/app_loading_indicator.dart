import 'package:flutter/material.dart';

import '../resources/resources.dart';
import '../util/screen_util.dart';

class AppLoadingIndicator extends StatelessWidget {
  const AppLoadingIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox.square(
      dimension: 64.w,
      child: Padding(
        padding: EdgeInsets.all(5.3.w),
        child: CircularProgressIndicator(
          color: evoColors.secondaryBase,
          backgroundColor: evoColors.secondaryInteractive,
          strokeCap: StrokeCap.butt,
          strokeWidth: 4.w,
        ),
      ),
    );
  }
}
