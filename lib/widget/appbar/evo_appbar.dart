import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_app_bar.dart';

import '../../util/screen_util.dart';
import 'evo_appbar_leading_button.dart';

class EvoAppBar extends CommonAppBar {
  EvoAppBar({
    super.key,
    bool? centerTitle,
    super.title,
    super.leadingWidth,
    super.leading = const EvoAppBarLeadingButton(),
    super.actions,
    super.styleTitle,
    Color backgroundColor = Colors.transparent,
    super.bottom,
    super.automaticallyImplyLeading,
    super.statusBarIconForAndroid,
    super.statusBarIconForIos,
    double? titleSpacing,
  }) : super(
          centerTitle: centerTitle ?? false,
          statusBarColor: backgroundColor,
          titleSpacing: titleSpacing ?? 16.w,
        );
}
