import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../resources/resources.dart';
import '../../util/evo_flutter_wrapper.dart';
import 'evo_support_dialog.dart';

@Deprecated('Unused')
class AppbarSupportActionButton extends StatelessWidget {
  final void Function()? onClickButton;

  const AppbarSupportActionButton({this.onClickButton, super.key});

  @override
  Widget build(BuildContext context) {
    return CommonButton(
      onPressed: onClickButton ?? _showSupportDialog,
      style: evoButtonStyles.utility(ButtonSize.small),
      child: Text(
        EvoStrings.needHelp,
        style: evoTextStyles.bold(TextSize.sm, color: evoColors.primary100),
      ),
    );
  }

  Future<Widget?> _showSupportDialog() {
    return evoFlutterWrapper.showDialog<Widget>(builder: (BuildContext context) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 28),
          child: EvoSupportDialog(),
        ),
      );
    });
  }
}
