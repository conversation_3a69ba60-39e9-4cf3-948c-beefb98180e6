import 'package:flutter/material.dart';

import '../../resources/global.dart';
import '../../resources/ui_strings.dart';
import '../../util/functions.dart';
import '../../util/screen_util.dart';
import 'appbar_support_action_button.dart';
import 'evo_support_appbar.dart';

@Deprecated('Unused')
class NeedHelpSupportAppbar extends EvoSupportAppbar {
  NeedHelpSupportAppbar({super.leading = null, super.key})
      : super(
          actions: <Widget>[
            Padding(
              padding: EdgeInsets.only(right: 24.w),
              child: AppbarSupportActionButton(
                onClickButton: () {
                  evoUtilFunction.openInAppWebView(
                    title: EvoStrings.contactItemTitle,
                    url: WebsiteUrl.evoContactUrl,
                  );
                },
              ),
            ),
          ],
        );
}
