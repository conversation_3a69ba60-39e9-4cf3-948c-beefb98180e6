import 'package:flutter/material.dart';

import 'appbar_support_action_button.dart';
import 'evo_appbar.dart';
import 'evo_appbar_leading_button.dart';

@Deprecated('Unused')
class EvoSupportAppbar extends EvoAppBar {
  /// follow up the IconButton spec https://m3.material.io/components/icon-buttons/specs
  /// IconButton size is [kMinInteractiveDimension] = 48px, IconSize default is 24px
  /// so that padding 12px each side
  /// we want to margin 24px from device edge to icon so the value should be 12px
  static const double leadingLeftInset = 12;

  @visibleForTesting
  static const List<Widget> defaultActions = <Widget>[
    Padding(
      padding: EdgeInsets.only(right: 24),
      child: AppbarSupportActionButton(),
    ),
  ];

  @visibleForTesting
  static const Widget defaultLeading = Padding(
    padding: EdgeInsets.only(left: leadingLeftInset),
    child: EvoAppBarLeadingButton(),
  );

  EvoSupportAppbar({
    super.key,
    super.leading = defaultLeading,
    List<Widget>? actions,
  }) : super(actions: actions ?? defaultActions);

  @override
  Size get preferredSize => const Size.fromHeight(64);
}
