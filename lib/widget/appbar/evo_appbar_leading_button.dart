import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/back_button.dart';

import '../../resources/resources.dart';
import '../../util/screen_util.dart';

class EvoAppBarLeadingButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget? image;

  const EvoAppBarLeadingButton({super.key, this.onPressed, this.image});

  @override
  Widget build(BuildContext context) {
    return CommonBackButton(
      onPressed: onPressed,
      image: image ?? evoImageProvider.asset(EvoImages.icArrowBack, width: 24.w, height: 24.w),
    );
  }
}
