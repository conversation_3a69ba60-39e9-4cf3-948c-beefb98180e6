import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../resources/resources.dart';
import '../../util/extension.dart';
import '../../util/url_launcher_uri_wrapper.dart';

@Deprecated('Unused')
class EvoSupportDialog extends StatelessWidget {
  const EvoSupportDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: evoColors.defaultWhite,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          _getDialogHeader(),
          const SizedBox(height: 16),
          _getDialogContent(),
        ],
      ),
    );
  }

  Widget _getDialogHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: <Widget>[
        Text(
          EvoStrings.needHelp,
          style: evoTextStyles.bold(
            TextSize.xl,
            color: evoColors.greyScale100,
          ),
        ),
        GestureDetector(
          onTap: () {
            navigatorContext?.pop();
          },
          child: SizedBox.square(
              dimension: 24,
              child: evoImageProvider.asset(
                EvoImages.icCloseGrey,
                width: 24,
                height: 24,
              )),
        )
      ],
    );
  }

  Widget _getDialogContent() {
    final TextStyle textStyle = evoTextStyles.regular(
      TextSize.sm,
      color: evoColors.greyScale90,
    );
    return Row(
      children: <Widget>[
        CircleAvatar(
          radius: 24,
          child: evoImageProvider.asset(
            EvoImages.icMail,
            width: 19.5,
            height: 15,
          ),
        ),
        const SizedBox(width: 20),
        Expanded(
            child: Text.rich(
          TextSpan(children: <InlineSpan>[
            const TextSpan(text: '${EvoStrings.feedbackAndContact}\n'),
            TextSpan(
              style: textStyle.copyWith(
                  decorationColor: evoColors.primary100,
                  decoration: TextDecoration.underline,
                  color: evoColors.primary100),
              text: ContactInfo.contactSupportEmail,
              recognizer: TapGestureRecognizer()..onTap = _onTapContactSupport,
            ),
          ]),
          style: textStyle,
        ))
      ],
    );
  }

  void _onTapContactSupport() {
    final Uri uri = ContactInfo.contactSupportEmail.uriForSendMail();
    urlLauncherWrapper.launchUrl(uri);
  }
}
