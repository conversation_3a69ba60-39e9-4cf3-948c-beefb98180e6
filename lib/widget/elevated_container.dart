import 'package:flutter/material.dart';

import '../resources/resources.dart';

class ElevatedContainer extends StatelessWidget {
  final Widget child;
  final Color? surfaceColor;
  final VoidCallback? onTap;
  final double? width;
  final double? height;

  const ElevatedContainer({
    required this.child,
    this.surfaceColor,
    this.onTap,
    this.width,
    this.height,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      clipBehavior: Clip.hardEdge,
      width: width,
      height: height,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(8), boxShadow: <BoxShadow>[
        BoxShadow(
          color: evoColors.shadowColor,
          spreadRadius: -8,
          blurRadius: 24,
          offset: const Offset(0, 15),
        ),
      ]),
      child: Material(
          color: surfaceColor ?? evoColors.defaultWhite,
          child: InkWell(onTap: onTap, child: child)),
    );
  }
}
