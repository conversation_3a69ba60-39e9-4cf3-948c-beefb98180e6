import 'package:flutter/material.dart';

import '../resources/resources.dart';

class EvoSwitch extends StatefulWidget {
  final bool value;
  final ValueChanged<bool> onToggle;
  final double width;
  final double height;
  final double toggleSize;
  final Color? activeColor;
  final Color? inactiveColor;
  final Color toggleColor;
  final bool disabled;

  const EvoSwitch({
    required this.value,
    required this.onToggle,
    super.key,
    this.width = 52,
    this.height = 32,
    this.toggleSize = 24,
    this.activeColor,
    this.inactiveColor,
    this.toggleColor = Colors.white,
    this.disabled = false,
  });

  @override
  State<EvoSwitch> createState() => _EvoSwitchState();
}

class _EvoSwitchState extends State<EvoSwitch> with SingleTickerProviderStateMixin {
  late Color activeColor;
  late Color inactiveColor;

  late final Animation<Alignment> _toggleAnimation;
  late final AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    activeColor = widget.activeColor ?? evoColors.primary;
    inactiveColor = widget.inactiveColor ?? evoColors.greyScale60;

    _animationController = AnimationController(
      vsync: this,
      value: widget.value ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 300),
    );
    _toggleAnimation = AlignmentTween(
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.linear,
      ),
    );
  }

  @override
  void didUpdateWidget(EvoSwitch oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.value == widget.value) {
      return;
    }

    if (widget.value) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (BuildContext context, Widget? child) {
        return SizedBox(
          width: widget.width,
          child: Align(
            child: GestureDetector(
              onTap: () {
                if (!widget.disabled) {
                  widget.onToggle(!widget.value);
                }
              },
              child: Opacity(
                opacity: widget.disabled ? 0.6 : 1,
                child: Container(
                  width: widget.width,
                  height: widget.height,
                  padding: const EdgeInsets.all(4),
                  decoration: ShapeDecoration(
                    shape: const StadiumBorder(),
                    color: widget.value ? activeColor : inactiveColor,
                  ),
                  child: Align(
                    alignment: _toggleAnimation.value,
                    child: Container(
                      width: widget.toggleSize,
                      height: widget.toggleSize,
                      padding: const EdgeInsets.all(4.0),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: widget.toggleColor,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
