import 'package:flutter/material.dart';

import '../resources/resources.dart';

class TextFieldErrorMessage extends StatelessWidget {
  const TextFieldErrorMessage({super.key, this.errorMessage = ''});

  final String? errorMessage;

  @override
  Widget build(BuildContext context) {
    final String message = errorMessage ?? '';
    final bool hasError = message.isNotEmpty;

    return Offstage(
      offstage: !hasError,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          evoImageProvider.asset(
            EvoImages.icErrorTextField,
            height: 16,
          ),
          const SizedBox(width: 8),
          Flexible(
            child: Text(
              message,
              style: evoTextStyles.regular(TextSize.sm, color: evoColors.error100).copyWith(
                    fontWeight: FontWeight.w500,
                  ),
              textAlign: TextAlign.start,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
          const SizedBox(width: 8),
        ],
      ),
    );
  }
}
