import 'package:flutter/material.dart';

import '../buttons.dart';

class KykoBottomSheetAction extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isPositive;

  const KykoBottomSheetAction._({
    required this.text,
    required this.onPressed,
    required this.isPositive,
    super.key,
  });

  const KykoBottomSheetAction.positive({
    required String text,
    required VoidCallback onPressed,
    Key? key,
  }) : this._(
          text: text,
          onPressed: onPressed,
          key: key,
          isPositive: true,
        );

  const KykoBottomSheetAction.negative({
    required String text,
    required VoidCallback onPressed,
    Key? key,
  }) : this._(
          text: text,
          onPressed: onPressed,
          key: key,
          isPositive: false,
        );

  @override
  Widget build(BuildContext context) {
    if (isPositive) {
      return PrimaryButton(
        text: text,
        onTap: onPressed,
      );
    }

    return SecondaryButton(
      onTap: onPressed,
      text: text,
    );
  }
}
