import 'package:flutter/material.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../resources/resources.dart';
import '../../util/screen_util.dart';
import 'kyko_bottom_sheet_action.dart';

class KykoBottomSheet extends StatefulWidget {
  final String title;
  final bool? hasCloseButton;
  final Widget? content;
  final List<KykoBottomSheetAction> actions;

  KykoBottomSheet({
    required this.title,
    required this.content,
    List<KykoBottomSheetAction>? actions,
    this.hasCloseButton = false,
    super.key,
  }) : actions = actions ?? <KykoBottomSheetAction>[];

  @override
  State<KykoBottomSheet> createState() => _KykoBottomSheetState();
}

class _KykoBottomSheetState extends State<KykoBottomSheet> {
  final double horizontalPadding = 24.w;

  @override
  Widget build(BuildContext context) {
    final Radius radius = Radius.circular(EvoDimension.borderRadius);
    return Padding(
      padding: _getBottomSheetPadding(),
      child: Material(
          borderRadius: BorderRadius.only(
            topRight: radius,
            topLeft: radius,
          ),
          color: evoColors.defaultWhite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              _getHeader(),
              _getContent(),
              _getActions(),
            ],
          )),
    );
  }

  Widget _getHeader() {
    final List<Widget> widgets = <Widget>[];

    final Widget title = Align(
        alignment: Alignment.bottomLeft,
        child: Padding(
            padding: EdgeInsets.only(left: 24.w),
            child: Text(
              widget.title,
              style: evoTextStyles.medium(TextSize.h3),
            )));
    widgets.add(title);

    if (widget.hasCloseButton == true) {
      final Widget closeButton = Align(
          alignment: Alignment.topRight,
          child: Padding(
            padding: EdgeInsets.only(
              right: 16.w,
              top: 16.w,
            ),
            child: GestureDetector(
              onTap: () {
                navigatorContext?.pop();
              },
              child: evoImageProvider.asset(
                EvoImages.icCloseGrey,
                width: 24.w,
                height: 24.w,
              ),
            ),
          ));
      widgets.add(closeButton);
    }

    return SizedBox(
        height: 50.w,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: widgets,
        ));
  }

  Widget _getContent() {
    return Padding(
      padding: EdgeInsets.only(
        left: horizontalPadding,
        right: horizontalPadding,
        top: 16.w,
      ),
      child: widget.content,
    );
  }

  Widget _getActions() {
    final List<Widget> widgets = <Widget>[];

    for (final (int index, KykoBottomSheetAction item) in widget.actions.indexed) {
      widgets.add(Expanded(child: item));

      final bool isLastItem = widget.actions.length - 1 == index;
      if (!isLastItem) {
        widgets.add(SizedBox(width: 16.w));
      }
    }

    return Padding(
        padding: EdgeInsets.only(
          left: horizontalPadding,
          right: horizontalPadding,
          top: 24.w,
          bottom: EvoDimension.screenBottomPadding,
        ),
        child: SizedBox(
          width: 100,
          child: Row(
            children: widgets,
          ),
        ));
  }

  EdgeInsets _getBottomSheetPadding() {
    final EdgeInsets viewInsets = MediaQuery.of(context).viewInsets;
    return viewInsets.let((EdgeInsets insets) {
      return insets.copyWith(bottom: insets.bottom + context.screenPadding.bottom);
    });
  }
}
