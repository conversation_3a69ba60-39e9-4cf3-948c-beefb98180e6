import 'package:flutter/material.dart';

import '../resources/resources.dart';
import '../util/screen_util.dart';

class EvoListTileWidget extends StatelessWidget {
  final VoidCallback? onPress;
  final String title;
  final Color? titleColor;
  final Widget? leading;
  final EdgeInsets? leadingPadding;
  final Widget? trailing;
  final EdgeInsets trailingPadding;
  final EdgeInsets? padding;

  const EvoListTileWidget({
    required this.title,
    this.titleColor,
    this.onPress,
    this.leading,
    this.leadingPadding,
    this.trailing,
    this.trailingPadding = EdgeInsets.zero,
    this.padding,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPress,
      child: Padding(
        padding: padding ?? EdgeInsets.symmetric(horizontal: 14.w),
        child: Row(
          children: <Widget>[
            _getLeadingIcon(),
            Expanded(
              child: Text(
                title,
                style: evoTextStyles.regular(
                  TextSize.sm,
                  color: titleColor ?? evoColors.textNormal,
                ),
              ),
            ),
            _getTrailingWidget(),
          ],
        ),
      ),
    );
  }

  Widget _getLeadingIcon() {
    final Widget? wd = leading;
    if (wd == null) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: leadingPadding ?? EdgeInsets.only(right: 20.w),
      child: wd,
    );
  }

  Widget _getTrailingWidget() {
    final Widget? wd = trailing;
    if (wd == null) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: trailingPadding,
      child: wd,
    );
  }
}
