import 'package:flutter/material.dart';

import '../resources/resources.dart';

class EvoProgressBar extends StatelessWidget {
  final double progress;
  final Color? backgroundColor;
  final Color? valueColor;

  const EvoProgressBar({
    required this.progress,
    this.valueColor,
    this.backgroundColor,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return LinearProgressIndicator(
      value: progress,
      backgroundColor: backgroundColor ?? evoColors.accent70,
      valueColor: AlwaysStoppedAnimation<Color>(valueColor ?? evoColors.accent90),
    );
  }
}
