import 'package:flutter/material.dart';

import '../../../resources/resources.dart';
import '../../../util/screen_util.dart';

enum StatusBannerType { success, error, warning }

class StatusBannerWidget extends StatelessWidget {
  @visibleForTesting
  static final double defaultIconSize = 24.w;

  factory StatusBannerWidget.create({
    required StatusBannerType type,
    required String title,
    String? description,
    Widget? descriptionWidget,
  }) {
    switch (type) {
      case StatusBannerType.success:
        return StatusBannerWidget(
          primaryColor: evoColors.positive100,
          backgroundColor: evoColors.positive70,
          imageIconName: EvoImages.icSuccessBanner,
          title: title,
          description: description,
          descriptionWidget: descriptionWidget,
        );
      case StatusBannerType.error:
        return StatusBannerWidget(
          primaryColor: evoColors.error100,
          backgroundColor: evoColors.error70,
          imageIconName: EvoImages.icErrorBanner,
          title: title,
          description: description,
          descriptionWidget: descriptionWidget,
        );
      case StatusBannerType.warning:
        return StatusBannerWidget(
          primaryColor: evoColors.warning100,
          backgroundColor: evoColors.warning70,
          imageIconName: EvoImages.icWarningBanner,
          title: title,
          description: description,
          descriptionWidget: descriptionWidget,
        );
    }
  }

  final Color backgroundColor;
  final Color primaryColor;
  final String imageIconName;
  final String title;
  final String? description;
  final Widget? descriptionWidget;

  const StatusBannerWidget({
    required this.primaryColor,
    required this.backgroundColor,
    required this.imageIconName,
    required this.title,
    this.description,
    this.descriptionWidget,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.all(Radius.circular(8.w)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Row(
            children: <Widget>[
              evoImageProvider.asset(
                imageIconName,
                height: defaultIconSize,
                color: primaryColor,
              ),
              EvoDimension.space8,
              Expanded(
                child: Text(
                  title,
                  style: evoTextStyles.bold(
                    TextSize.sm,
                    color: primaryColor,
                  ),
                ),
              ),
            ],
          ),
          _buildDescription(
            description: description,
            descriptionWidget: descriptionWidget,
          ),
        ],
      ),
    );
  }

  Widget _buildDescription({String? description, Widget? descriptionWidget}) {
    final EdgeInsets defaultPadding = EdgeInsets.only(top: 16.w);
    if (description != null && description.isNotEmpty) {
      return Padding(
          padding: defaultPadding,
          child: Text(
            description,
            style: evoTextStyles.regular(
              TextSize.sm,
              color: evoColors.greyScale100,
            ),
          ));
    }

    if (descriptionWidget != null) {
      return Padding(
        padding: defaultPadding,
        child: descriptionWidget,
      );
    }

    return const SizedBox.shrink();
  }
}
