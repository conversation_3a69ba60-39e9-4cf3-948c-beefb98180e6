import 'package:flutter/widgets.dart';

import '../../resources/resources.dart';
import '../../util/screen_util.dart';

class InfoBannerWidget extends StatelessWidget {
  final List<String> info;

  const InfoBannerWidget({
    required this.info,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: evoColors.informationBackground,
        borderRadius: BorderRadius.circular(EvoDimension.borderRadius),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: info.map((String item) {
          return Text(
            '  •  $item',
            style: evoTextStyles.regular(TextSize.s, color: evoColors.grayBase),
          );
        }).toList(),
      ),
    );
  }
}
