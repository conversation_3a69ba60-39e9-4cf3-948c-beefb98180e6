import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../resources/resources.dart';
import '../../util/screen_util.dart';
import '../elevated_container.dart';
import 'transaction_item.dart';
import '../../feature/main_screen/card_page/models/transaction_model.dart';

class TransactionSection extends StatelessWidget {
  final VoidCallback? onViewMoreClicked;
  final List<TransactionModel>? items;

  /// remove it when integrate with API
  static List<TransactionModel> mockData = <TransactionModel>[
    TransactionModel(
      merchantName: 'Spotify',
      amount: 149.99,
      fourLastDigitOfCardNumberUsed: '5638',
      purchaseAt: '2022-11-02T15:04:05Z',
    ),
    TransactionModel(
      merchantName: 'Netfix.com',
      amount: 349.99,
      fourLastDigitOfCardNumberUsed: '5638',
      purchaseAt: '2022-11-02T15:04:05Z',
    ),
    TransactionModel(
      merchantName: 'VIU',
      amount: 249.99,
      fourLastDigitOfCardNumberUsed: '5638',
      purchaseAt: '2022-11-02T15:04:05Z',
    ),
  ];

  const TransactionSection({
    this.onViewMoreClicked,
    this.items,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        Row(
          children: <Widget>[
            Expanded(
              child: Text(
                EvoStrings.recentTransaction,
                style: evoTextStyles.bold(
                  TextSize.xl,
                  color: evoColors.textPassive,
                ),
              ),
            ),
            EvoDimension.space8,
            CommonButton(
              onPressed: onViewMoreClicked,
              style: evoButtonStyles.utility(ButtonSize.small),
              child: Text(
                EvoStrings.viewMore,
                style: evoTextStyles.bold(TextSize.sm, color: evoColors.primary),
              ),
            ),
          ],
        ),
        SizedBox(height: 20.w),
        _buildBodyContainer(context: context),
        EvoDimension.space24,
      ],
    );
  }

  Widget _buildBodyContainer({required BuildContext context}) {
    final List<TransactionModel>? result = items;
    if (result == null || result.isEmpty) {
      return _buildEmptySection(context: context);
    }
    return _buildTransactionList(result);
  }

  Widget _buildEmptySection({required BuildContext context}) {
    return ElevatedContainer(
      height: 90.h,
      child: Padding(
        padding: EdgeInsets.all(8.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            SizedBox.square(
              dimension: 24.w,
              child: evoImageProvider.asset(
                EvoImages.icInbox,
                color: evoColors.inputUnfocusedColor,
                fit: BoxFit.scaleDown,
              ),
            ),
            EvoDimension.space16,
            Flexible(
              child: Text(
                EvoStrings.noTransactionYet,
                style: evoTextStyles.regular(TextSize.sm, color: evoColors.inputUnfocusedColor),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionList(List<TransactionModel> items) {
    return ElevatedContainer(
      child: ListView.separated(
        physics: const NeverScrollableScrollPhysics(),
        itemCount: items.length,
        shrinkWrap: true,
        itemBuilder: (BuildContext context, int index) {
          final TransactionModel model = items[index];
          return TransactionItemWidget(model: model);
        },
        separatorBuilder: (BuildContext context, int index) {
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Divider(
              color: evoColors.greyScale60,
              height: 1, // You can customize the color
              thickness: 1, // You can customize the thickness
            ),
          );
        },
      ),
    );
  }
}
