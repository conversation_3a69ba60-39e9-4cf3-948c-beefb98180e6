import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../feature/transaction_details/transaction_details_screen.dart';
import '../../resources/resources.dart';
import '../../util/functions.dart';
import '../../feature/main_screen/card_page/models/transaction_model.dart';
import '../../../util/extension.dart';
import '../../util/screen_util.dart';

class TransactionItemWidget extends StatelessWidget {
  final TransactionModel model;
  static BorderRadius defaultBorderRadius = BorderRadius.circular(8.w);

  const TransactionItemWidget({required this.model, super.key});

  @override
  Widget build(BuildContext context) {
    return Material(
      borderRadius: defaultBorderRadius,
      color: evoColors.defaultWhite,
      child: InkWell(
        borderRadius: defaultBorderRadius,
        onTap: () {
          // TODO: Pass transaction id.
          // TODO: The current recent transaction model does not have transaction id,
          // TODO: update it when integrating API
          TransactionDetailsScreen.pushNamed(transactionId: 'id');
        },
        child: Container(
          padding: EdgeInsets.all(16.w),
          child: Row(
            children: <Widget>[
              /// heading image
              _buildHeadingIcon(),
              EvoDimension.space8,
              Expanded(
                child: _buildBodyWidget(),
              ),
              _buildTrailingIcon(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeadingIcon() {
    return SizedBox.square(
      dimension: 24.w,
      child: evoImageProvider.asset(
        EvoImages.icCard,
        color: evoColors.accent90,
        fit: BoxFit.scaleDown,
      ),
    );
  }

  Widget _buildTrailingIcon() {
    return evoImageProvider.asset(
      EvoImages.icArrowRight,
      color: evoColors.screenTitle,
      width: 20.w,
      height: 20.w,
      fit: BoxFit.contain,
    );
  }

  Widget _buildBodyWidget() {
    return Column(
      children: <Widget>[
        Row(
          children: <Widget>[
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(right: 5.w),
                child: Text(
                  model.merchantName,
                  style: evoTextStyles.regular(
                    TextSize.sm,
                    color: evoColors.textNormal,
                  ),
                ),
              ),
            ),
            Text(
              evoUtilFunction.evoFormatCurrency(model.amount),
              style: evoTextStyles.bold(
                TextSize.base,
                color: evoColors.textNormal,
              ),
            ),
          ],
        ),
        SizedBox(height: 5.w),
        Row(
          children: <Widget>[
            Expanded(
              child: Text(
                model.purchaseAtDateTime?.toStringFormatDate() ?? '',
                style: evoTextStyles.regular(
                  TextSize.sm,
                  color: evoColors.textNormal,
                ),
              ),
            ),
            Text(
              _getTransactionCardNumber(model),
              style: evoTextStyles.regular(
                TextSize.sm,
                color: evoColors.textActive,
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _getTransactionCardNumber(TransactionModel model) {
    final String maskedCardNumber =
        EvoStrings.maskTransactionCardNumber.replaceVariableByValue(<String>[
      model.fourLastDigitOfCardNumberUsed,
    ]);
    return '${EvoStrings.prefixViaText} $maskedCardNumber';
  }
}
