// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../resources/resources.dart';
import '../util/screen_util.dart';

/// A wrapper for screens that do not require an app bar but share the same status bar style.
/// The [SystemUiOverlayStyle] is based on properties of [EvoAppBar].
class NoAppBarWrapper extends StatelessWidget {
  final Widget child;

  const NoAppBarWrapper({required this.child, super.key});

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle(
        statusBarColor: evoColors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
      ),
      child: ColoredBox(
        color: evoColors.grayBackground,
        child: Safe<PERSON><PERSON>(
          child: Padding(
            padding: EdgeInsets.only(top: 16.w),
            child: child,
          ),
        ),
      ),
    );
  }
}
