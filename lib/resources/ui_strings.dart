abstract final class EvoStrings {
  // Common
  static const String existWarning = 'Nhấn lần nữa để thoát';
  static const String retry = 'Retry';
  static const String enterMPIN = 'Enter your 4-digit MPIN';
  static const String login = 'Log in';
  static const String needHelp = 'Need Help?';
  static const String backToHomePage = 'Back to home';
  static const String back = 'Back';
  static const String logout = 'Log out';
  static const String ok = 'OK';
  static const String hi = 'Hi';
  static const String unknownError = 'An error occurred.';
  static const String gotIt = 'Got It';
  static const String payNow = 'Pay Now';
  static const String copiedToClipboard = 'Copied to clipboard.';
  static const String reset = 'Reset';
  static const String textFieldLabelRequiredMark = '*';

  // Bottom navigation bar
  static const String bottomBarHomeLabel = 'Home';
  static const String bottomBarCardLabel = 'Cards';
  static const String bottomBarPayCardLabel = 'Pay Card';
  static const String bottomBarUsageLabel = 'Usage';
  static const String bottomBarProfileLabel = 'Profile';

  // Login flow
  static const String otpInvalidMsg = 'OTP invalid or expired.';

  //OTP
  static const String otpExpiredDesc = 'Your OTP expires in {0}.';
  static const String otpResendCodeDesc = 'Didn\'t receive a code?';
  static const String otpResendCode = 'Resend code';
  static const String verifyOtpScreenTitle = 'OTP Verification';
  static const String verifyOtpScreenDesc =
      'Please enter the 6-digit code sent to your mobile number ending in {0}.';
  static const String verifyEmailOtpScreenDesc =
      'Please enter the 6-digit code sent to your email.';

  // Profile Page
  static const String unKnowPhone = 'XXX XXX XXXX';
  static const String privacySecuritySection = 'Privacy and Security';
  static const String helpSection = 'Help';
  static const String aboutSection = 'About EVO';
  static const String creditCardItemTitle = 'Credit Card Basics';
  static const String faqItemTitle = 'FAQs';
  static const String contactItemTitle = 'Contact Us';
  static const String aboutItemTitle = 'Who is EVO?';
  static const String tncItemTitle = 'Terms & Conditions';
  static const String policyItemTitle = 'Privacy Policy';

  // Feedback page
  static const String feedbackAndContact = 'For any inquiries, contact EVO Customer Support:';

  // Button
  static const String ctaCancel = 'Cancel';
  static const String ctaProceed = 'Proceed';
  static const String ctaActive = 'Activate';
  static const String ctaYes = 'Yes';
  static const String ctaSkip = 'Skip';
  static const String close = 'Close';
  static const String viewMore = 'View more';
  static const String ctaLogInAgain = 'Log in again';
  static const String ctaApplyNow = 'Apply Now';
  static const String ctaSubmit = 'Submit';
  static const String ctaGoToHome = 'Go to Home';
  static const String ctaLetsGo = 'Let\'s go';

  // Input Phone Number Screen
  static const String loginDesc = 'Enter registered number to log in.';
  static const String mobileNumberLabel = 'Mobile Number';
  static const String countryPhoneCode = '+63';
  static const String doNotHaveAccount = 'Don’t have an account? ';
  static const String getStarted = 'Get started';

  static const String inputPinTitle = 'Enter your MPIN';
  static const String inputPinDesc = 'For your security, we need your 4-digit MPIN to proceed.';
  static const String loginScreenLoginWithBiometric = 'Log in with Biometrics';
  static const String useMPIN = 'Use MPIN';

  /// Login on old device
  static const String loginOnOldDeviceSwitchAccountCTA = 'Switch Account';
  static const String loginOnOldDeviceTitle = 'Welcome back';
  static const String loginLimitedExceededTitle = 'Maximum failed login\nattempts detected';

  // Session timeout
  static const String authorizationSessionTimeoutTitle = 'The session has expired';
  static const String localizedReasonForUsingBiometrics = 'EVO';
  static const String settingTitle = 'Go to Settings';
  static const String ignoreTitle = 'Cancel';
  static const String titleSessionTokenExpired = 'Session expired';

  //bio metrics error
  static const String lockedOutError = 'biometrics authentication is locked out';
  static const String permanentlyLockedOutError =
      'biometrics authentication permanently locked out';
  static const String openDeviceSecuritySettingDesc =
      'Biometric authentication is not set up on your device. Go to ‘Settings’ to add biometric authentication';
  static const String openDeviceSecuritySettingToUnlockDesc =
      'Please lock and unlock phone screen or go to ‘Settings’ to unlock biometric.';
  static const String biometricRequiredTitle = 'Biometric required';
  static const String biometricLockedTitle = 'Biometric has been locked';

  static const String maxTriesReached = 'Maximum tries reached';
  static const String limitResendOtp = 'Maximum resend reached';

  //Text for No Setup FaceId/FingerId Popup
  static const String signOutFail = 'Bạn vừa đăng xuất không thành công';
  static const String signOutSuccess = 'Bạn đã đăng xuất thành công';
  static const String faceText = 'khuôn mặt';
  static const String fingerText = 'vân tay';
  static const String faceFingerText = 'khuôn mặt/ vân tay';
  static const String authenticateText = 'Xác thực';
  static const String enableText = 'Kích hoạt';
  static const String biometricTokenUnUsableMessage =
      'Đăng nhập bằng {0} đã hết hạn. Vui lòng kích hoạt lại trong Cài đặt của ứng dụng';

  //Web view
  static const String webViewErrorTitle = 'Không tìm thấy trang';
  static const String webViewErrorDescription =
      'Trang của bạn không tồn tại hoặc\nđang có lỗi xảy ra';

  static const String biometricDeviceChangeWarring =
      '{0} đã bị thay đổi. Vui lòng kiểm tra lại thiết lập {1} trong máy';

  // Transaction history
  static const String allTransactionTitle = 'All Transactions';
  static const String totalMonthSpend = 'Total Month Spend: ';

  // Intro
  static const String introductionDescription1 = 'Activate and use your Kyko card instantly';
  static const String introductionDescription2 =
      'Secure online shopping with your Kyko virtual card';
  static const String introductionDescription3 = 'Manage your card anytime, anywhere';
  static const String introductionNext = 'Let’s go';

  //force update
  static const String forceUpdateDescription =
      'Cập nhật ngay phiên bản mới để có trải nghiệm mượt mà hơn & sử dụng các tính năng mới nhất.';
  static const String forceUpdateSubDesc = 'Đã có phiên bản mới';
  static const String forceUpdateAgree = 'Cập nhật';
  static const String forceUpdateSkip = 'Bỏ qua';

  //Reset Pin
  static const String contentSessionTokenExpiredSignIn =
      'Please log in again to\ncontinue using the EVO app';
  static const String textSubmitSessionTokenExpiredSignIn = 'Log in again';
  static const String contentSessionTokenExpiredResetPin =
      'To retry, please create a new\nMPIN again';

  // Detect Root/Jailbreak
  static const String titleBlockInsecureDeviceDialog = 'EVO chưa hỗ trợ thiết bị này';
  static const String descriptionBlockInsecureDeviceDialog =
      'EVO chưa hỗ trợ các thiết bị đã bẻ khoá để bảo đảm an toàn cho tài khoản của bạn';

  // Download File
  static const String downloadLinkFileSuccess = 'Tải file thành công';
  static const String downloadLinkFileFail = 'Tải file không thành công';
  static const String startDownloadLinkFile = 'Đang tải file, bạn đợi chút nhé';

  /// MPIN widget
  static const String mpinObscureText = '•';
  static const String forgotMPINQuestion = 'Forgot MPIN?';

  /// Enable biometric
  static const String activeBiometricTitle = 'Do you want to enable Biometrics?';
  static const String activeBiometricDesc =
      'Activating this feature will allow you to login and secure your transactions via Fingerprint or Face ID (in supported devices)';
  static const String activateBiometricsSuccessDesc =
      'You have successfully updated\nyour preferred settings';

  /// Login on new device popup
  static const String loginOnNewDeviceTitle = 'You’re logging in from a\nnew device';
  static const String loginOnNewDeviceDesc =
      'You will be automatically logged out\nfrom the other device.\nDo you want to proceed?';
  static const String loginOnNewDeviceConfirmCTA = 'Proceed';

  /// Logout dialog
  static const String logoutTitle = 'Are you sure you\nwant to log out?';

  /// Switch dialog
  static const String switchAccountTitle = 'You’re about to switch accounts';

  /// Idle / InActive
  static const String idleAWhileTitle = 'Are you still there?';
  static const String idleAWhileContent =
      'To keep your account safe from unauthorized access, we’ll log you out in ';
  static const String idleAWhileDurationInSec = 'seconds';
  static const String idleAWhileKeepMeLoggedInCTA = 'Keep me logged in';
  static const String idleAWhileLogMeOutCTA = 'Log me out';

  static const String inActiveTitle = 'See you again soon!';
  static const String inActiveDescription =
      'To protect your account from unauthorized access, we automatically log you out after you’ve been inactive for a while';

  /// Change MPIN
  static const String changeMPINLockedResourceTitle =
      'Maximum failed attempts to\nchange MPIN reached';
  static const String backToProfile = 'Back to Profile';
  static const String changeMPIN = 'Change MPIN';
  static const String createNewMPINTitle = 'Create your new MPIN';
  static const String createNewMPINDesc =
      'You will use this to secure your transactions on the EVO app.';
  static const String enableBiometrics = 'Enable Biometrics';

  /// Reset MPIN
  static const String resetMPINLimitExceededTitle =
      'Maximum failed attempts to\nreset MPIN reached';

  /// MPIN validate message
  static const String noSameAllDigitsMPIN = 'No same all digits MPIN number';
  static String noDecreasingMPIN = 'No decreasing MPIN number';
  static String noIncreasingMPIN = 'No increasing MPIN number';
  static String mustMatchMPINLength = 'MPIN number must be 4 digits';

  /// MPIN info banner
  static String infoBannerNotSamePrevious = 'Must not be similar to previous MPIN';
  static String infoBannerNotIncreasing = 'Not increasing (ex. 1,2,3,4)';
  static String infoBannerNotDecreasing = 'Not decreasing (ex. 4,3,2,1)';
  static String infoBannerNotSameDigits = 'Not same digits (ex. 4,4,4,4)';
  static String infoBannerMustBe4Digits = 'Must be 4 digits';

  /// Welcome screen
  static const String welcomeEvoTitle = 'Welcome to EVO';
  static const String welcomeMessage = 'We’re excited to welcome you in!';
  static const String activateAccountMessage = 'Activate Account';
  static const String alreadyHaveAccountMessage = 'Already have an account? ';
  static const String welcomeNewUserTitle = 'Welcome onboard, {0}!';

  // MPIN confirm reset
  static String confirmResetPinTitle = 'Confirm your MPIN';
  static String confirmResetPinDesc =
      'You will use this to secure your transactions on the Kyko app.';
  static String mpinNotMatch = 'MPIN does not match';

  /// Force logout dialog
  static const String forceLogoutTitle = 'You have been logged out of your account';

  /// Page Card
  static const String cardPageTitle = 'My Cards';
  static const String virtualCard = 'Virtual Card';
  static const String physicalCard = 'Physical Card';
  static const String recentTransaction = 'Recent Transactions';
  static const String noTransactionYet = 'No Transactions Yet';
  static const String ctaFreezeCard = 'Freeze Card';
  static const String ctaShowDetails = 'Show Details';
  static const String prefixViaText = 'via';
  static const String maskTransactionCardNumber = '···· {0}';
  static const String cardStateFrozenTitle = 'Frozen';
  static const String cardStateBlockedTitle = 'Blocked';
  static const String cardStateInactiveTitle = '· · · · · · · ·';
  static const String cardNumber = 'Card Number';
  static const String expiryDate = 'Expiry Date';
  static const String cvv = 'CVV';
  static const String nameOnCard = 'Name on Card';
  static const String activateCard = 'Activate Card';

  /// Inactive Card Panel
  static const String activateCardDesc = 'Activate yours virtual card to start';
  static const String activeCTAText = 'Activate now';

  /// Payment Summary Panel
  static const String overDue = 'Overdue';
  static const String paid = 'Paid';
  static const String payToday = 'Pay Today';
  static const String payTomorrow = 'Pay Tomorrow';
  static const String dueDate = 'Due Date';
  static const String amountToPayTitle = 'Amount To Pay';
  static const String amountToPayDesc = 'Kindly disregard if paid';
  static const String foreClosedTitle = 'Please Pay Your Balance';
  static const String totalAmountTitle = 'Total Amount Due';
  static const String paidAmountTitle = 'Minimum Amount Due';
  static const String lastAmountPaidTitle = 'Last Amount Paid';

  /// Credit Limit Panel
  static const String availableCredit = 'Available Credit';
  static const String cutOffDate = 'Cut-off Date';
  static const String creditLimitProgressPrefix = 'out of';
  static const String creditLimitProgressSuffix = 'total credit limit';

  /// Account Activation
  static const String haveNotApplyYetBtn = 'Haven’t Applied Yet?';
  static const String mobileNumberCheckTitle = 'Let’s activate your account!';
  static const String mobileNumberCheckDesc =
      'Enter the mobile number you used in the credit card application form.';
  static const String alreadyHaveAnAccountText = 'Already have an account? ';
  static const String activeAccountText = 'Activate Account';
  static const String activationAccountChunk1 =
      'By clicking "Activate Account", I confirm that I understand and agree to the ';
  static const String activationAccountChunk2 = ' and ';
  static const String activationAccountChunk3 = ' of Kyko.';
  static const String activationAccountTermsAndConditions = 'Terms and Conditions';
  static const String activationAccountPrivacyPolicy = 'Privacy Policy';
  static const String activeAccountErrorApplicationNotFoundTitle =
      'Sorry, we can’t seem to find your record.';
  static const String activeAccountErrorApplicationNotFoundDescription = 'Haven’t applied yet?';
  static const String activeAccountErrorApplicationRejectedTitle =
      'Sorry, please try applying again another time.';
  static const String activeAccountErrorApplicationRejectedDescription =
      'Thanks for your interest in applying for an EVO credit card. Unfortunately, we can’t proceed with your application at this time.';
  static const String activeAccountErrorApplicationPendingTitle =
      'Your application is still being processed.';
  static const String activeAccountErrorApplicationPendingDescription =
      'We’ll send the result to you as soon as we’re done processing your application.';
  static const String activeAccountCreateUsernameTitle = 'Let’s make a username.';
  static const String activeAccountCreateUsernameDesc =
      'Please create a username. You will be using this to log into the app.';
  static const String usernameLabel = 'Username';
  static const String usernameGuide1 = 'Your handle can\'t exceed 30 characters';
  static const String usernameGuide2 = 'It can only contain letters, numbers, and periods';
  static const String usernameGuide3 = 'It can\'t contain symbols or punctuation marks';
  static const String usernameGuide4 = 'It needs to be unique';
  static const String errorInvalidUsername = 'invalid username';
  static const String errorUsernameEmpty = 'A username is required to proceed';
  static const String errorUsernameMaxLength = 'Username exceeds 30 characters';
  static const String errorUsernameContainsSymbols = 'Username can’t contain symbols';
  static const String activateAccountSuccessDesc = 'Your account was successfully created!';
  static const String activateAccountCreatePinTitle = 'Create your MPIN';
  static const String activateAccountCreatePinDesc =
      'You will use this to secure your transactions on the Kyko app.';

  /// Activate virtual card
  static const String activeVirtualCardTitle =
      'Let’s activate your virtual credit card already! 👀';
  static const String activeVirtualCardDesc =
      'Activate your virtual card to start using it for transactions.';
  static const String activeVirtualCardGuideLine1 =
      'Use your card instantly for online transactions';
  static const String activeVirtualCardGuideLine2 = 'Easily set a spend cap for your card';
  static const String activeVirtualCardGuideLine3 = 'Fully control your card on the app';
  static const String ctaActiveVirtualCard = 'Activate Virtual Card';
  static const String ctaActiveLater = 'Activate Later';
  static const String bannerCardActivateFailedTitle = 'Card activation failed';
  static const String bannerCardNewCardTitle = 'Your new card is ready to be activated';
  static const String bannerCardNewCardDesc = 'Activate your card now to start using it.';

  /// Virtual Card Activated
  static const String virtualCardActivatedTitle = 'Card Activated ⚡️\nWelcome, {0}! 🎉';
  static const String virtualCardActivatedIntroTitle =
      'Fully Enjoy Your Credit Card Experience With These Reminders:';
  static const String virtualCardActivatedIntroItem1 =
      'Remember your due date! Pay on-time to avoid paying penalty fees';
  static const String virtualCardActivatedIntroItem2 =
      'Protect your card from unwanted access and keep your card details to yourself';
  static const String virtualCardActivatedIntroItem3 =
      'Spend responsibly! Only spend what you can repay.';

  static const String activateVirtualCardSuccess = 'Your Virtual Card Is Ready For Use!';
  static const String activatePhysicalCardSuccess = 'Your Physical Card Is Ready For Use!';
  static const String goToCardsButton = 'Go to Cards';

  // Card Blocked
  static const String blockedCardBannerTitle = 'This card has been blocked.';
  static const String blockedCardBannerByUserDesc =
      'If you want to order a new credit card, please contact customer support.';
  static const String blockedCardBannerByBankDesc =
      'We noticed that you’re missing a few payments on the card. Please settle so you can use the card again.\n\nIf you have already paid, but still receive this screen, please contact customer support.';

  /// Face Capture Check (Intro screen)
  static const String faceCaptureCheckTitle = 'Glad to have you here,\n{0}.';
  static const String faceCaptureCheckSubtitle = 'Get ready for selfie verification.';
  static const String faceCaptureCheckDesc =
      'We just want to confirm if this is really you. Shoot your photos in a well-lit room and do not wear anything that covers your face.';

  /// Selfie Verification
  static const String selfieAppBarTitle = 'Selfie Verification';
  static const String selfieInitializingDesc =
      'Initializing selfie verification,\nplease wait for a moment';
  static const String selfieProcessingDesc =
      'System is checking your photo,\nplease wait for a moment';
  static const String selfieSafeInfoDesc = 'Your information is encrypted and secured';
  static const String selfieErrorTitle = 'Face authentication unsuccessful!';
  static const String selfieErrorSubtitle = 'Please try again!';
  static const String selfieSuccessTitle = 'Selfie verification successful!';

  // Last 4 digits check
  static const String last4DigitsCheckTitle = 'Enter The Last 4 Digits Of Your Card Number';
  static const String last4DigitsCheckDesc =
      'To keep your account safe, make sure no one’s looking before proceeding';

  /// freeze card
  static const String freezeCarErrorTitle = 'Cannot process freezing your card';
  static const String freezeCarErrorDesc1 =
      'We can’t seem to connect to our system properly. If your purpose is urgent, please ';
  static const String freezeCarErrorDesc2 = 'contact customer support.';

  // Camera permission
  static const String cameraPermissionTitle = 'Permission to use camera';
  static const String cameraPermissionDesc =
      'Access to the camera has been prohibited, please enable it in the “Settings” to continue';

  static const String tryAgainLater = 'Please try again later!';

  // Transaction details
  static const String transactionDetailsTitle = 'Transaction details';
  static const String paymentTitle = 'Payment';
  static const String amountTitle = 'Amount';
  static const String transactionIdTitle = 'Transaction ID';
  static const String paidToTitle = 'Paid to';
  static const String paidByTitle = 'Paid by';
  static const String dateAndTimeTitle = 'Date and time';
  static const String postingDateTitle = 'Posting Date';

  // Verify Username
  static const String verifyUsernameTitle = 'Hello, can you input your username?';
  static const String forgotUsernameTitle = 'Forgot Username?';

  // Activation Status
  static const String activationRejectedTitle = 'Sorry, please try applying again another time.';
  static const String activationRejectedDesc =
      'Thanks for your interest in applying for a Kyko credit card. Unfortunately, we can\'t proceed with your application at this time.';
  static const String activationProcessingTitle = 'Your application is still being processed.';
  static const String activationProcessingDesc =
      'We\'ll send the result to you via SMS and email as soon as we\'re done processing your application.';
  static const String activationNotFoundTitle = 'Sorry, we can\'t seem to find your record.';
  static const String activationNotFoundDesc =
      'It seems you haven\'t applied for a Kyko credit card using that number.';
  static const String activationExistTitle = 'You already have an activated account with Kyko.';
  static const String activationExistDesc = 'You can try logging in instead.';

  // Account activation - Verify email
  static const String verifyEmailTitle = 'Verify your email address';
  static const String verifyEmailDesc =
      'To begin using Kyko, you will need to verify your email address.';
  static const String verifyEmailSubtitle = 'Is this your email address?';
  static const String sendEmailCodeBtn = 'Yes, send me a verification code';
  static const String changeEmailBtn = 'No, change my email address';

  // Account activation - Input email
  static const String inputEmailTitle = 'Input your correct email address';
  static const String sendEmailCodeBtn2 = 'Send me a verification code';
  static const String errorInvalidEmailAddress = 'Invalid email address';

  // Account activation - Verify OTP
  static const String ctaBackToActivationAccount = 'Back to account activation';
  static const String contentActivationAccountSessionTokenExpired =
      'You can redo the account activation process by clicking the button below.';
}
