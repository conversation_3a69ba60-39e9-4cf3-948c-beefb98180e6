import 'package:flutter/widgets.dart';

import '../util/screen_util.dart';
import '../widget/evo_text_field.dart';

abstract final class EvoDimension {
  static const double figmaScreenHeight = 800;
  static const double figmaScreenWidth = 360;

  /// aspect ratio 342:216 is referred from figma design
  /// https://www.figma.com/design/4sNM28bR3NtK59bcLRpYn8/RBank-CC-Project-(Design-System)?node-id=2189-2267&node-type=frame&t=c9vsa5awJAKxiLXu-0
  static double defaultCardRatio = 342.w / 216.h;

  /// https://www.figma.com/design/o0mjhHZYvsfTQfK4X14tj7/Kyko-Design-System?node-id=1-134&p=f&t=MlW9xmEIXPHq6Ddk-0
  static double borderRadius = 8.w;

  /// refer figma design:
  /// https://www.figma.com/design/4sNM28bR3NtK59bcLRpYn8/RBank-CC-Project-(Design-System)?node-id=3-1782&node-type=canvas&t=c9vsa5awJAKxiLXu-0
  static SizedBox space4 = SizedBox.square(dimension: 4.w);
  static SizedBox space8 = SizedBox.square(dimension: 8.w);
  static SizedBox space16 = SizedBox.square(dimension: 16.w);
  static SizedBox space24 = SizedBox.square(dimension: 24.w);
  static SizedBox space32 = SizedBox.square(dimension: 32.w);
  static SizedBox space48 = SizedBox.square(dimension: 48.w);
  static SizedBox space64 = SizedBox.square(dimension: 64.w);

  static double screenHorizontalPadding = 16.w;

  /// TextField will included [EvoTextField.outerFocusedBorderPadding]
  /// for matching design we must to reduce the [screenHorizontalPadding]
  static double screenHorizontalPaddingWithTextField =
      screenHorizontalPadding - EvoTextField.focusedBorderPadding;

  static double screenBottomPadding = 54.w;
}
