import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/resources.dart';

import '../util/screen_util.dart';
import 'resources.dart';

/// Figma: https://www.figma.com/design/o0mjhHZYvsfTQfK4X14tj7/Kyko-Design-System?node-id=15-1141&t=NA6VvSctpuhcrKTW-4
class EvoButtonStyles extends CommonButtonStyles {
  @visibleForTesting
  WidgetStateProperty<T?> onState<T>({T? all, T? fallback, T? pressed, T? disabled}) {
    if (all != null) {
      return WidgetStateProperty.all(all);
    }
    return WidgetStateProperty.resolveWith((Set<WidgetState> states) {
      if (states.contains(WidgetState.disabled)) {
        return disabled ?? fallback;
      }
      if (states.contains(WidgetState.pressed)) {
        return pressed ?? fallback;
      } else {
        return fallback;
      }
    });
  }

  @visibleForTesting
  Size getMinSize(ButtonSize size) {
    return switch (size) {
      ButtonSize.small => Size(0, 38.w),
      ButtonSize.medium => Size(0, 44.w),
      _ => <PERSON>ze(0, 48.w)
    };
  }

  @override
  ButtonStyle primary(
    ButtonSize size, {
    bool hasPadding = true,
    @Deprecated('Unused') MaterialTapTargetSize? tapTargetSize,
    @Deprecated('Unused') Brightness? brightness,
  }) {
    final ButtonStyle style = super.primary(size, tapTargetSize: MaterialTapTargetSize.shrinkWrap);
    return style.copyWith(
      overlayColor: onState(all: evoColors.transparent),
      minimumSize: onState(all: getMinSize(size)),
      padding: hasPadding ? style.padding : onState(all: EdgeInsets.zero),
      backgroundColor: onState(
        fallback: evoColors.primaryBase,
        pressed: evoColors.primaryDark,
        disabled: evoColors.grayBorders,
      ),
      foregroundColor: onState(all: evoColors.white),
    );
  }

  @override
  ButtonStyle secondary(
    ButtonSize size, {
    bool hasPadding = true,
    @Deprecated('Unused') MaterialTapTargetSize? tapTargetSize,
  }) {
    final ButtonStyle style =
        super.secondary(size, tapTargetSize: MaterialTapTargetSize.shrinkWrap);
    return style.copyWith(
      overlayColor: onState(all: evoColors.transparent),
      minimumSize: onState(all: getMinSize(size)),
      padding: hasPadding ? style.padding : onState(all: EdgeInsets.zero),
      side: onState(
        fallback: BorderSide(color: evoColors.primaryBase, width: 1.w),
        pressed: BorderSide(color: evoColors.primaryDark, width: 1.w),
        disabled: BorderSide(color: evoColors.grayBorders, width: 1.w),
      ),
      foregroundColor: onState(
        fallback: evoColors.primaryBase,
        pressed: evoColors.primaryDark,
        disabled: evoColors.grayBorders,
      ),
      backgroundColor: onState(
        fallback: evoColors.transparent,
        pressed: evoColors.primaryBackground,
      ),
    );
  }

  @override
  ButtonStyle tertiary(
    ButtonSize size, {
    bool hasPadding = true,
    @Deprecated('Unused') bool? isHasShadow,
    @Deprecated('Unused') MaterialTapTargetSize? tapTargetSize,
    @Deprecated('Unused') EdgeInsets? padding,
  }) {
    final ButtonStyle style = super.tertiary(
      size,
      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      isHasShadow: false,
    );
    return style.copyWith(
      overlayColor: onState(all: evoColors.transparent),
      minimumSize: onState(all: getMinSize(size)),
      padding: hasPadding ? style.padding : onState(all: EdgeInsets.zero),
      foregroundColor: onState(
        fallback: evoColors.primaryBase,
        pressed: evoColors.primaryDark,
        disabled: evoColors.grayBorders,
      ),
      backgroundColor: onState(all: evoColors.transparent),
    );
  }

  @Deprecated('Unused')
  ButtonStyle utility(
    ButtonSize size, {
    MaterialTapTargetSize tapTargetSize = MaterialTapTargetSize.shrinkWrap,
    EdgeInsets? padding,
    double? minimumHeight
  }) {
    return ButtonStyle();
  }

  @Deprecated('Unused')
  ButtonStyle popup(
    ButtonSize size, {
    MaterialTapTargetSize tapTargetSize = MaterialTapTargetSize.shrinkWrap,
    FontWeight fontWeight = FontWeight.w700,
  }) {
    return ButtonStyle();
  }
}
