import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/resources.dart';

import '../util/screen_util.dart';

/// Refer Figma:https://www.figma.com/design/o0mjhHZYvsfTQfK4X14tj7/Kyko-Design-System?node-id=37-820&t=jhsHXi4g7kOD6BIQ-0
enum TextSize {
  xs(11, 14),
  s(12, 14),
  base(14, 20),
  lg(16, 24),
  h5(18, 24),
  h4(20, 24),
  h3(24, 28),
  h2(28, 32),
  h1(32, 40),
  h0(48, 56),

  /// deprecated
  xl3(31, 40),
  xl2(25, 32),
  xl(20, 26),
  sm(13, 16);

  final double fontSize;
  final double lineHeight;

  double get height => lineHeight / fontSize;

  const TextSize(this.fontSize, this.lineHeight);
}

class EvoTextStyles extends CommonTextStyles {
  static const String defaultFontFamily = 'Gabarito';

  EvoTextStyles() : super(fontFamily: defaultFontFamily);

  TextStyle regular(TextSize size, {Color? color}) => TextStyle(
        fontFamily: fontFamily,
        color: color ?? commonColors.textActive,
        fontSize: size.fontSize.sp,
        height: size.height,
        fontWeight: FontWeight.w400,
      );

  TextStyle medium(TextSize size, {Color? color}) => TextStyle(
        fontFamily: fontFamily,
        color: color ?? commonColors.textActive,
        fontSize: size.fontSize.sp,
        height: size.height,
        fontWeight: FontWeight.w500,
      );

  TextStyle semibold(TextSize size, {Color? color}) => TextStyle(
        fontFamily: fontFamily,
        color: color ?? commonColors.textActive,
        fontSize: size.fontSize.sp,
        height: size.height,
        fontWeight: FontWeight.w600,
      );

  TextStyle bold(TextSize size, {Color? color}) => TextStyle(
        fontFamily: fontFamily,
        color: color ?? commonColors.textActive,
        fontSize: size.fontSize.sp,
        height: size.height,
        fontWeight: FontWeight.w700,
      );

  /// Figma: https://www.figma.com/design/o0mjhHZYvsfTQfK4X14tj7/Kyko-Design-System?node-id=15-1141&t=NA6VvSctpuhcrKTW-4
  @override
  TextStyle button(
    ButtonSize size,
    @Deprecated('Unused') Color color, {
    @Deprecated('Unused') double? fontSize,
  }) {
    final TextStyle style = switch (size) {
      ButtonSize.small => semibold(TextSize.s),
      ButtonSize.medium => semibold(TextSize.base),
      _ => semibold(TextSize.lg)
    };
    return style.copyWith(leadingDistribution: TextLeadingDistribution.even);
  }
}
