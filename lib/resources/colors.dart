import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/colors.dart';

/// Refers Figma: https://www.figma.com/design/4sNM28bR3NtK59bcLRpYn8/RBank-CC-Project-(Design-System)?node-id=1879-4102&t=kAjDmHEWJ6MUZYLG-0
class EvoColors extends CommonColors {
  // region page
  @override
  Color get primary => primaryBase;

  @override
  Color get foreground => greyScale100;

  @override
  Color get background => greyScale50;

  @override
  Color get error => errorBase;

  @override
  Color get highlighted => primary100;

  @override
  Color get appBarShadow => const Color(0x14000000);

  // bottom sheet color
  @override
  Color get bottomSheetBackground => defaultWhite;

  @override
  Color get bottomSheetSelectedItem => primary100;

  @override
  Color get bottomSheetUnselectedItem => greyScale80;

  // endregion

  // region text
  @override
  Color get textActive => grayText;

  @override
  Color get textPassive => greyScale90;

  @override
  Color get textPassive2 => const Color(0xFF999999);

  @override
  Color get textHint => greyScale70;

  @override
  Color get textNormal => const Color(0xFF626A84);

  @override
  Color get icon => accent90;

  // endregion

  // region TextField
  @override
  Color get focusedTextFieldBorder => primary90;

  @override
  Color get textFieldBorder => greyScale70;

  @override
  Color get disableTextFieldBorder => greyScale60;

  @override
  Color get disableTextFieldBg => greyATrans5;

  @override
  Color get textFieldBg => defaultWhite;

  @override
  Color get textSelectedBg => defaultWhite;

  // endregion

  // region button
  // primary button
  @override
  Color get primaryButtonForeground => Colors.white;

  @override
  Color get primaryButtonBg => greyScale100;

  @override
  Color get primaryButtonForegroundDisable => Colors.white;

  @override
  Color get primaryButtonBgDisable => greyScale70;

  // secondary button
  @override
  Color get secondaryButtonForeground => greyScale100;

  @override
  Color get secondaryButtonBg => greyScale50;

  @override
  Color get secondaryButtonForegroundDisable => greyScale70;

  @override
  Color get secondaryButtonBgDisable => greyScale50;

  // accent button
  @override
  Color get accentButtonForeground => Colors.white;

  @override
  Color get accentButtonBg => primary100;

  @override
  Color get accentButtonForegroundDisable => Colors.white;

  @override
  Color get accentButtonBgDisable => primary80;

  // tertiary button
  @override
  Color get tertiaryButtonForeground => greyScale100;

  @override
  Color get tertiaryButtonBg => defaultTransparent;

  @override
  Color get tertiaryButtonForegroundDisable => greyScale70;

  @override
  Color get tertiaryButtonBgDisable => defaultTransparent;

  // endregion

  // otp text input field
  @override
  Color get inputFocusedColor => primary100;

  @override
  Color get inputUnfocusedColor => const Color(0xFFA1A7BA);

  @override
  Color get selectedRadioButton => primary100;

  // WebView loading Progress Bg;
  @override
  Color get webViewProgressBg => accent70;

  @override
  Color get webViewProgressValue => accent90;

  @override
  Color get iconColor => greyScale100;

  @override
  Color get loadingViewColor => accent90;

  @override
  Color get negativeButtonBg => defaultTransparent;

  @override
  Color get negativeButtonBgDisable => defaultTransparent;

  @override
  Color get negativeButtonForeground => defaultTransparent;

  @override
  Color get negativeButtonForegroundDisable => defaultTransparent;

  /// Additional Colors
  Color get activeIndicator => const Color(0xFF222222);

  Color get shadowColor => const Color(0x0A000000);

  /// Define Evo's Color System
  Color get defaultWhite => Colors.white;

  Color get defaultBlack => Colors.black;

  Color get defaultTransparent => Colors.transparent;

  /// grey scale

  Color get greyScale100 => const Color(0xFF1D1D1D);

  Color get greyScale90 => const Color(0xFF5E5E5E);

  Color get greyScale80 => const Color(0xFF999999);

  Color get greyScale75 => const Color(0xFFC2C2C2);

  Color get greyScale70 => const Color(0xFFD1D1D1);

  Color get greyScale60 => const Color(0xFFE9E9E9);

  Color get greyScale50 => const Color(0xFFFAFAFA);

  /// grey scale - alpha
  Color get greyATrans10 => const Color(0x141d1d1d);

  Color get greyATrans5 => const Color(0x0a1d1d1d);

  /// primary
  Color get primary100 => const Color(0xFF09B364);

  Color get primary90 => const Color(0xFF1DC978);

  Color get primary80 => const Color(0xFFD5F6E7);

  Color get primary70 => const Color(0xFFECF9F3);

  Color get primary00 => const Color(0xFF0D6D40);

  /// end primary

  /// Accent
  Color get accent100 => const Color(0xFF37366F);

  Color get accent90 => const Color(0xFF6867A8);

  Color get accent70 => const Color(0xFFEBEBFF);

  /// info
  Color get info100 => const Color(0xFF1F71F4);

  Color get info80 => const Color(0xFFBFDBFE);

  Color get info70 => const Color(0xFFEBF3FF);

  /// positive
  Color get positive100 => const Color(0xFF3CBE69);

  Color get positive70 => const Color(0xFFEAFAF0);

  /// Warning
  Color get warning100 => const Color(0xFFFFB224);

  Color get warning70 => const Color(0xFFFFF6E5);

  /// Error
  Color get error100 => const Color(0xFFE54D2E);

  Color get error70 => const Color(0xFFFFE5DC);

  /// Utility button
  Color get utilityButtonForeground => primary100;

  Color get utilityButtonBg => defaultTransparent;

  Color get utilityButtonForegroundDisable => primary70;

  Color get utilityButtonBgDisable => defaultTransparent;

  Color get utilityButtonBorder => const Color(0xFFA1A7BA);

  @Deprecated('Deprecated color')
  Color get screenTitle => const Color(0xFF16181D);

  /// Popup button
  Color get popupButtonForeground => primary100;

  Color get popupButtonBg => defaultTransparent;

  Color get popupButtonForegroundDisable => primary70;

  Color get popupButtonBgDisable => defaultTransparent;

  Color get popupButtonBorder => const Color(0x1A000000);

  /// Activate Card Success
  Color get activateCardSuccessText => const Color(0xFFFCFDFD);

  // KYKO Design System
  // Figma: https://www.figma.com/design/o0mjhHZYvsfTQfK4X14tj7/Kyko-Design-System?node-id=0-1
  final Color primaryBase = const Color(0xFFB83A8D);
  final Color primaryBackground = const Color(0xFFFEF7FB);
  final Color primaryInteractive = const Color(0xFFFADDED);
  final Color primaryBorders = const Color(0xFFE5ADCD);
  final Color primaryDark = const Color(0xFFA82A7F);
  final Color primaryText = const Color(0xFF611948);

  final Color secondaryBase = const Color(0xFF65BA74);
  final Color secondaryBackground = const Color(0xFFF5FBF5);
  final Color secondaryInteractive = const Color(0xFFDAF1DB);
  final Color secondaryBorders = const Color(0xFFB2DDB5);
  final Color secondaryDark = const Color(0xFF2A7E3B);
  final Color secondaryText = const Color(0xFF203C25);

  final Color accentBase = const Color(0xFF8CA8DC);
  final Color accentBackground = const Color(0xFFF7F9FD);
  final Color accentInteractive = const Color(0xFFE1EAFB);
  final Color accentBorders = const Color(0xFFC0D3F5);
  final Color accentDark = const Color(0xFF4C6695);
  final Color accentText = const Color(0xFF203050);

  final Color successBase = const Color(0xFF30A46C);
  final Color successBackground = const Color(0xFFF4FBF6);
  final Color successInteractive = const Color(0xFFD6F1DF);
  final Color successBorders = const Color(0xFF8ECEAA);
  final Color successText = const Color(0xFF193B2D);

  final Color warningBase = const Color(0xFFFFBA18);
  final Color warningBackground = const Color(0xFFFEFBE9);
  final Color warningInteractive = const Color(0xFFFFEE9C);
  final Color warningBorders = const Color(0xFFE9C162);
  final Color warningText = const Color(0xFF4F3422);

  final Color errorBase = const Color(0xFFE5484D);
  final Color errorBackground = const Color(0xFFFFF7F7);
  final Color errorInteractive = const Color(0xFFFFDBDC);
  final Color errorBorders = const Color(0xFFF4A9AA);
  final Color errorText = const Color(0xFF641723);

  final Color informationBase = const Color(0xFF0090FF);
  final Color informationBackground = const Color(0xFFF4FAFF);
  final Color informationInteractive = const Color(0xFFD5EFFF);
  final Color informationBorders = const Color(0xFF8EC8F6);
  final Color informationText = const Color(0xFF113264);

  final Color grayBase = const Color(0xFF8D8D86);
  final Color grayBackground = const Color(0xFFFDFDFC);
  final Color grayInteractive = const Color(0xFFE9E8E6);
  final Color grayBorders = const Color(0xFFCFCECA);
  final Color grayText = const Color(0xFF21201C);

  final Color black = const Color(0xFF000000);
  final Color white = const Color(0xFFFFFFFF);
  final Color transparent = const Color(0x00000000);
}
