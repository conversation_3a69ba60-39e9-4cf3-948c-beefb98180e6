import 'package:flutter/material.dart';

import 'resources.dart';

abstract final class EvoTheme {
  static ThemeData get themeData => ThemeData(
        scaffoldBackgroundColor: evoColors.grayBackground,
        fontFamily: EvoTextStyles.defaultFontFamily,

        /// this configuration is for the text in TextField is highlighted (selected)
        textSelectionTheme: TextSelectionThemeData(
          cursorColor: evoColors.info100,
          selectionColor: evoColors.info80,
          selectionHandleColor: evoColors.info100,
        ),
        appBarTheme: const AppBarTheme(
          /// set the [surfaceTintColor] as white.
          /// To prevent the AppBar from dimming when scrolling and
          /// consistent with app design,
          surfaceTintColor: Colors.white,
        ),
      );
}
