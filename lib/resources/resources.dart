import 'package:evoapp/resources/text_styles.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';

import 'button_styles.dart';
import 'colors.dart';
import 'input_borders.dart';

export 'colors.dart';
export 'dimensions.dart';
export 'global.dart';
export 'images.dart';
export 'text_styles.dart';
export 'ui_strings.dart';

final CommonImageProvider evoImageProvider = getIt.get<CommonImageProvider>();
final EvoTextStyles evoTextStyles = getIt.get<CommonTextStyles>() as EvoTextStyles;
final EvoColors evoColors = getIt.get<CommonColors>() as EvoColors;
final EvoButtonStyles evoButtonStyles = getIt.get<CommonButtonStyles>() as EvoButtonStyles;
final EvoInputBorders evoInputBorders = getIt.get<EvoInputBorders>();
