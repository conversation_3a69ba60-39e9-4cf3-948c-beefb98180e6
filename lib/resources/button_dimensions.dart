import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/button_dimensions.dart';
import 'package:flutter_common_package/resources/dimensions.dart';

import '../util/screen_util.dart';

class EvoButtonDimensions extends CommonButtonDimensions {
  @override
  double getCornerRadius(@Deprecated('Unused') ButtonSize buttonSize) => 8.w;

  /// Returns paddings when the button contains text only as default.
  /// For buttons with icons, paddings can be set manually by switching [hasPadding] to false.
  @override
  EdgeInsets getPadding(ButtonSize buttonSize) {
    switch (buttonSize) {
      case ButtonSize.small:
        return EdgeInsets.symmetric(horizontal: 12.w);
      default:
        return EdgeInsets.symmetric(horizontal: 16.w);
    }
  }

  @Deprecated('This is not aware of line height, font size is set in EvoTextStyles.button().')
  @override
  double? getFontSize(ButtonSize buttonSize) => null;
}
