// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/foundation.dart';

import '../../feature/account_activation/handler/account_activation_ui_handler.dart';

abstract class ActivateAccountRequest {
  abstract final AccountActivationType type;
  final String? sessionToken;

  ActivateAccountRequest({this.sessionToken});

  @mustCallSuper
  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'type': type.value,
    };
  }
}

class ActivateAccountVerifyOTPRequest extends ActivateAccountRequest {
  final String otp;

  ActivateAccountVerifyOTPRequest({
    required this.otp,
    required super.sessionToken,
  });

  @override
  AccountActivationType type = AccountActivationType.verifyOTP;

  @override
  Map<String, dynamic> toJson() {
    return super.toJson()
      ..addAll(<String, dynamic>{
        'otp': otp,
      });
  }
}

class ActivateAccountCreateUsernameRequest extends ActivateAccountRequest {
  final String username;

  ActivateAccountCreateUsernameRequest({
    required this.username,
    required super.sessionToken,
  });

  @override
  Map<String, dynamic> toJson() {
    return super.toJson()
      ..addAll(<String, dynamic>{
        'user_name': username,
      });
  }

  @override
  AccountActivationType get type => AccountActivationType.createUsername;
}

class ActivateAccountVerifyPhoneNumberRequest extends ActivateAccountRequest {
  final String? phoneNumber;

  ActivateAccountVerifyPhoneNumberRequest({
    this.phoneNumber,
    super.sessionToken,
  });

  @override
  AccountActivationType get type => AccountActivationType.none;

  @override
  Map<String, dynamic> toJson() {
    return super.toJson()
      ..addAll(<String, dynamic>{
        'phone_number': phoneNumber,
      });
  }
}

/// ignore coverage since it will implement on specific ticket
/// coverage:ignore-start

/// TODO: update in [https://trustingsocial1.atlassian.net/browse/ENBCC-205]
class ActivateAccountVerifyPinRequest extends ActivateAccountRequest {
  @override
  AccountActivationType get type => AccountActivationType.createPin;
}

/// TODO: update in [https://trustingsocial1.atlassian.net/browse/ENBCC-201]
class ActivateAccountVerifySelfieRequest extends ActivateAccountRequest {
  @override
  AccountActivationType get type => AccountActivationType.verifySelfie;
}

/// coverage:ignore-end
