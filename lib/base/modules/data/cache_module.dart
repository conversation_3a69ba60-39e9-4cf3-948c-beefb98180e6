import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/data/local_storage/common_local_storage_helper.dart';
import 'package:flutter_common_package/data/local_storage/common_shared_preferences_helper.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';
import '../../../util/cache/cache_manager.dart';
import '../../../util/cache/cache_manager_impl.dart';
import '../../../util/cache/memory_cache.dart';
import '../../../util/cache/memory_cache_impl.dart';
import '../../../util/cache/disk_cache.dart';
import '../../../util/cache/disk_cache_impl.dart';

/// Cache module that provides caching functionality dependencies.
///
/// This module handles memory caching, disk caching, and cache management.
/// It extends the common package storage capabilities with app-specific caching needs.
class EvoCacheModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.cache;

  @override
  List<Type> get dependencies => [
    CacheManager,
    MemoryCache,
    DiskCache,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register memory cache
    if (!getIt.isRegistered<MemoryCache>()) {
      getIt.registerLazySingleton<MemoryCache>(
        () => MemoryCacheImpl(),
      );
    }

    // Register disk cache
    if (!getIt.isRegistered<DiskCache>()) {
      getIt.registerLazySingleton<DiskCache>(
        () => DiskCacheImpl(
          sharedPreferencesHelper: getIt.get<CommonSharedPreferencesHelper>(),
          localStorageHelper: getIt.get<CommonLocalStorageHelper>(),
        ),
      );
    }

    // Register cache manager
    if (!getIt.isRegistered<CacheManager>()) {
      getIt.registerLazySingleton<CacheManager>(
        () => CacheManagerImpl(
          memoryCache: getIt.get<MemoryCache>(),
          diskCache: getIt.get<DiskCache>(),
        ),
      );
    }
  }
}
