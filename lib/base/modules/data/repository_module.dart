import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/repository/ekyc/ekyc_repo.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';
import '../../../data/repository/authentication_repo.dart';
import '../../../data/repository/authentication_repo_impl.dart';
import '../../../data/repository/user_repo.dart';
import '../../../data/repository/user_repo_impl.dart';
import '../../../data/repository/common_repo.dart';
import '../../../data/repository/common_repo_impl.dart';
import '../../../data/repository/mock_ekyc_repo.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../prepare_for_app_initiation.dart';

/// Repository module that provides data repository dependencies.
///
/// This module handles all repository implementations for data access,
/// including authentication, user, common, and eKYC repositories.
/// It depends on API and storage modules.
class EvoRepositoryModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.repository;

  @override
  List<Type> get dependencies => [
    AuthenticationRepo,
    UserRepo,
    CommonRepo,
    EkycRepo,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register authentication repository
    if (!getIt.isRegistered<AuthenticationRepo>()) {
      getIt.registerLazySingleton<AuthenticationRepo>(
        () => AuthenticationRepoImpl(
          evoHttpClient: getIt.get<CommonHttpClient>(),
          nonAuthenticationEvoHttpClient: getIt.get<CommonHttpClient>(
            instanceName: nonAuthenticationHttpClientInstance,
          ),
          evoLocalStorageHelper: getIt.get<EvoLocalStorageHelper>(),
          appState: getIt.get<AppState>(),
        ),
      );
    }

    // Register user repository
    if (!getIt.isRegistered<UserRepo>()) {
      getIt.registerLazySingleton<UserRepo>(
        () => UserRepoImpl(
          getIt.get<CommonHttpClient>(),
          getIt.get<EvoLocalStorageHelper>(),
          getIt.get<AppState>(),
        ),
      );
    }

    // Register common repository
    if (!getIt.isRegistered<CommonRepo>()) {
      getIt.registerLazySingleton<CommonRepo>(
        () => CommonRepoImpl(getIt.get<CommonHttpClient>()),
      );
    }

    // Register mock eKYC repository (TODO: replace with real implementation)
    if (!getIt.isRegistered<EkycRepo>()) {
      getIt.registerSingleton<EkycRepo>(
        MockEkycRepoImpl(getIt.get<CommonHttpClient>()),
      );
    }
  }
}
