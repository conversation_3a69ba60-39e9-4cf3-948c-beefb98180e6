import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/dio_http_client_impl.dart';
import 'package:flutter_common_package/data/http_client/dio_log_interceptor.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo_impl.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';
import '../../../util/interceptor/log_event_interceptor.dart';
import '../../../util/interceptor/unauthorized_interceptor.dart';
import '../../../data/repository/authentication_repo.dart';
import '../../../data/repository/authentication_repo_impl.dart';
import '../../../data/response/reset_pin_entity.dart';
import '../../../util/token_utils/jwt_helper.dart';
import '../../../feature/authorization_session_expired/authorization_session_expired.dart';
import '../../../prepare_for_app_initiation.dart';

/// API module that provides HTTP client and API communication dependencies.
///
/// This module extends the common package network module with app-specific
/// API configurations, interceptors, and error handling.
class EvoApiModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.api;

  @override
  List<Type> get dependencies => [
    LogEventInterceptor,
    UnauthorizedInterceptor,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register log event interceptor
    if (!getIt.isRegistered<LogEventInterceptor>()) {
      getIt.registerLazySingleton<LogEventInterceptor>(
        () => LogEventInterceptor(eventLogPath: LoggingRepoImpl.logUrl),
      );
    }

    // Register unauthorized interceptor
    if (!getIt.isRegistered<UnauthorizedInterceptor>()) {
      getIt.registerLazySingleton<UnauthorizedInterceptor>(
        () => UnauthorizedInterceptor(
          getIt.get<AuthorizationSessionExpiredHandler>(),
          getIt.get<AuthenticationRepo>(),
          getIt.get<AppState>(),
          getIt.get<JwtHelper>(),
          ignoredRefreshTokenApiPath: <String>[
            AuthenticationRepoImpl.signInUrl,
          ],
          ignoredVerdictEmitUnauthorized: <String>[
            ResetPinEntity.verdictExpiredResetPinSession,
            ResetPinEntity.verdictInvalidResetPinSession,
            ResetPinEntity.verdictMissingResetPinSession,
          ],
        ),
      );
    }

    // Set up additional Dio interceptors for app-specific APIs
    await _setupAppSpecificInterceptors(getIt);

    // Register non-authentication HTTP client
    await _registerNonAuthenticationHttpClient(getIt);
  }

  /// Set up app-specific Dio interceptors
  Future<void> _setupAppSpecificInterceptors(GetIt getIt) async {
    final Dio dio = getIt.get<Dio>();
    
    // Clear existing interceptors to avoid duplicates
    dio.interceptors.clear();
    
    // Add app-specific interceptors in order
    dio.interceptors.addAll([
      if (kDebugMode)
        DioLogInterceptor(
          responseHeaderKeyLogged: <String>[CommonHttpClient.xRequestIdHeader],
        ),
      getIt.get<LogEventInterceptor>(),
      getIt.get<UnauthorizedInterceptor>(),
    ]);
  }

  /// Register non-authentication HTTP client for refresh token operations
  Future<void> _registerNonAuthenticationHttpClient(GetIt getIt) async {
    if (getIt.isRegistered<Dio>(instanceName: nonAuthenticationHttpClientInstance)) {
      return; // Already registered
    }

    /// We need a separate Dio to handle refresh token which will not apply [UnauthorizedInterceptor].
    /// Use can use it to make api call which don't need authentication (don't put access token to header)
    final Dio nonAuthenticationDio = Dio();

    if (kDebugMode) {
      nonAuthenticationDio.interceptors.add(
        DioLogInterceptor(
          responseHeaderKeyLogged: <String>[CommonHttpClient.xRequestIdHeader],
        ),
      );
    }

    getIt.registerLazySingleton<Dio>(
      () => nonAuthenticationDio,
      instanceName: nonAuthenticationHttpClientInstance,
    );

    getIt.registerLazySingleton<CommonHttpClient>(
      () => DioClientImpl(
        getIt.get<Dio>(
          instanceName: nonAuthenticationHttpClientInstance,
        ),
      ),
      instanceName: nonAuthenticationHttpClientInstance,
    );
  }
}
