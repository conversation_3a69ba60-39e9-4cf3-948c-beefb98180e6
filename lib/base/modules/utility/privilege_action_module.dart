import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';
import '../../feature/privilege_action/privilege_access_guard_module.dart';
import '../../feature/privilege_action/privilege_action_handler/privilege_action_factory.dart';
import '../../feature/privilege_action/verify_biometric_privilege_action/verify_biometric_privilege_action.dart';
import '../../feature/biometric/utils/biometrics_authenticate.dart';
import '../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../util/token_utils/jwt_helper.dart';
import '../../util/functions.dart';

/// Privilege Action module that provides secure action verification dependencies.
///
/// This module handles privilege actions that require additional authentication
/// such as biometric or PIN verification for sensitive operations.
class EvoPrivilegeActionModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.privilegeAction;

  @override
  List<Type> get dependencies => [
    VerifyBiometricForPrivilegeAction,
    PrivilegeAccessGuardModule,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register verify biometric for privilege action (factory)
    if (!getIt.isRegistered<VerifyBiometricForPrivilegeAction>()) {
      getIt.registerFactory(() => VerifyBiometricForPrivilegeAction(
        biometricsAuthenticate: getIt.get<BiometricsAuthenticate>(),
        secureStorageHelper: getIt.get<EvoLocalStorageHelper>(),
        jwtHelper: getIt.get<JwtHelper>(),
        evoUtilFunction: getIt.get<EvoUtilFunction>(),
      ));
    }

    // Register privilege access guard module (factory)
    if (!getIt.isRegistered<PrivilegeAccessGuardModule>()) {
      getIt.registerFactory<PrivilegeAccessGuardModule>(
        () => PrivilegeAccessGuardModule(
          biometricForPrivilegeAction: getIt.get<VerifyBiometricForPrivilegeAction>(),
          privilegeActionFactory: PrivilegeActionHandlerFactory(),
        ),
      );
    }
  }
}
