import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';
import '../../../util/performance/lazy_loader.dart';
import '../../../util/performance/lazy_loader_impl.dart';
import '../../../util/performance/memory_monitor.dart';
import '../../../util/performance/memory_monitor_impl.dart';
import '../../../util/performance/startup_timer.dart';
import '../../../util/performance/startup_timer_impl.dart';

/// Performance module that provides performance optimization dependencies.
///
/// This module handles lazy loading, memory monitoring, and startup time tracking.
/// It helps optimize app performance and provides metrics for monitoring.
class EvoPerformanceModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.performance;

  @override
  List<Type> get dependencies => [
    <PERSON><PERSON><PERSON>oa<PERSON>,
    MemoryMonitor,
    StartupTimer,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register startup timer
    if (!getIt.isRegistered<StartupTimer>()) {
      getIt.registerLazySingleton<StartupTimer>(() => StartupTimerImpl());
    }

    // Register memory monitor
    if (!getIt.isRegistered<MemoryMonitor>()) {
      getIt.registerLazySingleton<MemoryMonitor>(() => MemoryMonitorImpl());
    }

    // Register lazy loader
    if (!getIt.isRegistered<LazyLoader>()) {
      getIt.registerLazySingleton<LazyLoader>(
        () => LazyLoaderImpl(
          startupTimer: getIt.get<StartupTimer>(),
          memoryMonitor: getIt.get<MemoryMonitor>(),
        ),
      );
    }
  }
}
