import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';
import '../../util/validator/evo_validator.dart';
import '../../util/validator/mpin_validator.dart';

/// Validation module that provides validation utilities.
///
/// This module handles form validation, input validation, and business rule validation.
class EvoValidationModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.validation;

  @override
  List<Type> get dependencies => [
    EvoValidator,
    MpinValidator,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register EVO validator
    if (!getIt.isRegistered<EvoValidator>()) {
      getIt.registerLazySingleton<EvoValidator>(() => EvoValidator());
    }

    // Register MPIN validator
    if (!getIt.isRegistered<MpinValidator>()) {
      getIt.registerLazySingleton<MpinValidator>(() => MpinValidator());
    }
  }
}
