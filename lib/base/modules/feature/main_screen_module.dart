import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';
import '../../../feature/main_screen/main_screen_bloc.dart';
import '../../../feature/main_screen/card_page/cubit/card_page_cubit.dart';
import '../../../feature/main_screen/home_page/cubit/home_page_cubit.dart';
import '../../../feature/main_screen/usage_page/cubit/usage_page_cubit.dart';
import '../../../feature/main_screen/payment_summary/payment_summary_cubit.dart';
import '../../../data/repository/user_repo.dart';
import '../../../data/repository/common_repo.dart';

/// Main screen module that provides main screen functionality dependencies.
///
/// This module handles the main screen, card page, home page, usage page, and payment summary.
/// It depends on user, common repositories and other core modules.
class EvoMainScreenModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.mainScreen;

  @override
  List<Type> get dependencies => [
    MainScreenBloc,
    CardPageCubit,
    HomePageCubit,
    UsagePageCubit,
    PaymentSummaryCubit,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register main screen bloc (factory for multiple instances)
    if (!getIt.isRegistered<MainScreenBloc>()) {
      getIt.registerFactory<MainScreenBloc>(
        () => MainScreenBloc(
          userRepo: getIt.get<UserRepo>(),
        ),
      );
    }

    // Register card page cubit (factory for multiple instances)
    if (!getIt.isRegistered<CardPageCubit>()) {
      getIt.registerFactory<CardPageCubit>(
        () => CardPageCubit(
          userRepo: getIt.get<UserRepo>(),
          commonRepo: getIt.get<CommonRepo>(),
        ),
      );
    }

    // Register home page cubit (factory for multiple instances)
    if (!getIt.isRegistered<HomePageCubit>()) {
      getIt.registerFactory<HomePageCubit>(
        () => HomePageCubit(
          userRepo: getIt.get<UserRepo>(),
          commonRepo: getIt.get<CommonRepo>(),
        ),
      );
    }

    // Register usage page cubit (factory for multiple instances)
    if (!getIt.isRegistered<UsagePageCubit>()) {
      getIt.registerFactory<UsagePageCubit>(
        () => UsagePageCubit(
          userRepo: getIt.get<UserRepo>(),
          commonRepo: getIt.get<CommonRepo>(),
        ),
      );
    }

    // Register payment summary cubit (factory for multiple instances)
    if (!getIt.isRegistered<PaymentSummaryCubit>()) {
      getIt.registerFactory<PaymentSummaryCubit>(
        () => PaymentSummaryCubit(
          userRepo: getIt.get<UserRepo>(),
          commonRepo: getIt.get<CommonRepo>(),
        ),
      );
    }
  }
}
