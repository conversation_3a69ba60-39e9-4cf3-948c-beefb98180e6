import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/data/repository/ekyc/ekyc_repo.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';
import '../../../feature/ekyc/ekyc_intro/cubit/ekyc_intro_cubit.dart';
import '../../../feature/ekyc/ekyc_selfie/cubit/ekyc_selfie_cubit.dart';
import '../../../feature/ekyc/utils/ekyc_utils.dart';
import '../../../feature/ekyc/utils/camera_permission_handler.dart';
import '../../../feature/ekyc/utils/camera_permission_handler_impl.dart';
import '../../../util/functions.dart';

/// eKYC module that provides eKYC functionality dependencies.
///
/// This module handles eKYC introduction, selfie capture, document verification,
/// and camera permission management. It depends on eKYC repository and utility modules.
class EvoEkycModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.ekyc;

  @override
  List<Type> get dependencies => [
    EkycIntroCubit,
    EkycSelfieCubit,
    EkycUtils,
    CameraPermissionHandler,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register eKYC utils
    if (!getIt.isRegistered<EkycUtils>()) {
      getIt.registerLazySingleton<EkycUtils>(
        () => EkycUtils(
          ekycRepo: getIt.get<EkycRepo>(),
          evoUtilFunction: getIt.get<EvoUtilFunction>(),
        ),
      );
    }

    // Register camera permission handler (factory for multiple instances)
    if (!getIt.isRegistered<CameraPermissionHandler>()) {
      getIt.registerFactory<CameraPermissionHandler>(
        () => CameraPermissionHandlerImpl(),
      );
    }

    // Register eKYC intro cubit (factory for multiple instances)
    if (!getIt.isRegistered<EkycIntroCubit>()) {
      getIt.registerFactory<EkycIntroCubit>(
        () => EkycIntroCubit(
          ekycRepo: getIt.get<EkycRepo>(),
          ekycUtils: getIt.get<EkycUtils>(),
        ),
      );
    }

    // Register eKYC selfie cubit (factory for multiple instances)
    if (!getIt.isRegistered<EkycSelfieCubit>()) {
      getIt.registerFactory<EkycSelfieCubit>(
        () => EkycSelfieCubit(
          ekycRepo: getIt.get<EkycRepo>(),
          ekycUtils: getIt.get<EkycUtils>(),
          cameraPermissionHandler: getIt.get<CameraPermissionHandler>(),
        ),
      );
    }
  }
}
