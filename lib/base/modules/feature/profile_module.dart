import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';
import '../../../feature/profile/cubit/profile_screen_cubit.dart';
import '../../../feature/profile/utils/profile_utils.dart';
import '../../../data/repository/user_repo.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../util/functions.dart';

/// Profile module that provides user profile management dependencies.
///
/// This module handles user profile display, editing, and profile-related utilities.
/// It depends on user repository and storage modules.
class EvoProfileModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.profile;

  @override
  List<Type> get dependencies => [
    ProfileScreenCubit,
    ProfileUtils,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register profile utils
    if (!getIt.isRegistered<ProfileUtils>()) {
      getIt.registerLazySingleton<ProfileUtils>(
        () => ProfileUtils(
          userRepo: getIt.get<UserRepo>(),
          localStorageHelper: getIt.get<EvoLocalStorageHelper>(),
          evoUtilFunction: getIt.get<EvoUtilFunction>(),
        ),
      );
    }

    // Register profile screen cubit (factory for multiple instances)
    if (!getIt.isRegistered<ProfileScreenCubit>()) {
      getIt.registerFactory<ProfileScreenCubit>(
        () => ProfileScreenCubit(
          userRepo: getIt.get<UserRepo>(),
          profileUtils: getIt.get<ProfileUtils>(),
          localStorageHelper: getIt.get<EvoLocalStorageHelper>(),
        ),
      );
    }
  }
}
