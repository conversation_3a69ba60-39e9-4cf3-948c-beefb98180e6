import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';
import '../../../feature/verify_otp/cubit/verify_otp_cubit.dart';
import '../../../feature/verify_otp/utils/otp_utils.dart';
import '../../../data/repository/authentication_repo.dart';
import '../../../util/functions.dart';

/// Verify OTP module that provides OTP verification functionality dependencies.
///
/// This module handles OTP verification, resend OTP, and OTP-related utilities.
/// It depends on authentication repository and utility modules.
class EvoVerifyOtpModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.verifyOtp;

  @override
  List<Type> get dependencies => [
    VerifyOtpCubit,
    OtpUtils,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register OTP utils
    if (!getIt.isRegistered<OtpUtils>()) {
      getIt.registerLazySingleton<OtpUtils>(
        () => OtpUtils(
          authRepo: getIt.get<AuthenticationRepo>(),
          evoUtilFunction: getIt.get<EvoUtilFunction>(),
        ),
      );
    }

    // Register verify OTP cubit (factory for multiple instances)
    if (!getIt.isRegistered<VerifyOtpCubit>()) {
      getIt.registerFactory<VerifyOtpCubit>(
        () => VerifyOtpCubit(
          authRepo: getIt.get<AuthenticationRepo>(),
          otpUtils: getIt.get<OtpUtils>(),
        ),
      );
    }
  }
}
