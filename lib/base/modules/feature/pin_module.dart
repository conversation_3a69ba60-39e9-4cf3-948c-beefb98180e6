import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';
import '../../feature/pin/reset_pin/reset_pin_handler.dart';
import '../../feature/pin/reset_pin/reset_pin_handler_impl.dart';
import '../../feature/pin/reset_pin/reset_pin_ui_handler.dart';
import '../../data/repository/authentication_repo.dart';

/// PIN module that provides PIN management dependencies.
///
/// This module handles PIN creation, validation, reset, and change operations.
/// It depends on authentication and validation modules.
class EvoPinModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.pin;

  @override
  List<Type> get dependencies => [
    ResetPinHandler,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register reset PIN handler (factory for multiple instances)
    if (!getIt.isRegistered<ResetPinHandler>()) {
      getIt.registerFactory<ResetPinHandler>(
        () => ResetPinHandlerImpl(
          resetPinUiHandler: ResetPinUiHandler(),
          authRepo: getIt.get<AuthenticationRepo>(),
        ),
      );
    }
  }
}
