import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/util/device_info_plugin_wrapper/device_info_plugin_wrapper.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:get_it/get_it.dart';
import 'package:local_auth/local_auth.dart';
import 'package:ts_bio_detect_changed/ts_bio_detect_changed.dart';

import '../module_names.dart';
import '../../../feature/biometric/biometric_token_module/biometrics_token_module.dart';
import '../../../feature/biometric/request_user_active_biometric/request_user_active_biometric_handler.dart';
import '../../../feature/biometric/request_user_active_biometric/request_user_active_biometric_handler_impl.dart';
import '../../../feature/biometric/request_user_active_biometric/request_user_active_biometric_util.dart';
import '../../../feature/biometric/utils/biometric_functions.dart';
import '../../../feature/biometric/utils/biometric_status_helper.dart';
import '../../../feature/biometric/utils/biometric_status_helper_impl.dart';
import '../../../feature/biometric/utils/biometric_type_helper.dart';
import '../../../feature/biometric/utils/biometrics_authenticate.dart';
import '../../../feature/biometric/utils/biometrics_authenticate_impl.dart';
import '../../../data/repository/user_repo.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../util/token_utils/jwt_helper.dart';

/// Biometric module that provides biometric authentication dependencies.
///
/// This module handles biometric authentication, token management, and device biometric capabilities.
/// It depends on auth and device info modules.
class EvoBiometricModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.biometric;

  @override
  List<Type> get dependencies => [
    BiometricsAuthenticate,
    TsBioDetectChanged,
    BiometricsTokenModule,
    BiometricStatusHelper,
    BiometricFunctions,
    BiometricTypeHelper,
    RequestUserActiveBiometricUtil,
    RequestUserActivateBiometricHandler,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register biometric authenticate
    if (!getIt.isRegistered<BiometricsAuthenticate>()) {
      final BiometricsAuthenticate biometricAuthenticate = BiometricAuthenticateImpl(
        localAuth: LocalAuthentication(),
        isForceStrongBiometric: true,
      );
      getIt.registerLazySingleton<BiometricsAuthenticate>(() => biometricAuthenticate);
    }

    // Register bio detect changed
    if (!getIt.isRegistered<TsBioDetectChanged>()) {
      getIt.registerLazySingleton<TsBioDetectChanged>(() => TsBioDetectChanged());
    }

    // Register biometric token module (factory for multiple instances)
    if (!getIt.isRegistered<BiometricsTokenModule>()) {
      getIt.registerFactory<BiometricsTokenModule>(
        () => BiometricsTokenModule(
          biometricsAuthenticate: getIt.get<BiometricsAuthenticate>(),
          userRepo: getIt.get<UserRepo>(),
          secureStorageHelper: getIt.get<EvoLocalStorageHelper>(),
          bioDetectChanged: getIt.get<TsBioDetectChanged>(),
          jwtHelper: getIt.get<JwtHelper>(),
        ),
      );
    }

    // Register biometric status helper
    if (!getIt.isRegistered<BiometricStatusHelper>()) {
      getIt.registerLazySingleton<BiometricStatusHelper>(() => BiometricStatusHelperImpl());
    }

    // Register biometric functions
    if (!getIt.isRegistered<BiometricFunctions>()) {
      getIt.registerLazySingleton<BiometricFunctions>(() => BiometricFunctions());
    }

    // Register biometric type helper
    if (!getIt.isRegistered<BiometricTypeHelper>()) {
      getIt.registerLazySingleton<BiometricTypeHelper>(
        () => BiometricTypeHelper(
          getIt.get<BiometricsAuthenticate>(),
          getIt.get<DeviceInfoPluginWrapper>().deviceInfoPlugin,
          DevicePlatformImp(),
        ),
      );
    }

    // Register request user active biometric util
    if (!getIt.isRegistered<RequestUserActiveBiometricUtil>()) {
      getIt.registerLazySingleton<RequestUserActiveBiometricUtil>(
        () => RequestUserActiveBiometricUtil(
          localStorageHelper: getIt.get<EvoLocalStorageHelper>(),
        ),
      );
    }

    // Register request user activate biometric handler (factory)
    if (!getIt.isRegistered<RequestUserActivateBiometricHandler>()) {
      getIt.registerFactory<RequestUserActivateBiometricHandler>(
        () => RequestUserActiveBiometricHandlerImp(
          requestUserActiveBiometricUtil: getIt.get<RequestUserActiveBiometricUtil>(),
          biometricsAuthenticate: getIt.get<BiometricsAuthenticate>(),
        ),
      );
    }
  }
}
