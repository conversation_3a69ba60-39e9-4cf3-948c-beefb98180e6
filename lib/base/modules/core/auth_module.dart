import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';
import '../../data/repository/authentication_repo.dart';
import '../../data/repository/authentication_repo_impl.dart';
import '../../data/repository/user_repo.dart';
import '../../data/repository/user_repo_impl.dart';
import '../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../util/secure_storage_helper/secure_storage_helper_impl.dart';
import '../../util/token_utils/jwt_helper.dart';
import '../../util/token_utils/mock_jwt_helper.dart';
import '../../prepare_for_app_initiation.dart';

/// Authentication module that provides authentication-related dependencies.
///
/// This module handles user authentication, token management, and JWT operations.
/// It depends on storage and network modules from the common package.
class EvoAuthModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.auth;

  @override
  List<Type> get dependencies => [
    AuthenticationRepo,
    UserRepo,
    JwtHelper,
    EvoLocalStorageHelper,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register JWT helper
    if (!getIt.isRegistered<JwtHelper>()) {
      // TODO: switch to EvoJwtHelperImpl after Demo phase
      getIt.registerLazySingleton<JwtHelper>(() => MockJwtHelper());
    }

    // Register local storage helper
    if (!getIt.isRegistered<EvoLocalStorageHelper>()) {
      getIt.registerLazySingleton<EvoLocalStorageHelper>(
        () => EvoSecureStorageHelperImpl(
          secureStorage: getIt.get(),
        ),
      );
    }

    // Register authentication repository
    if (!getIt.isRegistered<AuthenticationRepo>()) {
      getIt.registerLazySingleton<AuthenticationRepo>(
        () => AuthenticationRepoImpl(
          evoHttpClient: getIt.get<CommonHttpClient>(),
          nonAuthenticationEvoHttpClient: getIt.get<CommonHttpClient>(
            instanceName: nonAuthenticationHttpClientInstance,
          ),
          evoLocalStorageHelper: getIt.get<EvoLocalStorageHelper>(),
          appState: getIt.get<AppState>(),
        ),
      );
    }

    // Register user repository
    if (!getIt.isRegistered<UserRepo>()) {
      getIt.registerLazySingleton<UserRepo>(
        () => UserRepoImpl(
          getIt.get<CommonHttpClient>(),
          getIt.get<EvoLocalStorageHelper>(),
          getIt.get<AppState>(),
        ),
      );
    }
  }
}
