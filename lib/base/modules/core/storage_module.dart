import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../util/secure_storage_helper/secure_storage_helper_impl.dart';

/// Storage module that provides storage-related dependencies.
///
/// This module handles secure storage, shared preferences, and local storage operations.
/// It extends the common package storage capabilities with app-specific storage needs.
class EvoStorageModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.storage;

  @override
  List<Type> get dependencies => [
    EvoLocalStorageHelper,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register local storage helper
    if (!getIt.isRegistered<EvoLocalStorageHelper>()) {
      getIt.registerLazySingleton<EvoLocalStorageHelper>(
        () => EvoSecureStorageHelperImpl(
          secureStorage: getIt.get(),
        ),
      );
    }

    // Additional storage utilities can be registered here
    // For example: cache helpers, file storage, etc.
  }
}
