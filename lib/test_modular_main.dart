// Test file for modular system without Firebase dependencies
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';

import 'app_initialization_modular.dart' as modular;
import 'feature/inactive_detector/inactive_detector.dart';
import 'prepare_for_app_initiation.dart';
import 'resources/dimensions.dart';
import 'resources/evo_theme.dart';
import 'util/navigator/evo_router.dart';
import 'util/screen_util.dart';
import 'widget/hud_loading/hud_loading.dart';

Future<void> main() async {
  final WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);

  // Feature flag to switch between old and new modular system
  const bool useModularSystem = bool.fromEnvironment('USE_MODULAR', defaultValue: true);

  if (useModularSystem) {
    if (kDebugMode) {
      debugPrint('🔧 Using MODULAR initialization system');
    }
    await modular.initializeEvoApplication(
      locale: const Locale('vi'),
      isProduction: false, // test environment
    );
  } else {
    if (kDebugMode) {
      debugPrint('🔧 Using LEGACY initialization system');
    }
    await prepareForAppInitiation();
  }

  // Lock screen orientation to portrait
  SystemChrome.setPreferredOrientations(<DeviceOrientation>[DeviceOrientation.portraitUp])
      .then((_) {
    runApp(const TestEvoModularApp());
  });
}

class TestEvoModularApp extends StatelessWidget {
  const TestEvoModularApp({super.key});

  @override
  Widget build(BuildContext context) {
    FlutterNativeSplash.remove();
    ScreenUtil().init(
      context: context,
      design: const Size(
        EvoDimension.figmaScreenWidth,
        EvoDimension.figmaScreenHeight,
      ),
    );
    return KeyboardDismissOnTap(
      child: InactiveDetector(
        detectorController: getIt.get<AppState>().inactiveDetectorController,
        child: getMaterialApp(context),
      ),
    );
  }

  TransitionBuilder getAppBuilder() {
    return HudLoading.instance.init();
  }

  MaterialApp getMaterialApp(BuildContext context) => MaterialApp.router(
        title: 'EVO Test Modular',
        scaffoldMessengerKey: globalKeyProvider.scaffoldMessengerKey,
        theme: EvoTheme.themeData,
        localizationsDelegates: const <LocalizationsDelegate<dynamic>>[
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate
        ],
        routerConfig: evoRouter,
        builder: getAppBuilder(),
      );
}
